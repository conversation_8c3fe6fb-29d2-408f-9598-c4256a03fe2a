import PluginApi from "../../pluginbase-ava/package/PluginApi";
import {i18n, request, uuid} from "../../pluginbase-ava/package/pluginutils";
import log from "../../pluginbase-ava/package/log";

export default class DataSource {

    constructor(pluginService, pluginParam) {
        log.tickPluginUsed(pluginParam && pluginParam.describe);
        this.pluginParam = pluginParam;
        this.request = pluginService.api.request
        this.pluginApi = new PluginApi(pluginService);
        this.details = pluginParam && pluginParam.params && pluginParam.params.details;
    }

    mdRenderBefore(pluginExecResult, options) {
        let self = this;
        let preData = pluginExecResult && pluginExecResult.preData;
        let preHandleNormalButtons = preData && preData.handleNormalButtons || [];
        let {objApiName, dataGetter} = options;
        return Object.assign({}, preData, {
            handleNormalButtons: [...preHandleNormalButtons, (opt) => {
                opt = opt || {};
                opt.buttons = opt.buttons || [];
                let fields = dataGetter.getLayoutFields && dataGetter.getLayoutFields(objApiName, opt && opt.recordType);
                let lookupFields = fields && Object.values(fields).filter(it => it.type === 'object_reference');
                let detailConfig = self.details && self.details.find(i => i.objectApiName === objApiName)
                let orderProductSource = detailConfig && detailConfig.customParam && detailConfig.customParam.orderProductSource;
                let dataSourceFields = orderProductSource && lookupFields && lookupFields.filter(it => {
                    return orderProductSource.find(source => {
                        return source && (it.target_api_name === source.sourceMdApiName);
                    });
                })
                dataSourceFields && dataSourceFields.length && dataSourceFields.forEach(dataSourceField => {
                    let {api_name, label} = dataSourceField;
                    let find = opt.buttons.find(it => it.lookup_field_name === dataSourceField.api_name);
                    if (!find) {
                        opt.buttons.push({
                            lookup_field_name: api_name,
                            label: i18n('ava.object_form.add_from_param', [label]/*从${label}添加*/),
                            action: "Batch_Lookup_Add",
                            api_name: `Batch_Lookup_Add_button_${api_name}`
                        })
                    }
                });
                return opt;
            }]
        })
    }

    mdBatchAddAfter(pluginExecResult, options) {
        let preData = pluginExecResult && pluginExecResult.preData;
        let {masterObjApiName, objApiName, lookupDatas, lookupField, newDatas, seriesId, dataGetter} = options || {};
        let detailConfig = this.details && this.details.find(i => i.objectApiName === objApiName)
        let orderProductSource = detailConfig && detailConfig.customParam && detailConfig.customParam.orderProductSource;
        let dataSourceField = orderProductSource && orderProductSource.find(it => {
            return it.seleceDataBtn === lookupField.api_name;
        });
        if (!dataSourceField) {//不是数据源字段不处理
            return preData;
        }
        let {sourceMdApiName, sourceMasterApiName, mappingRule} = dataSourceField || {};
        if (!lookupDatas || !lookupDatas.length || !sourceMdApiName) {
            return preData;
        }
        let pageId = dataGetter.getPageId();
        let token = 'data_source_' + uuid();
        this.pluginApi.showSingletonLoading(token, {}, pageId);
        return this.mapping({[sourceMdApiName]: lookupDatas}, sourceMasterApiName, masterObjApiName, mappingRule, seriesId)
            .then(res => {
                this.pluginApi.hideSingletonLoading(token, pageId);
                let {details, fields} = res || {};
                let detailList = details && details[objApiName]
                newDatas && newDatas.forEach((newData, index) => {
                    let detailData = detailList && detailList[index];
                    Object.assign(newData, detailData);
                });
                let preObjectFilterFields = preData
                    && preData.extraCalUiParams
                    && preData.extraCalUiParams.filterFields
                    && preData.extraCalUiParams.filterFields[objApiName];
                let currentObjectFilterFields = fields && fields[objApiName];
                return Object.assign({}, preData, {
                    extraCalUiParams: Object.assign({}, preData && preData.extraCalUiParams, {
                        filterFields: Object.assign({}, preData && preData.extraCalUiParams && preData.extraCalUiParams.filterFields,
                            {[objApiName]: [...(preObjectFilterFields || []), ...(currentObjectFilterFields || [])]})
                    })
                })
            }).catch(err => {
                this.pluginApi.hideSingletonLoading(token, pageId);
                this.pluginApi.showToast(err);
                return preData;
            })
    }

    mapping(detailDataMap, sourceApiName, targetApiName, ruleName = null, seriesId) {
        return request(this.request, {
            url: `FHE/EM1ANCRM/API/v1/object/data_mapping/service/mapping`,
            data: {
                ruleName,
                sourceApiName,
                targetApiName,
                detailDataMap,
                seriesId
            }
        })
    }

    apply() {
        return [{
            event: "md.render.before",
            functional: this.mdRenderBefore.bind(this)
        }, {
            event: "md.batchAdd.after",
            functional: this.mdBatchAddAfter.bind(this)
        }]
    }
}