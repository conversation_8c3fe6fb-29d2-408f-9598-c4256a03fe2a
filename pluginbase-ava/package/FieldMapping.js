/**
 * 获取字段映射
 */
export default class FieldMapping {

    /**
     * @param serverMapping 后台下发的字段映射
     * @param defFieldMapping 默认的字段映射
     */
    constructor(serverMapping, defFieldMapping) {
        let {fieldMapping, details} = serverMapping || {};
        this.defFieldMapping = defFieldMapping;
        this.masterFieldMap = fieldMapping;
        this.detailFieldMap = {};
        details && details.length && details.forEach(detail => {
            let {objectApiName, fieldMapping} = detail;
            if (objectApiName) {
                this.detailFieldMap[objectApiName] = fieldMapping;
            }
        })
    }

    getMasterFields() {
        let {masterFields} = this.defFieldMapping || {};
        return Object.assign({}, masterFields, this.masterFieldMap)
    }

    getDetailFields(objectApiName) {
        let {detailFields: defDetailFields} = this.defFieldMapping || {};
        return Object.assign({}, defDetailFields, this.detailFieldMap[objectApiName])
    }

    getDetailObjApiNames() {
        return this.detailFieldMap && Object.keys(this.detailFieldMap);
    }

    getFirstDetailObjApiName() {
        let objApiNames = this.getDetailObjApiNames();
        return objApiNames && objApiNames.length && objApiNames[0];
    }
}