/**
 * @Description: 切换客户、价目表、合作伙伴 不清空明细逻辑；
 * <AUTHOR>
 * @date 2022/1/14
 */

import PPM from 'plugin_public_methods'

export default class NoClearMd {

    constructor(pluginService, pluginParam, parent) {
        this.parent = parent;
        this.pluginService = pluginService;
    }

    getAllFields(mdApiname) {
        return this.parent.getAllFields(mdApiname);
    }

    getMasterFields() {
        return this.parent.getMasterFields();
    }

    getConfig(key){
       return this.parent.getConfig(key);
    }

    // 开了价目表且没开价格政策，切换客户 合作伙伴 日期不清空从对象；
    noClearMd(plugin) {
        let allPlugins = plugin.api.getPlugins() || [];
        let hasAdvancePlugin = allPlugins.find(item => item.pluginApiName === 'price_policy');
        return this.getConfig('openPriceList') && !hasAdvancePlugin;
    }

    _beforeRender(plugin, param) {
        const masterDesc = param.dataGetter.getDescribe() || {};
        this.masterPriceBookDesc = masterDesc.fields?.price_book_id;
    }

    // 切换主对象客户 价目表 合作伙伴 日期 业务逻辑；
    _dataChange_after(plugin, param) {
        let field = this.findChangeField({changeData: param.changeData, masterApiName: param.masterObjApiName}, param);
        return new Promise(async resolve => {
            if (field) {
                this.parent.pluginService.api.showLoading();
                await this.todoAfterFieldChange(field, param, plugin, resolve);
                this.parent.pluginService.api.hideLoading();
            }else{
                resolve()
            }
        })
    }

    async checkPriceBook({
        key = '',
        masterData = {},
        allOldData = {}
    } = {}, param){
        let {form_price_book_id, form_account_id, form_partner_id} = this.getMasterFields();
        let priceBookId = key === form_account_id ? (allOldData[form_price_book_id] || masterData[form_price_book_id]) : masterData[form_price_book_id];
        masterData[form_price_book_id] = priceBookId;
        let res = await this.validPriceBookByCustomer({
            price_book_id: priceBookId,
            account_id: masterData[form_account_id] || '',
            partner_id: masterData[form_partner_id] || '',
            object_data: masterData,
            details: PPM.parsePriceBookDataRangeDetails(param.dataGetter.getDetails(), this.masterPriceBookDesc)
        });
        return res.result;
    }

    // 缓存本地的价目表id
    setLocalPriceBook(masterData){
        let {form_price_book_id} = this.parent.getMasterFields();
        if(masterData[form_price_book_id]) this.pluginService.api.setLocal('price_book_id_order_form', masterData[form_price_book_id]);
    }

    

    // 修改客户，清空价目表。只处理自定义对象。预制对象已经刷了父子级依赖，会自动处理；
    clearPriceBookId(param, field = '', masterApiName = ''){
        let {form_price_book_id, form_account_id} = this.getMasterFields();
        if(masterApiName.includes('__c') && field === form_account_id){
            param.dataUpdater.updateMaster({
                [form_price_book_id]: '',
                [form_price_book_id + '__r']: ''
            })
        }
    }

    // 校验从对象是否有数据
    checkMdHasData(param){
        let mdApinameList = this.parent.getAllMdApiName();
        let len = mdApinameList.length;
        while (len--) {
            let mdApiName = mdApinameList[len];
            let r = param.dataGetter.getDetail(mdApiName);
            if(r && r.length) return true;
        }
        return false;
    }

    // 重置主对象数据
    resetMasterPriceBook({key = '', oldValue = '', oldData = '', priceBookId = '', priceBookId__r = ''} = {}, param){
        const {form_price_book_id, form_account_id, form_partner_id} = this.getMasterFields();
        const SpecialFields = [form_price_book_id, form_account_id, form_partner_id];
        let obj = SpecialFields.includes(key) ? {
            [key + '__r']: oldValue,
            [key]: oldData,
        } : {
            [key]: oldData,
        };
        if (key === form_account_id) {
            obj[form_price_book_id] = priceBookId;
            obj[form_price_book_id + '__r'] = priceBookId__r;
        }
        param.dataUpdater.updateMaster(obj);
    }

    // 清空所有从对象数据并计算
    async clearMd(param){
        let mdApinameList = this.parent.getAllMdApiName();
        let len = mdApinameList.length;
        while (len--) {
            let mdApiName = mdApinameList[len];
            await param.dataUpdater.delDetailAndTrigger(mdApiName);
        }
    }

    /**
     * @desc 如果没有值，不处理，如果是客户字段且没有值，还原被清掉的价目表;
     * @param value
     * @param allOldData
     * @returns {boolean}
     */
    checkValue({key = '', value = '', allOldData = {}, masterData} = {}, param) {
        if (!PPM.hasValue(value)) {
            let {form_price_book_id, form_account_id} = this.getAllFields();
            if (key === form_account_id && allOldData[form_price_book_id]) {
                param.dataUpdater.updateMaster({
                    [form_price_book_id]: allOldData[form_price_book_id],
                    [form_price_book_id + '__r']: allOldData[form_price_book_id + '__r']
                })
            }
            return false;
        }
        return true;
    }

    /**
     * @desc 是否需要请求默认的价目表
     * @param key 修改字段
     * @param des 主对象描述
     * @param masterData  主对象数据
     * @param allOldData  被更改字段的上一次值
     * @param param
     * @returns {Promise<void>}
     */
    async fetchDefaultValue({
                                key = '',
                                des = {},
                                masterData = {},
                                allOldData = {}
                            } = {}, param) {

        let {form_price_book_id, form_account_id, form_partner_id} = this.getAllFields(param.objApiName);
        if (![form_account_id, form_partner_id].includes(key)) {
            return;
        }
        let priceBookId = allOldData[form_price_book_id] || masterData[form_price_book_id];
        await this.fetchDefaultPriceBook({
            priceBookId: priceBookId,
            masterData: masterData,
            des
        }, param)

    }

    /**
     * @desc 请求默认价目表；
     * @param priceBookId 主对象价目表id
     * @param masterData  主对象数据
     * @param des         主对象描述
     * @param param
     * @returns {Promise<void>}
     */
    async fetchDefaultPriceBook({
                                    priceBookId = '',
                                    masterData = {},
                                    des = {},
                                } = {}, param) {
        let {form_price_book_id, form_account_id, form_partner_id} = this.getAllFields();
        let isRequire = des[form_price_book_id] && des[form_price_book_id].is_required;
        if(!isRequire) return;
        let aid = masterData[form_account_id],
            partner = masterData[form_partner_id];
        if (param.formType === 'edit' || !this.getConfig('openPriceList') || !aid) {
            return;
        }
        let pId = priceBookId || this.pluginService.api.getLocal('price_book_id_order_form') || '';
        if(priceBookId) return;
        let res = await this.requestDefaultValue({
            price_book_id: pId,
            account_id: aid,
            partner_id: partner,
            object_data: masterData, // 主对象数据
            details: PPM.parsePriceBookDataRangeDetails(param.dataGetter.getDetails(), this.masterPriceBookDesc)
        });
        let data = res.result || {};
        if (PPM.isObject(data)) {
            param.dataUpdater.updateMaster({
                [form_price_book_id]: data._id,
                [form_price_book_id + '__r']: data.name__r || data.name
            })
        }
    }

    // 校验客户、日期 和 价目表是否匹配；
    requestDefaultValue(param) {
        let url = `FHH/EM1HNCRM/API/v1/object/pricebook/service/pickone_for_sales_order`;
        return PPM.ajax(this.parent.request, url, Object.assign({},{
            price_book_id: '',
            account_id: '',
            partner_id: '',
            object_data: {} // 主对象数据
        }, param))
    }
   // 切换主对象客户 价目表 合作伙伴 日期 业务逻辑；
    async todoAfterFieldChange(field = '', param, plugin, resolve) {
        let _this = this;
        // ui事件更新的字段，不触发业务逻辑；
        let fromUiEvent = param.autoFields || {};
        if(fromUiEvent.hasOwnProperty(field)) return resolve();

        let r1 =  this.parent.runPluginSync('price-service.todoAfterFieldChange.before', {
            param,
        });
        if(r1 && r1.isNoTriggerChange){
            return resolve();
        }

        let value = param.changeData[field];
        let masterData = param.dataGetter.getMasterData();
        // 若值为空，不触发业务
        let r = this.checkValue({
            key: field,
            value,
            allOldData: param.oldData,
            masterData
        }, param);
        let des = param.dataGetter.getDescribe(param.masterObjApiName).fields;
        this.setPriceBookReadOnly(field, masterData, param);
        if (!r) return resolve();
        // 没开价目表的不处理
        if(!this.getConfig('openPriceList')) return resolve();
        let {form_price_book_id, form_account_id, form_partner_id} = this.getMasterFields();
        // todo: 高新投三江 ui 事件不触发业务
        let EA = ['782635_sandbox', 'gxtsj2023'];
        if([form_price_book_id, form_account_id, form_partner_id].includes(field) && !param.isManual && EA.includes(CRM.ea)){
            return resolve();
        }
        let fieldName = des[field].label;
        let oldData = param.oldData[field],
            oldValue = param.oldData[field + '__r'],
            allOldData = param.oldData,
            key = field;
        let priceBookId = allOldData[form_price_book_id] || masterData[form_price_book_id];
        let priceBookId__r = allOldData[form_price_book_id + '__r'] || masterData[form_price_book_id + '__r'];
        await this.fetchDefaultValue({
            key: field,
            des,
            masterData,
            allOldData: param.oldData,
        }, param, plugin);
        this.setLocalPriceBook(masterData);
        
        
        // 修改非价目表字段
        if (key !== form_price_book_id && PPM.hasValue(value) && priceBookId && masterData[form_account_id]){
            // 校验价目表
            let r = await this.checkPriceBook({
                key: field,
                masterData,
                allOldData: param.oldData,
            }, param);
            // 校验价目表不通过，清空价目表和明细信息；
            if(!r){
                let flag = false;
                plugin.api.confirm({
                    title: this.parent.i18n("提示"),
                    msg: this.parent.i18n("crm.plugin.priceservice.checkPriceBook.tip", {
                        name: fieldName
                    }, '更换{{name}}将清空已选价目表，确认更换？'),
                    success: async () => {
                        flag = true;
                        if (masterData[form_price_book_id]) {
                            param.dataUpdater.updateMaster({
                                [form_price_book_id]: '',
                                [form_price_book_id + '__r']: ''
                            })
                        }
                        
                        // await _this.clearMd(param);
                        await _this.checkMdIsMatchCustomer({key, value, masterData}, param)
                        resolve()
                    },
                    cancel: () => {
                        if (!flag) {
                        _this.resetMasterPriceBook({
                            key,
                            oldValue,
                            oldData,
                            priceBookId,
                            priceBookId__r
                        }, param);
                            resolve()
                        }
                    }
                })
                return;
            }
            // 如果是切换客户，即使价目表校验通过，也需要再校验一下明细数据；可能有产品不在客户范围内；
            if (key === form_account_id) {
                // 还原被清掉的价目表；
                param.dataUpdater.updateMaster({
                    [form_price_book_id]: priceBookId,
                    [form_price_book_id + '__r']: priceBookId__r
                });
                await this.checkMdIsMatchCustomer({key, value, masterData}, param) // 校验明细数据是否匹配当前客户
            }
            resolve()
            
        }else{
            // 改价目表字段
            await this.checkMdIsMatchCustomer({key, value, masterData}, param); // 校验明细数据是否匹配当前客户
            resolve();
        }
        
    }
    /**
     * @desc 校验明细数据是否匹配当前客户
     * @param key 更改字段
     * @param value 更改的值
     * @param masterData 主对象数据
     * @returns {Promise<void>}
     */
    async checkMdIsMatchCustomer({key = '', value = '',  masterData = {}} = {}, param) {
        let mdApinameList = this.parent.getAllMdApiName();
        let len = mdApinameList.length;
        let _this = this;
        let {form_price_book_id} = this.getMasterFields();
        while (len--) {
            let mdApiName = mdApinameList[len];
            let data = param.dataGetter.getDetail(mdApiName) || [];
            if (!data.length || !PPM.hasValue(value)) return;
            if (data.length) {
                // 若改价目表，更新明细的价目表
                if (key === form_price_book_id && value) _this.setPriceBookId(data, value, mdApiName);
                // 重新取价，取客户的可售产品
                let result = await this.parent.getRealPriceAndCalculate({
                    data,
                    mdApiName,
                    actionFrom: '',
                    masterApiName: param.masterObjApiName,
                    masterData,
                    noChildrenPrice: true,
                    changeField: key,
                }, param)
                let newData = result.data;
                // 更新明细
                newData.forEach(d => {
                    param.dataUpdater.updateDetail(mdApiName, d.rowId, d)
                });
                await this.parent.runPlugin('price-service.checkMdIsMatchCustomer.after', {
                    mdApiName,
                    param,
                })
            }
        }
    }

    // 如果修改的是价目表，重置明细的价目表字段，然后走取价服务校验
    setPriceBookId(data = [], val = '', mdApiName) {
        let {price_book_id} = this.getAllFields(mdApiName);
        data.forEach(item => item[price_book_id] = val);
    }

    // 校验客户、日期 和 价目表是否匹配；
    validPriceBookByCustomer(param) {
        let url = `FHH/EM1HNCRM/API/v1/object/pricebook/service/validate_account_pricebook`;
        return PPM.ajax(this.parent.request, url, Object.assign({},{
            price_book_id: '',
            account_id: '',
            partner_id: '',
            object_data: {} // 主对象数据
        }, param))
    }

    /**
     * @desc 查找更改的字段；按顺序查找 客户》价目表》合作伙伴》日期
     * @param changeData
     * @param masterApiName
     * @param param
     * @returns {*}
     */
    findChangeField({changeData = {}, masterApiName = ''}, param) {
        let {form_account_id, form_partner_id, form_price_book_id} = this.getAllFields(param.objApiName);
        let matchFields = this.getConfig('match_price_book_valid_field');
        let config = matchFields ? JSON.parse(matchFields) : {};
        let field = config[masterApiName];
        let keys = Object.keys(changeData);
        if (keys.includes(form_account_id)) return form_account_id;
        if (keys.includes(form_price_book_id)) return form_price_book_id;
        if (keys.includes(form_partner_id)) return form_partner_id;
        if (keys.includes(field)) return field;
    }

    // mdRenderAfter 后执行，接口需要获取 details 数据
    async _mdRenderAfter(pluginService, param) {
        let masterData = param.dataGetter.getMasterData();
        let {form_price_book_id} = this.getAllFields();
        let des = param.dataGetter.getDescribe(param.masterObjApiName).fields;
        pluginService.api.showLoading();
        let pId = masterData[form_price_book_id] ;
        if(pId){
            await this.checkMasterPriceBook(masterData, param);
        }else{
            await this.fetchDefaultPriceBook({
                priceBookId: masterData[form_price_book_id],
                masterData,
                des,
            }, param);
        }
        pluginService.api.hideLoading();
    }

    // 校验价目表是否可用
    async checkMasterPriceBook(masterData = {}, param){
        let { form_account_id, form_price_book_id } = this.getAllFields();
        if(!masterData[form_account_id] || !masterData[form_price_book_id]) return;
        let r = await this.checkPriceBook({
            key: form_price_book_id,
            masterData,
        }, param);
        if(!r){
            param.dataUpdater.updateMaster({
                [form_price_book_id]: '',
                [form_price_book_id + '__r']: ''
            });
            this.parent.alert(this.parent.i18n("当前价目表不可用已清空，请知悉") + '!')
        }
    }

    // 设置自定义对象，价目表字段可编辑性，依赖客户字段是否有值
    setPriceBookReadOnly(field, masterData, param){
        let {form_price_book_id, form_account_id} = this.getAllFields();
        let masterApiName = param.masterObjApiName;
        if(masterApiName.includes('__c') && param.formType === "add" && field === form_account_id){
            let status = !masterData[form_account_id];
            param.dataUpdater.setReadOnly({
                fieldName: [form_price_book_id],
                objApiName: masterApiName,
                status: status
            });
        }
    }

    getHook() {
        return [
            {
                event: 'form.render.before',
                functional: this._beforeRender.bind(this)
            },{
                event: 'md.render.after',
                functional: this._mdRenderAfter.bind(this)
            }, {
                event: 'form.dataChange.after',
                functional: this._dataChange_after.bind(this)
            },
        ];
    }

}

