/**
 * @Description: BOM (添加数据)
 * <AUTHOR>
 * @date 2022/1/22
 */

import PPM from 'plugin_public_methods'

export default class Add {

    constructor(pluginService, pluginParam, parent) {
        this.pluginService = pluginService;
        this.pluginParam = pluginParam;
        this.parent = parent;
        this._cacheEditRootRow = [];
    }

    getAllFields(mdApiname) {
        return this.parent.getAllFields(mdApiname);
    }

    getMultiUnitPluginFields(mdApiName) {
        return this.parent?.getPluginFields?.('multi-unit', mdApiName) || {};
    }

    getPeriodProductPluginFields(mdApiName) {
        return this.parent?.getPluginFields?.('period_product', mdApiName) || {};
    }

    // 获取多个插件的映射字段
    getAllPluginFields(mdApiName){
        return this.parent?.getSomePluginFields(['bom', 'attribute', 'period_product', 'multi-unit'], mdApiName)
    }

    getDescribe(mdApiName = '', param = {}, field = '') {
        if (!this.mdDescribe) this.mdDescribe = param?.dataGetter?.getDescribe?.(mdApiName)?.fields || {};
        if (field) return this.mdDescribe[field];
        return this.mdDescribe;
    }

    getFixedCollocationOpenStatus() {
        return this.parent.getConfig('fixedCollocationOpenStatus');
    }

    // 不清 sales_price 字段;
    noClearSalesPrice(){
        return CRM.util.noClearSalesPrice && CRM.util.noClearSalesPrice();
    }

    // 选完数据之后，处理BOM数据
    async _batchAddAfter(plugin, param) {
        let r1 = await this.parent.runPlugin('bom.parseAddBomData.before', {
            param,
        });
        if (r1 && r1.parseData) {
            let fieldMapping = this.getAllFields();
            r1.parseData({
                fieldMapping,
                param
            });
        }
        this._setRootQuantity(param.addDatas, param);
        this._formAdd = true;
        let {   allChildren, allAddData,} = await this.parseAddBomData({
            addDatas: param.addDatas,
            lookupDatas: param.lookupDatas
        }, param, plugin);
        await this.parent.runPlugin('bom.parseAddBomData.end', {
            param,
        });

    }

    // 给产品包补数量；没有数量补1
    _setRootQuantity(addDatas, param) {
        let mdApiName = param.objApiName;
        let allFields = this.getAllFields(mdApiName);
        let {
            quantity,
        } = allFields;
            addDatas.forEach(item => {
            if (PPM.isBom(item, allFields).isPackage) {
                if(!PPM.hasValue(item[quantity])){
                    let up = {
                        [quantity]:1
                    };
                    param.dataUpdater.updateDetail(mdApiName, item.rowId, up);
                }
            }
        })
    }

    // 选完数据之后，处理BOM数据映射
    async _batchAddEnd(plugin, param) {
        let mdApiName = param.objApiName;
        let masterApiName = param.masterObjApiName;
        this._needCalBom_add = false; 
        if(!this._cacheAllAddData) return;
        let {parent_prod_pkg_key} = this.getAllFields(mdApiName);
        let children = this._cacheAllAddData.filter(item => item[parent_prod_pkg_key]);
        await this.parent.runPlugin('bom.md.batchAdd.end', {
            param,
            list: this._cacheAllAddData,
            children,
            objectApiName: masterApiName,
            detailObjectApiName: mdApiName,
        });
        this._cacheAllAddData = [];
    }

    // 是否开了价格政策
    isOpenAdvancePrice() {
        let allPlugins = this.pluginService.api.getPlugins() || [];
        let hasAdvancePlugin = allPlugins.find(item => item.pluginApiName === 'price_policy');
        return hasAdvancePlugin;
    }

    // 是否开启了临时子件
    isOpenQuoter() {
        return this.parent.getConfig('is_open_quoter') == '1';
    }

    // 有某个插件
    hasPlugin(pluginName, mdApiName){
        let plugin = this.pluginService.api.getPlugins().find(item => item.pluginApiName === pluginName);
        if(!plugin) return false;
        let details = plugin.params.details || [];
        let md = details.find(item => item.objectApiName === mdApiName);
        return !!md
    }

    // 添加数据时，处理BOM数据
    async parseAddBomData({
                              addDatas = [],
                              lookupDatas = []
                          } = {}, param, plugin) {
        let mdApiName = param.objApiName;
        let recordType = param.recordType;
        let {
            product_price,
            quantity,
            discount,
            price_book_id,
            product_group_id,
        } = this.getAllFields(mdApiName);

        await this._calRootQuantity({
            addDatas,
            mdApiName,
        }, param);

        let allAddData = await this.concatChildren({
            addDatas,
            mdApiName,
            lookupDatas,
            recordType
        }, param, plugin);

        let list1 = allAddData.filter(item => !item.isGroup);
        let r1 = await this.parent.runPlugin('bom.parseAddBomData.after', {
            allAddData: list1,
            mdApiName,
            param,
        });
        let allChildren = PPM.getChildren({
            details: allAddData,
            excludeGroup: true
        });
        let ignoreCalFields = [product_price, quantity, discount, product_group_id];
        let masterData = param.dataGetter.getMasterData();
        let masterApiName = param.masterObjApiName;

        // 如果是配置价格，没有价目表，折扣写死为1，再计算一遍
        // 佳能不用折扣字段
        let EA = ['bis001'];
        if(this.parent.isGrayDeleteRoot() && !EA.includes(CRM.ea)){
            let noDiscount = allChildren.filter(c => !c[price_book_id]);
            if(noDiscount.length){
                noDiscount.forEach(c => c[discount] = 100);

            }
        }

        // 是否需要计算bom。开了分摊再调用querybom接口
        let needCalBom = this.isSupportShareRate(mdApiName, param);
        let noCalBomPrice = true;
        // 如果有报价器改了子件的价格或数量，也需要重新计算包价格
        let findQuoter = this.isOpenQuoter() && allChildren.find(item => item._isChangedByQuoter);
        if(findQuoter){
            needCalBom = true;
            noCalBomPrice = false;
        }

        // 有周期性插件，先不算bom，等周期性走完再计算；
        let periodPlugin = this.hasPlugin('period_product', mdApiName);
        if(periodPlugin){
            this._needCalBom_add = true;
            needCalBom = false;
        }
        await this.calculateBomPrice({
            data: allAddData,
            mdApiName,
            masterData,
            masterApiName,
            recordType,
            needCalBom,
            noCalBomPrice,   // 新添加数据，不用 server 计算包的价格，只分摊就行
            from: 'add',
            triggerUIEvent: false,
            noTriggerCal: true,   // 新添加数据，不用再重复走计算了
        }, param);

        // 因为之前底层的ui事件参数里缺少子产品数据，所以再走一次新增数据的ui事件
        // if(allChildren.length) await param.triggerUIEvent();

        this.setFieldsReadonly({
            data: allAddData,
            mdApiName,
            recordType
        }, param);

        this._changeCalParam({
            allCalData: allAddData,
            mdApiName,
        }, param);

        // 格式化子件一次性产品
        // await this.parent.runPluginSync('period-product.initOneProduct', {
        //     mdApiName,
        //     data: allChildren,
        //     param
        // });

        this._cacheAllAddData = allAddData;
        this._cacheAllChildren = allChildren.filter(c => !c.isGroup);

        return {
            allChildren,
            allAddData,
        }
        // return {
        //     newDatas: allAddData
        // };
    }

    // 如果有产品包，且数量字段有默认值公式，需要先计算一下数量，后续需要计算子件数量用；
    async _calRootQuantity({
                      addDatas = [],
                      mdApiName = '',
                  } = {}, param){
        let allFields = this.getAllFields(mdApiName);
        let rootData = addDatas.filter(item => PPM.isBom(item, allFields).isPackage);
        if(!rootData.length) return;
        let {quantity} = this.getAllFields(mdApiName);
        let findNoQuantity = rootData.find(item => !PPM.hasValue(item[quantity]));
        if(!findNoQuantity) return;
        let modifyIndex = rootData.map(item => item.rowId);
        let des = param.dataGetter.getDescribe().fields;
        let defVal = des[quantity].default_value;
        if(defVal){
            let p = {
                operateType: 'mdAdd',
                dataIndex: modifyIndex,
                objApiName: mdApiName,
            };
            await param.triggerCal(p);
        }
    }

    // 更改底层计算参数；母件不计算价格，子件不计算预制字段；
    _changeCalParam({allCalData, mdApiName} = {}, param){
        let allFields = this.getAllPluginFields(mdApiName);
        let {
            bom_id,
            quantity,
            product_price,
            discount,
            extra_discount,
            node_subtotal,
            share_rate,
            sales_price,
            subtotal,
            price_book_discount,
            amount_any,
            product_group_id,
            price_book_price,
            price_book_subtotal,
            price_per_set, pricing_period
        } = allFields;

        let obj = {};
        let allChildren = allCalData.filter(item => item.parent_rowId && !item.isGroup);
        let rootData = allCalData.filter(item => PPM.isBom(item, allFields).isPackage);

        function _fn(fieldList = []){
            return fieldList.map(f => {
                return {
                    "fieldName": f,
                    "order": 1
                }
            })
        }

        // 灰了不回填母件，子件价格 数量不走计算接口算
        if(this.parent.isGrayDeleteRoot()){
            let filterChildren = {};
            let fields = [product_price, quantity];
            if(pricing_period){
                fields = fields.concat([price_per_set, pricing_period])
            }
            allChildren.forEach(item => {
                filterChildren[item.rowId] =  _fn(fields)
            });
            obj = {
                modifiedDataIndexList: allCalData.map(item => item.rowId),
                excludedDetailCalculateFields: {
                    [mdApiName]:{
                        ...filterChildren,
                    }
                }
            };
        }else{
            let filterRoot = {};
            let filterChildren = {};

            // 母件不计算价格，子件不计算预制字段；
            if(rootData.length){
                let des = this.getDescribe(mdApiName, param);
                let filterRootFields = [product_price, amount_any,];
                if(pricing_period){
                    filterRootFields = filterRootFields.concat([price_per_set]);
                }
                PPM.each(des, (item, f) => {
                    if(item.type === 'quote' && item.quote_field?.includes(bom_id)){
                        filterRootFields.push(item.api_name)
                    }
                });
                rootData.forEach(item => {
                    filterRoot[item.rowId] =  _fn(filterRootFields)
                });
            }

            // 子件这些字段不计算
            if(allChildren && allChildren.length){
                let fieldList = [product_price, quantity, extra_discount, subtotal, price_book_discount, product_group_id, price_book_price, price_book_subtotal, ];
                if(pricing_period){
                    fieldList = fieldList.concat([price_per_set, pricing_period]);
                }
                let r1 = this.parent.runPluginSync('bom.filterCalculateFields.before', {
                    mdApiName,
                    param
                });
                if(r1 && r1.filterFields){
                    fieldList = fieldList.concat(r1.filterFields);
                }
                let fieldList_share = [].concat(fieldList, [node_subtotal, discount, sales_price]);

                // 开了不清销售单价，需要走计算接口计算销售单价
                fieldList = this.noClearSalesPrice() ? fieldList : fieldList_share;
                let fieldList_tempNode = [].concat(fieldList, this._getBasicFieldsForTempNode(mdApiName)); // 临时子件过滤字段
                allChildren.forEach(c => {
                    // 有分摊比例的子件，不走接口计算[node_subtotal, discount, sales_price]，取 querybom返回的值；
                    if(PPM.hasValue(c[share_rate])){
                        filterChildren[c.rowId] =  _fn(fieldList_share);
                    }else if(this.isTempNode(c)){
                        filterChildren[c.rowId] =  _fn(fieldList_tempNode);
                    }else{
                        filterChildren[c.rowId] =  _fn(fieldList);
                    }
                });
            }

            obj = {
                modifiedDataIndexList: allCalData.map(item => item.rowId),
                excludedDetailCalculateFields: {
                    [mdApiName]:{
                        ...filterRoot,
                        ...filterChildren,
                    }
                }
            };
        }
        if(rootData?.length || allChildren?.length || this.parent.isGrayDeleteRoot()){
            // 不能改底层数据引用
            if(param.dataIndex) param.dataIndex.splice(0, param.dataIndex.length, ...obj.modifiedDataIndexList);
            // todo: 底层需要把parseParam改成个数组形式，现在用起来太奇怪；
            let fn1 = param.parseParam;
            param.parseParam = (p) => {
                if(fn1) p = fn1(p);
                p.modifiedDataIndexList = p.modifiedDataIndexList || [];
                p.modifiedDataIndexList = p.modifiedDataIndexList.concat(obj.modifiedDataIndexList);
                p.excludedDetailCalculateFields = p.excludedDetailCalculateFields || {};
                this.mergeCalculateFields(p.excludedDetailCalculateFields, obj.excludedDetailCalculateFields);
                return p;
            };
        }
    }

    // 获取 子级不计算的字段
    _getChildrenNoCalculateFields(param){
        let mdApiName = param?.objApiName;
        let {
            quantity,
            product_price,
            discount,
            extra_discount,
            node_subtotal,
            share_rate,
            sales_price,
            subtotal,
            price_book_discount,
            amount_any,
            product_group_id,
            price_book_price,
            price_book_subtotal,
            price_editable, amount_editable, price_mode, min_amount, max_amount, increment,
            price_per_set, pricing_period

        } = this.getAllPluginFields(mdApiName);
        return  [product_price, quantity, extra_discount, subtotal, price_book_discount, product_group_id, price_book_price, price_book_subtotal, node_subtotal, discount, sales_price, price_per_set, pricing_period
        ]
    }

    // 合并计算过滤字段；
    mergeCalculateFields(base, self){
        PPM.each(self, (val, key) => {
            base[key] = base[key] || {};
            PPM.each(val, (arr, rowId) => {
                if(base[key][rowId]){
                    base[key][rowId] = base[key][rowId].concat(arr)
                }else{
                    base[key][rowId] = arr;
                }
            })
        })
    }

    async concatChildren({
                             addDatas = [],
                             mdApiName = '',
                             lookupDatas = {},
                             recordType = ''
                         }, param, plugin) {
        let {
            node_type,
            quantity,
        } = this.getAllFields(mdApiName);
        let allChildren = [];
        let nc = [];
        let rootList = [];

        for (let arr of lookupDatas.entries()) {
            let index = arr[0];
            let item = arr[1];
            if(!item) break;
            this.setQuantityForModule2(addDatas[index], item, mdApiName, param);
            if(this.isOpenTempNode()) addDatas[index][node_type] = null;
            if (PPM.isBom(item).isPackage) {
                let rootData = addDatas[index];
                rootList.push(rootData);
              
                // 处理产品包
                this.updateRootDataForAdd({
                    lookUpRootData: item,
                    rootData,
                    mdApiName,
                    param
                });

                let newChildren = [];
                if (item.children && item.children.length) {
                    // 更新pid
                    this.updateParentRowId(item.children, rootData);
                    // 子产品补字段；
                    newChildren = PPM.parseTreeToNormal(item.children);
                    //
                    newChildren = await this.parseNewChildren({
                        newChildren, // usefulFields_md
                        rootData: rootData,
                        mdApiName,
                        recordType
                    }, param);

                    allChildren = allChildren.concat(newChildren);
                }else{
                    this.updateProdKey([], rootData);
                }
                // 处理子件数量；
                this.setSubQuantity({
                    allData: [].concat(newChildren).concat([rootData]),
                    rootQuantity: rootData[quantity],
                    mdApiName,
                }, param, plugin);
            }
        }
        if (allChildren.length) {
            nc = param.dataUpdater.add(allChildren);
        }
        if(this.parent.isGrayDeleteRoot() && rootList.length){
            this.deleteRoot(addDatas, rootList, mdApiName, param);
        }
        let allAddData = addDatas.concat(nc);
        return allAddData;
    }

    // 新增产品包时，更新母件数据
    updateRootDataForAdd({lookUpRootData = {}, rootData = {}, mdApiName = '', updateAll = false, param} = {}){
        let {
            is_package,
            product_price,
            bom_id,
            product_id,
            node_type,
            bom_core_id,
            bom_type,
            bom_version,
            new_bom_path,
            price_book_price,
            price_book_discount,
        } = this.getAllFields(mdApiName);
        let periodProductFields = this.getPeriodProductPluginFields(mdApiName);
        let hasProductId = lookUpRootData.hasOwnProperty('product_id');
        let isUpdateRootPrice = lookUpRootData?.children?.length || lookUpRootData._updateRootPrice  || !PPM.hasValue(rootData[product_price]);
        let obj = {
            [bom_core_id]: lookUpRootData.core_id,
            [bom_core_id + '__r']: lookUpRootData.core_id__r,
         
            [bom_id]: lookUpRootData.bom_id,
            [new_bom_path]: lookUpRootData.bom_id,
            [product_id]: hasProductId ? lookUpRootData.product_id : lookUpRootData._id,
            [product_id + '__r']: hasProductId ? lookUpRootData.product_id__r : lookUpRootData.display_name || lookUpRootData.name,
        };
        if(isUpdateRootPrice || updateAll) {
            obj[product_price] = lookUpRootData.newestPrice;
            obj[price_book_price] = lookUpRootData.newestPrice;
            // 周期性产品
            if(periodProductFields){
                let {price_per_set, pricing_period} = periodProductFields;
                obj[price_per_set] = lookUpRootData.totalSingleSetPrice;
            }
        }
        if (!rootData.hasOwnProperty(is_package + '__v') || updateAll) {
            obj = Object.assign(obj, {
                [is_package]: lookUpRootData.is_package,
                [is_package + '__v']: PPM.hasValue(lookUpRootData.is_package__v) ? lookUpRootData.is_package__v : lookUpRootData.is_package,
            })
        }
        if (!rootData.hasOwnProperty(bom_type + '__v') || updateAll) {
            obj = Object.assign(obj, {
                [bom_type + '__v']: lookUpRootData.node_bom_core_type,
            })
        }
        // 给母件补价目表折扣
        if(!PPM.hasValue(rootData[price_book_discount]) || updateAll){
            obj[price_book_discount] = lookUpRootData.discount;
        }
        for(let key in obj){
            if(obj[key] === undefined) delete obj[key];
        }
        param.dataUpdater.updateDetail(mdApiName, rootData.rowId, obj);
    }

    // 模式 2
    _isModule2(){
        return CRM.util.getConfigStatusByKey('cpq_ui_mode') === '1';
    }

    // 模式 2 回填数量
    setQuantityForModule2(addData, lookUpData,  mdApiName, param){
        if(!this._isModule2()) return;
        let {
            quantity,
        } = this.getAllFields(mdApiName);
        if(lookUpData._selfQuantity){
            param.dataUpdater.updateDetail(mdApiName, addData.rowId, {
                [quantity]: lookUpData._selfQuantity,
            });
        }
    }

    // 分割包数据，5个为一组，返回一个二维数组
    _splitRootList(rootList){
        const SplitNum = 5;
        let res = [];
        let n = 0;
        let arr = [];
        rootList.forEach(item => {
            if(n <= SplitNum){
                n++;
                arr.push(item);
                if(n === SplitNum){
                    res.push(arr);
                    n = 0;
                    arr = []
                }
            }
        });
        if(arr.length) res.push(arr);
        console.log(res);
        return res;
    }

    /**
     * @desc bom分摊和计算
     * @param data 只给产品包数据就行
     * @param mdApiName
     * @param masterData
     * @param masterApiName
     * @param recordType
     * @param changeField
     * @param changeRow Array || Object 更改行
     * @param needCalBom Boolean 是否需要调接口计算bom
     * @param param
     * @returns {Promise<void>}
     */
    async calculateBomPrice({
                                data = [],
                                mdApiName = '',
                                masterData = {},
                                masterApiName = '',
                                recordType = '',
                                changeField = '',
                                changeRow,
                                switchMasterPriceBook = false,
                                needCalBom = true,      // 是否计算 bom 价格
                                noCalBomPrice = false,  // server 不计算 bom 价格，只分摊
                                noCalPriceBookPrice = false,  // 不计算价目表价格
                                from = '',
                                triggerUIEvent = true,
                                noTriggerCal = false,  // 不走后续计算接口
                            } = {}, param) {
        if(this.parent.isGrayDeleteRoot()) return;

        let {
            product_price,
            form_account_id,
        } = this.getAllFields(mdApiName);
        if(!masterData[form_account_id]) {
            if(from === 'edit') this.pluginService.api.alert(this.parent.i18n('请先选择客户'));
            return;
        }
        data = PPM.uniq(data,item=> item.rowId);
        let needCalData = [];
        let details = param.dataGetter.getDetail(mdApiName);
        this._rootPriceChange = [];
        let startTime = new Date().getTime();
        let rootList = data.filter(item => PPM.isBom(item, this.getAllFields(mdApiName)).isPackage && !item.parent_rowId);
        if(!rootList.length) return;

        let allChildren = data.filter(item => item.parent_rowId);
        let r2 = this.parent.runPluginSync('bom.calculateBomPrice.before', {
            rootData: rootList,
            children: allChildren,
            needCalBom,
            mdApiName,
            param
        });
        if (r2 && r2.needCalBom !== undefined) {
            needCalBom = r2.needCalBom;
            if(r2.needCalBom) noCalBomPrice = false;
        }

        rootList = this._splitRootList(rootList);

        let list = [];

        rootList.forEach(arr => {
            let rootDataList = {};
            let childrenList = {};
            arr.forEach(item => {
                let children = PPM.getChildren({
                    rowId: item.rowId,
                    details,
                    excludeGroup: true
                });
                this.addDiscountForChildren(children, item);
                childrenList[item.rowId] = children;
                rootDataList[item.rowId] = item;
                needCalData = needCalData.concat([item]).concat(children);

            });

            let nc = needCalBom;

            if(nc){
                let r = this.calculateBomPriceAndShareRate({
                    rowData: changeRow || Object.values(rootDataList),
                    rootDataList,
                    childrenList,
                    changeField: changeField || product_price,
                    masterData,
                    masterApiName,
                    mdApiName,
                    recordType,
                    switchMasterPriceBook,
                    noCalBomPrice
                }, param);
                list.push(r);
            }
        })

        await Promise.all(list);

        // 走完 querybom 后计算
        if (needCalData.length && !noTriggerCal) {
            let f = this._rootPriceChange.length ? product_price : changeField || product_price ;
            await this.calculate({
                data: needCalData,
                mdApiName,
                changeField: f,
                triggerUIEvent,
                noCalPriceBookPrice
            }, param);
        }

        if(from === 'add'){
            let endTime = new Date().getTime();
            let allTime = endTime - startTime;
            this.parent.sendLog({
                eventId: 'fs-crm-sfa-plugin-selectbom-log',
                eventType: 'PROD',
                eventName: 'pv',
                // 对象ApiName
                apiName:'bom',
                num1: allTime,
            })
        }
    }


    /**
     * @desc bom计算，且有分摊比例时执行分摊
     * @param rowData 修改当前行数据
     * @param rootData 根节点数据
     * @param children 子节点数据
     * @param changeField 修改字段 String || Array
     * @returns {Promise<unknown>}
     */
    async calculateBomPriceAndShareRate({
                                            rowData ,
                                            // rootData = {},
                                            // children = [],
                                            rootDataList = {},
                                            childrenList = {},
                                            changeField,
                                            masterData = {},
                                            masterApiName = '',
                                            mdApiName = '',
                                            recordType = '',
                                            switchMasterPriceBook = false,
                                            noCalBomPrice = false,

                                        } = {}, param) {
        let rq = this.getParamForCalBomPrice({
            rowData,
            rootDataList,
            childrenList,
            masterData,
            changeField,
            masterApiName,
            mdApiName,
            switchMasterPriceBook,
            noCalBomPrice,
        }, param);
        if(!rq) return;
        this.pluginService.api.showLoading();
        let res = await this.requestBomPrice(rq);
        this.pluginService.api.hideLoading();
        console.log(res);

        for (let key in res){
            let newData = res[key];
            this.addTempNodeGroupId(newData, childrenList[key]);
            await this.updateBomAfterShare({
                newData,
                rootData: rootDataList[key],
                mdApiName,
                changeField,
                recordType,
                noCalBomPrice
            }, param);
        }
    }

    // 给临时子产品补分组id
    addTempNodeGroupId(newData, oldData) {
        let {product_group_id} = this.getAllFields();
        oldData.forEach(item => {
            if (this.isTempNode(item) && item[product_group_id]) {
                let fn = newData.find(d => d.prod_pkg_key === item.rowId);
                if (fn) {
                    fn[product_group_id] = item[product_group_id + '__v'];
                    fn[product_group_id + '__r'] = item[product_group_id];
                }
            }
        })
    }

    parseNewDataBeforeUpdate(newData, rootData, mdApiName, noCalBomPrice, param) {
        let {product_group_id} = this.getAllFields(mdApiName);
        let newRootData = {};
        let subData = [];
        newData = this.sortNewData(newData, param);

        // 不让 server 计算价格，需要补分组数据
        if(noCalBomPrice){
            let allData = param.dataGetter.getDetail(mdApiName);
            let oldChildrenData = PPM.getChildren({
                rowId: rootData.rowId,
                details: allData
            });
            newData.forEach(item => {
                let findOld = oldChildrenData.find(r => r.rowId === item.prod_pkg_key);
                if(findOld && findOld[product_group_id + '__v'] && !item.product_group_id){
                    item.product_group_id = findOld[product_group_id + '__v'];
                    item.product_group_id__r = findOld[product_group_id];
                }
            })
        }

        newData.forEach(item => {
            if (!this.isTempNode(item)) {
                item.bom_id = item._id;
                item.bom_id__r = item.name;
            }else{
                if(item[product_group_id]){
                   item.product_group_id = item[product_group_id];
                   item.product_group_id__r = item[product_group_id + '__r'];
                }
            }
            item.modified_adjust_price = item.adjust_price;
            if (item.parent_bom_id || item.parent_prod_pkg_key) {
                subData.push(item);
            }else{
                newRootData = item;
            }
            // 有分组，创建分组数据；
            if (item.product_group_id) {
                let f = subData.find(c => c._id === item.product_group_id && c.isGroup && item.current_root_new_path === c.current_root_new_path);
                if(!f){
                    let parentBomPath = item.new_bom_path.split('.');
                    parentBomPath.pop();
                    parentBomPath = parentBomPath.join('.');
                    subData.push({
                        _id: item.product_group_id,
                        product_id: item.product_group_id__r,
                        name: item.product_group_id__r,
                        bom_id: item.product_group_id,
                        parent_bom_id: item.parent_bom_id,
                        children: [],
                        isGroup: true,
                        isFake: true,
                        new_bom_path: parentBomPath + '.' + item.product_group_id,
                        current_root_new_path: item.current_root_new_path,
                    })
                }
            }
        });

        subData = PPM.parseDataToBOM(subData, newRootData.bom_id);
        return {
            newRootData,
            subData
        }
    }

    // 分摊计算之后更新BOM
    async updateBomAfterShare({
                                  newData = [],
                                  rootData = {},
                                  mdApiName = '',
                                  changeField,
                                  recordType = '',
                                  noCalBomPrice = false,
                              }, param) {
        let obj = {};
        let {
            newRootData,
            subData
        } = this.parseNewDataBeforeUpdate(newData, rootData, mdApiName, noCalBomPrice, param);
        let details = param.dataGetter.getDetail(mdApiName);
        let oldChildren = PPM.getChildren({
            rowId: rootData.rowId,
            details
        });
        let newAddData = this.alertUpdateRemind(subData, oldChildren);
        obj = this.parseDataForUpdateBom(newRootData, subData);
        let res = await this.updateBom({
            newChildren: obj.data,
            newRootData: obj.newRootData,
            rootDataBomId: obj.rootDataBomId,
            rootDataPrice: obj.totalMoney,
            originalTotalMoney: obj.newRootData.modify_price,
            rootData: rootData,
            mdApiName: mdApiName,
            recordType,
            noCalBomPrice,
        }, param,);
        // 如果编辑子产品的价格或数量时发现有新增必选子产品，则这些新增的子产品需要走全量计算；
        if (newAddData.length) {
            await this.parent.runPlugin('bom.parseNewChildren', {
                newChildren: newAddData,
                mdApiName,
                param
            });
        }

        let r2 = this.parent.runPluginSync('bom.updateBomAfterShare.after', {
            rootData: res.newRootData,
            children: res.newChildren,
            mdApiName,
            param
        });

        return res;
    }

    // 获取bom计算行标记
    getBomCalIndex(data) {
        let allModify = []; // 所以产品的rowId
        let normalModify = []; // 非子产品的rowId
        data.forEach(item => {
            if (item.isGroup) return;
            allModify.push(item.rowId);
            if (!item.parent_rowId) normalModify.push(item.rowId);
        });
        return {
            allModify,
            normalModify
        };
    }

    /**
     * @desc BOM计算逻辑；分两次计算，第一次只算非子产品数据(包和普通产品)，第二次计算子产品数据，只计算自定义字段
     * @desc 不计算折扣、额外折扣；
     * @param data
     * @param mdApiName
     * @param changeField 编辑的字段 String || Array
     */
    async calculate({
                        data = [],
                        mdApiName = '',
                        changeField,
                        operateType = 'mdEdit',
                        triggerUIEvent = true,
                        noCalPriceBookPrice = false,
                    } = {}, param) {
        let {
            product_price,
            discount,
            extra_discount,
            node_subtotal,
            node_discount,
            share_rate,
            node_price,
            sales_price,
            subtotal,
            price_book_price,
            bom_id,
            product_id,
            bom_core_id
        } = this.getAllFields(mdApiName);
        data = data.filter(item => !item.isGroup);
        changeField = Array.isArray(changeField) ? changeField : [changeField];
        // 额外计算字段
        let extraFields = noCalPriceBookPrice ? [] : [price_book_price];
        // 计算和这些字段有关的字段，不含这些字段自身；
        let needCalFields = [node_subtotal, node_discount, share_rate, node_price, discount, sales_price].concat(changeField);
        // 不影响价格就不用计算价格相关字段了；
        if (!changeField.includes(subtotal) && !needCalFields.includes(product_price)) needCalFields.push(product_price);
        // 如果更改字段包含 product_id，则需要计算 bom_id 相关字段
        if (changeField.includes(product_id)) {
            needCalFields.push(bom_id, bom_core_id);
        }
        // 不能算价格、额外折扣和变化字段自身；
        let ignoreCalFields = [extra_discount, product_price].concat(changeField);
        // 如果价目表有变化，需要重新取价目表折扣
        if (this._price_book_id_change) extraFields.push(discount);
        this._price_book_id_change = false;
        // 把子产品和普通产品分开计算；
        let {allModify, normalModify} = this.getBomCalIndex(data);
        // let hasChildren = allModify.length !== normalModify.length; // 有子产品
        let filterFields = {
            [mdApiName]: ignoreCalFields
        };
        filterFields = this._mergeFilterFields(filterFields, param);
        let p = {
            // noMerge: true,
            changeFields: needCalFields,
            operateType: operateType,
            dataIndex: allModify,
            objApiName: mdApiName,
            triggerField: changeField[0],
            filterFields,
            extraFields: {
                [mdApiName]: extraFields
            },
        };
        // 支持不触发 ui 事件
        let calMethod = triggerUIEvent ? 'triggerCalAndUIEvent' : 'triggerCal';
        this._changeCalParam({
            allCalData: data,
            mdApiName,
        }, param);
        await param[calMethod](p);
    }

    /**
     * @des 过滤计算字段；子产品只计算特定字段；
     * @param obj 计算入参
     * @param mdApiName
     * @returns {*}
     */
    filterCalFields(obj, mdApiName) {
        if(this.parent.isGrayDeleteRoot()) return obj;
        const {
            base_product_price,
            base_price,
            mc_currency,
            mc_exchange_rate,
            price_editable,
            price_mode,
            min_amount,
            increment,
            amount_editable,
            max_amount,
            unit,
            bom_type,
            bom_version,
            amount_any,
            sales_price,
            subtotal,
            discount,
        } = this.getAllFields(mdApiName);

        const {is_multiple_unit} = this.getMultiUnitPluginFields(mdApiName);
        let needCalFields = [base_product_price, base_price, mc_currency, mc_exchange_rate, price_editable, price_mode, min_amount, increment, amount_editable, max_amount, unit, is_multiple_unit,  bom_type, bom_version, amount_any];
        let noCalFields = [sales_price, subtotal, discount];

        if (obj.calculateFieldApiNames) {
            let keys = Object.keys(obj.calculateFieldApiNames);
            keys.forEach(key => {
                if (key === mdApiName) {
                    let arr = obj.calculateFieldApiNames[key];
                    obj.calculateFieldApiNames[key] = arr.filter(function (field) {
                        return (field.includes('__c') || needCalFields.includes(field)) && !noCalFields.includes(field);
                    })
                }
            });
        }
        if (obj.calculateFields) {
            let keys = Object.keys(obj.calculateFields);
            keys.forEach(key => {
                if (key === mdApiName) {
                    let arr = obj.calculateFields[key];
                    obj.calculateFields[key] = arr.filter(function (item) {
                        return (item.fieldName.includes('__c') || needCalFields.includes(item.fieldName)) && !noCalFields.includes(item.fieldName);
                    })
                }
            });
        }
        return obj
    }

    // 组装数据，给更新数据方法用
    parseDataForUpdateBom(newRootData, subData) {
        newRootData.pricebook_id = newRootData.price_book_id;
        newRootData.pricebook_id__r = newRootData.price_book_id__r;
        return {
            totalMoney: newRootData.adjust_price,
            data: subData,
            rootDataBomId: newRootData.bom_id,
            newRootData: newRootData
        }
    }

    // 校验产品包配置是否有变化，提示用户；如增、删、改了子节点
    alertUpdateRemind(newChildren, oldChildren) {
        let res = [];
        PPM.forEachTreeData(newChildren, item => {
            if (PPM.isObject(item) && !item.isGroup) {
                let r = PPM.findDataByBomId(oldChildren, item.prod_pkg_key, 'rowId');
                if (!r) res.push(item);
            }
        });
        if (res.length) this.pluginService.api.alert(this.parent.i18n('产品包配置有变化，已更新为最新配置'));
        return res;
    }

    // 新数据需要根据老数据的顺序进行排序；
    sortNewData(newData, param) {
        let _this = this;
        if (_this._cacheParamData) {
            newData.forEach(nd => {
                let findCache = _this._cacheParamData.find(item => item.bomId == nd._id);
                if (findCache) {
                    nd._sortBy = findCache.orderField;
                }
            });
            _this._cacheParamData = null;
            newData = PPM.sortTreeData(newData, '_sortBy');
        }
        let r = this.parent.runPluginSync('bom.sortNewData.after', {
            data: newData,
            param,
        });
        if (r && r.data) newData = r.data;
        return newData;
    }

    // 删除标记
    _clearQuoterMark(data){
        delete data._isChangedByQuoter;
        delete data._isChangeQuantityByQuoter;
        delete data._isChangePricingByQuoter;
    }

    // 是否有配高级公式
    _hasFormular(rowId, param, allFields){
        let {product_price} = allFields;
        if(!param.formulaInfo || !param.formulaInfo[product_price]) return false;
        return param.formulaInfo[product_price].includes(rowId);
    }

    // 处理计算 bom入参
    getParamForCalBomPrice({
                               rowData,
                               rootDataList = {},
                               childrenList = {},
                               masterData = {},
                               changeField,
                               masterApiName = '',
                               mdApiName = '',
                               switchMasterPriceBook = false,
                               noCalBomPrice = false,
                           } = {}, param) {
        let allFields = this.getAllPluginFields();
        let {
            product_price,
            quantity,
            bom_id,
            product_id,
            subtotal,
            price_book_id,
            attribute_price_book_id,
            attribute_json,
            form_account_id,
            form_partner_id,
            form_mc_currency,
            node_type,
            root_prod_pkg_key,
            parent_prod_pkg_key,
            prod_pkg_key,
            bom_core_id,
            new_bom_path,
            bom_type,
            bom_version,
            related_core_id,
            price_book_discount,
            share_rate,
            price_per_set,
            pricing_period,
            pricing_mode

        } = allFields;

        let changeRow = Array.isArray(rowData) ? rowData : [rowData];
        changeField = Array.isArray(changeField) ? changeField : [changeField];
        let rootList = [];
        let allChildren = [];
        let newBomMap = {};
        let updateFields = [pricing_period, product_price, quantity];

        PPM.each(rootDataList, (rootData, key) => {
            let children = childrenList[key];
            let newData = children.concat([rootData]);
            newData = newData.filter(item => !item.isGroup && item[bom_id] || this.isTempNode(item));
            this._addDefQuantity(newData, rootData, param);
            newData = newData.map((item, index) => {
                let hasFormular = this._hasFormular(item.rowId, param, allFields);
                let attrMap = item[attribute_json] || {};
                if (PPM.isString(attrMap)) attrMap = JSON.parse(attrMap);
                let obj = {
                    coreId: item[bom_core_id],
                    newBomPath: item[new_bom_path] || item[bom_id],
                    nodeBomCoreType: item[bom_type + '__v'] || item[bom_type],
                    nodeBomCoreVersion: item[bom_version],
                    relatedCoreId: item[related_core_id],
                    "bomId": item[bom_id],
                    "productId": item[product_id],
                    "priceBookId": item[price_book_id] || '',
                    "price": item.hasOwnProperty('__' + product_price) ? item['__' + product_price] : item[product_price],
                    "amount": item.parent_rowId ? item.defQuantity : '1',
                    "attrPriceBookId": item[attribute_price_book_id] || '',
                    "attrMap": attrMap,
                    "updateFlag": false,
                    "orderField": index,
                    "nodeType": item[node_type],
                    "rootProdKey": item[root_prod_pkg_key],
                    "parentProdKey": item[parent_prod_pkg_key],
                    "prodKey": item[prod_pkg_key],
                    "parentBomId": item.parent_bom_id || '',
                    "shareRate": item.hasOwnProperty(share_rate) ? item[share_rate] : '',
                    advancePriceFlag: hasFormular || !!item.advancePriceFlag,
                };
                if(noCalBomPrice){
                    obj.product_group_id = item.product_group_id__v  || '';
                    obj.product_group_id__r = item.product_group_id  || '';
                }
                // 周期性产品
                if(pricing_period){
                    obj.pricingPeriod = item[pricing_period];
                    obj.pricePerSet = item[price_per_set];
                    obj.pricingMode = item[pricing_mode] || '';
                }
                if(item.hasOwnProperty('adjust_price')) obj.originPrice = item.adjust_price;
                // 给变化的行加标记，server用
                let f = changeRow.find(d => d.rowId === item.rowId);
                if (f) {
                    obj.updateFlag = true;
                    obj.updateField = updateFields.includes(changeField?.[0]) ? changeField?.[0] : '';
                }
                // 高级公式计算价格的行，价格永远不变。以高级公式计算为准
                if(item._isChangedByQuoter || obj.advancePriceFlag){
                    obj.updateFlag = true;
                    this._clearQuoterMark(item);
                }
                return obj;
            });
            let fd = newData.filter(item => item.bomId || item.nodeType);
            if(!fd.length) return;

            newBomMap[key] = {
                coreId: rootData[bom_core_id],
                nodeBomCoreType: rootData[bom_type + '__v'] || rootData[bom_type],
                nodeBomCoreVersion: rootData[bom_version],
                rootSubtotal: changeField.includes(subtotal) ? rootData[subtotal] : null, //母件小计
                rootAmount: rootData[quantity], //母件数量
                "rootBomId": rootData[bom_id], // 根结点bom_id
                "bomList": newData,
                priceBookDiscount: rootData[price_book_discount]
            };
            rootList.push(rootData);
            allChildren = allChildren.concat(children);
        });

        if(!rootList.length) return;

        this._cacheParamData = allChildren;

        const priceBookDesc = param.dataGetter.getDescribe(mdApiName)?.fields?.price_book_id;
        const details = PPM.parsePriceBookDataRangeDetails(
            param.dataGetter.getDetails(),
            priceBookDesc,
            mdApiName,
            // {field: product_id, value: rootData[product_id]}
        );

        return {
            "accountId": masterData[form_account_id],
            "partnerId": masterData[form_partner_id] || '',
            "calculateArg": changeField.includes(subtotal) || !this.isSupportShareRate(mdApiName, param) ? null : this.getCalculateInfo({
                data: rootList,
                masterApiName,
                mdApiName,
                masterData
            }, param),
            apiName: masterApiName,
            mcCurrency: masterData[form_mc_currency] || '',
            detailApiName: mdApiName,
            objectData: masterData,
            details,
            switchMasterPriceBook,  // 切主对象价目表时传true
            noCalPrice: noCalBomPrice,
            newBomMap
        };
    }

    // 补单包子件数量
    _addDefQuantity(data, rootData, param){
        let {parent_prod_pkg_key, quantity} = this.getAllFields();
        let rootQuantity = PPM.hasValue(rootData[quantity]) ? rootData[quantity] : 1;
        data.forEach(item => {
            if(item[parent_prod_pkg_key] || item.parent_rowId){
                if(!PPM.hasValue(item.defQuantity)){
                    let q = PPM.division(item[quantity], rootQuantity);
                    item.defQuantity = q;
                    param.dataUpdater.updateDetail(param.objApiName, item.rowId, {defQuantity: q});
                }
            }
        });
    }

    /**
     * @desc 获取计算接口入参；
     * @param data 从对象数据
     */
    getCalculateInfo({data = [], masterApiName = '', mdApiName = '', masterData = {}} = {}, param) {
        let {product_price} = this.getAllFields(mdApiName);
        let modifyIndex = [];
        let dataObj = {};
        _.each(data, function (item, index) {
            let k = item.rowId || index;
            modifyIndex.push(k);
            dataObj[k] = item;
        });
        let detailDataMap = {
            [mdApiName]: dataObj
        };
        let calFields = param.dataGetter.getCalculateFieldsByFieldName(product_price, false, mdApiName);
        delete calFields[masterApiName];
        let calFieldsOrder = param.dataGetter.getCalculateFieldsOrder(calFields);
        return {
            masterObjectApiName: masterApiName,
            masterData: masterData,
            detailDataMap: detailDataMap,
            calculateFieldApiNames: calFields,
            modifiedObjectApiName: mdApiName,
            modifiedDataIndexList: modifyIndex,
            calculateFields: calFieldsOrder
        }
    }

    // server计算产品包价格；
    requestBomPrice(param) {
        // let url = `FHH/EM1HNCRM/API/v1/object/bom/service/query_bom_price`;
        let url = `FHH/EM1HNCRM/API/v1/object/bom/service/batch_query_bom_price`;
        return PPM.ajax(this.parent.request, url, Object.assign({}, {
            "rootBomId": "", // 根结点bom_id
            "accountId": "",
            "partnerId": "",
            // "bomList": []
        }, param))
    }

    // 处理子产品的价目表折扣，取根结点的价目表折扣;
    addDiscountForChildren(children = [], rootData = {}) {
        const {
            price_book_discount
        } = this.getAllFields();
        children.forEach(c => {
            if (!c.isGroup) c[price_book_discount] = rootData[price_book_discount];
        });
    }

    // 是否支持分摊，描述中有share_rate就代表支持分摊
    isSupportShareRate(mdApiName, param) {
        let des = this.getDescribe(mdApiName, param);
        let {
            share_rate
        } = this.getAllFields(mdApiName);
        return !!des[share_rate];
    }

    _getBasicFieldsForTempNode(mdApiName){
        const {
            price_editable,
            amount_editable,
            price_mode,
            min_amount,
            max_amount,
            increment,
            amount_any,
        } = this.getAllFields(mdApiName);
        return [price_editable, amount_editable, price_mode, min_amount, max_amount, increment, amount_any];
    }

    // 获取bom对象的某些字段默认值，临时子件要用；
    getBomBasicFields(children = []) {
        if(this._cloneBomBasicFields ) return this._cloneBomBasicFields;
        const {
            price_editable,
            amount_editable,
            price_mode,
            min_amount,
            max_amount,
            increment,
            amount_any,
        } = this.getAllFields();
        let cloneBomBasicFields;
        if (this.isOpenTempNode()) {
            let editableField = [price_editable, amount_editable, amount_any];
            let cloneFields = [price_editable, amount_editable, price_mode, min_amount, max_amount, increment, amount_any];
            let tempNode = children.find(item => this.isTempNode(item));
            if (tempNode) {
                cloneBomBasicFields = {};
                cloneFields.forEach(f => {
                    if(editableField.includes(f)){
                        cloneBomBasicFields[f + '__v'] = tempNode[f];
                        cloneBomBasicFields[f] = tempNode[f] ? this.parent.i18n('是') : this.parent.i18n('否');
                    }else if(f === price_mode){
                        cloneBomBasicFields[f] = tempNode[f] == '1' ? this.parent.i18n('配置价格') : this.parent.i18n('价目表价格');
                    }else{
                        cloneBomBasicFields[f] = tempNode[f];
                    }
                })
            }
        }
        this._cloneBomBasicFields = cloneBomBasicFields;
        return cloneBomBasicFields;
    }

    // 合并过滤字段
    _mergeFilterFields(filterFields, param){
        if(param.filterFields){
             PPM.each(param.filterFields, (obj, apiname) => {
                 if(filterFields[apiname]){
                     filterFields[apiname] = filterFields[apiname].concat(obj)
                 }else{
                     filterFields[apiname] = obj;
                 }
             })
        }
        return filterFields;
    }

    async calculateChildren({
                                mdApiName = '',
                                children = [],
                                changeFields = [],
                                extraFields = [],
                                operateType = 'mdAdd',
                                ignoreCalFields = [],
                                triggerUIEvent = false
                            } = {}, param) {
        let _this = this;
        let nc = children.filter(c => !c.isGroup);
        if (!nc.length) return;
        let modifyIndex = nc.map(item => item.rowId);
        let filterFields = {
            [mdApiName]: ignoreCalFields
        };
        filterFields = this._mergeFilterFields(filterFields, param);
        let o = {
            operateType: operateType,
            dataIndex: modifyIndex,
            objApiName: mdApiName,
            filterFields,
            extraFields: {
                [mdApiName]: extraFields
            },
            parseParam: function (obj) {
                return _this.filterCalFields(obj, mdApiName);
            }
        };
        if (changeFields.length) o.changeFields = changeFields;
        if(operateType === 'mdAdd') o.newDataIndexs = modifyIndex;
        triggerUIEvent ? await param?.triggerCalAndUIEvent?.(o) : await param?.triggerCal?.(o);
    }

    // 缓存一个临时子件数据；
    _cacheTempNode(children){
        if(this.isOpenTempNode()){
            let temp = children.find(item => this.isTempNode(item));
            if(temp){
                this._cacheOneTempNode = PPM.deepClone(temp);
            }
        }
    }

    // 重置临时子件的字段默认值；
    resetTempNodeFields(children, param) {
        if(this.parent.isGrayDeleteRoot()) return;
        let bomBasicFields = this.getBomBasicFields(children);
        if (!bomBasicFields) return;
        children.forEach(item => {
            if (this.isTempNode(item)) {
                item = Object.assign(item, bomBasicFields);
                if(param) {
                    param.dataUpdater.updateDetail(param.objApiName, item.rowId, bomBasicFields);
                }
            }
        })
    }

    // 更新parent_rowId；注意：此处 children 为树状结构，非平铺；
    updateParentRowId(children, rootData) {
        PPM.forEachTreeData(children, item => {
            if (item.pid) item.parent_rowId = item.pid;
        });
        children.forEach(item => {
            item.parent_rowId = item.pid = rootData.rowId;
        });
    }

    /**
     * @desc 处理新添加的子产品数据
     * @param newChildren 添加的子节点数据
     * @param rootData 根节点数据
     * @param mdApiName 从对象apiname
     * @param recordType 业务类型
     * @param param
     * @returns [{}]
     */
    async parseNewChildren({
                               newChildren = [],
                               rootData = {},
                               mdApiName = '',
                               recordType = ''
                           } = {}, param) {
        // bom 对象上的一些字段；
        let usefulFields = ['parent_bom_id', 'adjust_price', 'amount', 'modified_adjust_price', 'isGroup', 'rowId', 'parent_rowId', 'price_editable', 'amount_editable', 'amount_any', 'order_field', '__insertRowId', '__isNew', '__adjust_price', 'attribute_price_book_lines_ids','price_per_set', 'pricing_period',];
        // 明细上需要回填的一些字段；
        let usefulFields_md = [
            'core_id', 'new_bom_path', 'node_bom_core_type', 'node_bom_core_version', 'related_core_id',
            'product_id', 'bom_id', 'product_group_id', 'price_book_id', 'price_book_product_id', 'share_rate', 'node_discount', 'node_price', 'node_subtotal', 'node_type', 'price_mode', 'pricing_mode', 'pricing_period'];
        // 带__r的字段；
        let fields__r = [
            'product_id', 'parent_bom_id', 'bom_id', 'price_book_id', 'price_book_product_id'];
        // 引用字段；
        let fields__c = ['product_group_id'];
        // 添加数据之前，处理bom子产品数据勾子；支持保留某些特殊字段到明细；
        let r = await this.parent.runPlugin('bom.addChildren.before', {
            param
        });
        let otherFields = [];
        if (r && r.usefulFields) otherFields = otherFields.concat(r.usefulFields);
        if (otherFields && otherFields.length) usefulFields = usefulFields.concat(otherFields);
        const {actual_unit} = this.getMultiUnitPluginFields(mdApiName);
        let allFields = this.getAllPluginFields(mdApiName);
        let {
            product_id,
            product_price,
            price_book_discount,
            quantity,
            discount,
            product_group_id,
            temp_node_group_id,
            node_no,

            bom_core_id,
            bom_type,
            bom_version,
            related_core_id,
            new_bom_path,
            amount_any,
            price_per_set,
            pricing_period
        } = allFields;

        let basicData = param.getRowBasicData(mdApiName, recordType);
        if (!rootData.hasOwnProperty(quantity)) rootData[quantity] = '1';
        // let rootQuantity = rootData[quantity];
        let nc = newChildren.map(c => {
            let o = {};
            usefulFields.forEach(k => {
                if (c.hasOwnProperty(k)) {
                    o[k] = c[k];
                    if (fields__r.includes(k)) o[k + '__r'] = c[k + '__r'];
                }
            });
            usefulFields_md.forEach(k => {
                if (c.hasOwnProperty(k)) {
                    let nk = allFields[k];
                    if(!nk) return;
                    o[nk] = c[k];
                    if (fields__r.includes(k)) o[nk + '__r'] = c[k + '__r'];
                    if (fields__c.includes(k)) o[nk] = c[k + '__r'];
                    if (fields__c.includes(k)) o[nk + '__v'] = c[k];
                }
            });
            if (c.isGroup) {
                return Object.assign({
                    isFake: true,
                    record_type: basicData.record_type,
                    object_describe_api_name: basicData.object_describe_api_name,
                }, o);
            } else {
                if(c.related_core_id){
                    o[related_core_id] = o[bom_core_id] = c.related_core_id; // 复用bom
                }
                o[new_bom_path] = c.new_bom_path;

                // 处理子产品价格；
                o[product_price] = PPM.hasValue(c.modified_adjust_price) ? c.modified_adjust_price : c.adjust_price;

                // 周期性产品
                if(price_per_set){
                    o[price_per_set] = c.price_per_set;
                    o[pricing_period] = c.pricing_period;
                }

                // 子产品的数量 = 每个包中的子产品数量 * 产品包数量；
                o.defQuantity = PPM.hasValue(o.amount) ? o.amount : 0;

                if(c.amount_any) o[quantity] = o.defQuantity;
                // 子产品的价格表折扣取根结点的价目表折扣；
                o[price_book_discount] = o[discount] = rootData[price_book_discount];
                o.root_product_id = rootData[product_id];
                o.root_product_id__r = rootData[product_id + '__r'];
                o.isChecked = rootData.isChecked;
                o._isChildren = true;

                if (!this.isSupportShareRate(mdApiName, param) && !this.noClearSalesPrice()) o[discount] = null;
                // 临时子件补充序号和分组id
                if(c.node_type === 'temp'){
                    o[temp_node_group_id] = o[product_group_id + '__v'];
                    o[temp_node_group_id + '__v'] = o[product_group_id];
                }
                o[node_no] = c.order_field;
                // 处理多单位字段
                if (actual_unit) o[actual_unit] = c.unit_id;
                return Object.assign({}, basicData, o);
            }
        });
        this.parsePriceDecimalPlaces(nc, mdApiName, param);
        this.resetTempNodeFields(nc, param);

        if(!this.parent.isGrayDeleteRoot()){
            this.setFieldsReadonly({
                data: nc,
                mdApiName,
                recordType
            }, param);
            this.setBomSpecialFieldsVal({
                data: nc,
                mdApiName
            }, param);
            this.updateProdKey(nc, rootData);
        }else{
            // 灰了不回填根结点
            this.deleteChildrenProdKey(nc);
            nc = nc.filter(item => !item.isGroup);
        }
        let r1 = await this.parent.runPlugin('bom.parseNewChildren.after', {
            newChildren: nc,
            mdApiName,
            param,
        });
        if(r1 && r1.data) nc = r1.data;
        return nc;
    }

    findDataByRowId(rowId, data) {
        return data.find(item => item.rowId === rowId);
    }

    /**
     * @desc 更新虚拟key;  PS：改动方法注意影响临时子件
     * @param data 子产品数据或者含根节点的包数据
     * @param rootData 根节点数据
     */
    updateProdKey(data = [], rootData = {}, mdApiName = '', param) {
        if(this.parent.isGrayDeleteRoot()) return;
        let allFields = this.getAllFields();
        const {
            prod_pkg_key,
            parent_prod_pkg_key,
            root_prod_pkg_key,
            bom_id,
        } = allFields;

        rootData[prod_pkg_key] = rootData.rowId;
        if(rootData[bom_id]) rootData[root_prod_pkg_key] = rootData.rowId;
        
        // param.dataUpdater.updateDetail(mdApiName, rootData.rowId, {
        //     [prod_pkg_key]: rootData.rowId,
        //     [root_prod_pkg_key]: rootData.rowId,
        // });
        PPM.forEachTreeData(data, item => {
            if (PPM.isBom(item, allFields).isPackage && item[bom_id] || item.parent_rowId) {
                item[prod_pkg_key] = item.rowId;
                item[root_prod_pkg_key] = rootData.rowId;
            }
            if (item.parent_rowId) {
                let parent = this.findDataByRowId(item.parent_rowId, data);
                item[parent_prod_pkg_key] = parent && parent.isGroup ? parent.parent_rowId : item.parent_rowId;
            }
        })
    }

    /**
     * @desc 更新BOM；二次配置或编辑子产品;
     * @param newChildren // 新子产品数据
     * @param newRootData // 新根节点数据
     * @param rootDataBomId // 根节点bomId
     * @param rootDataPrice // 新根节点价格
     * @param rootData // 旧根节点数据
     * @param mdApiName
     * @param recordType
     * @param noUpdateAttr 不更新属性
     * @param originalTotalMoney 产品包原始金额
     * @param param
     * @returns {Promise<Array>} children 数据
     */
    async updateBom({
                        newChildren = [],
                        newRootData = {},
                        rootDataBomId = '',
                        rootDataCoreId = '',
                        rootDataPrice = '',
                        rootData = {},
                        mdApiName = '',
                        recordType = '',
                        noUpdateAttr,
                        originalTotalMoney,
                        noCalBomPrice = false,
                        from = '',
                    } = {}, param) {
        let rd = await this.updateRootData({
            rootData,
            newRootData,
            rootDataPrice,
            rootDataBomId,
            rootDataCoreId,
            originalTotalMoney,
            mdApiName
        }, param);
        let children = await this.updateBomChildren({
            newChildren,
            rootData,
            mdApiName,
            recordType,
            noUpdateAttr,
            noCalBomPrice,
            from
        }, param);
        this.updateProdKey(children, rd);
        await this.parent.runPlugin('bom.updateBom.after', {
            data: [rd].concat(children),
            mdApiName,
            param
        });
        return {
            'newRootData': rd,
            'newChildren': children
        }
    }

    /**
     * @desc 更新根节点
     * @param rootData
     * @param newRootData
     * @param rootDataPrice
     * @param rootDataBomId
     * @param mdApiName
     * @param param
     */
    async updateRootData({
                             rootData = {},
                             newRootData = {},
                             rootDataPrice = '',
                             rootDataBomId = '',
                             rootDataCoreId = '',
                             originalTotalMoney = '', // 产品包原始金额
                             mdApiName = ''
                         } = {}, param) {
        const {
            bom_id,
            product_price,
            price_book_discount,
            price_book_id,
            price_book_product_id,
            bom_core_id,
            price_per_set,

        } = this.getAllPluginFields(mdApiName);
        let up = {};
        up[product_price] = rootDataPrice;
        if(price_per_set) up[price_per_set] = newRootData.price_per_set;
        if(PPM.hasValue(originalTotalMoney)) up.__originalTotalMoney = originalTotalMoney;
        if (Number(rootData[product_price]) !== Number(rootDataPrice)) this._rootPriceChange?.push(rootData.rowId);
        this.parsePriceDecimalPlaces(up, mdApiName, param);
        if (!rootData[bom_id]) up[bom_id] = rootDataBomId;
        if (!rootData[bom_core_id]) up[bom_core_id] = rootDataCoreId;
        if (rootData.hasOwnProperty(price_book_id) && newRootData && newRootData.pricebook_id) {
            if (rootData[price_book_id] !== newRootData.pricebook_id) {
                this._price_book_id_change = true; // 价目表有变化，需要计算折扣
            }
            up[price_book_id] = newRootData.pricebook_id;
            up[price_book_id + '__r'] = newRootData.pricebook_id__r;
            up[price_book_product_id] = newRootData.price_book_product_id || newRootData._id;
            up[price_book_product_id + '__r'] = newRootData.price_book_product_id__r || newRootData.name;
            if (newRootData.hasOwnProperty('discount')) up[price_book_discount] = newRootData.discount;
        }
        param.dataUpdater.updateDetail(mdApiName, rootData.rowId, up);
        await this.parent.runPlugin('bom.updateRootData', {
            rootData,
            newRootData,
            mdApiName,
            param
        });
        return param.dataGetter.getData(mdApiName, rootData.rowId);
    }

    // 根据描述格式化价格和配置价格小数位
    parsePriceDecimalPlaces(data = [], mdApiName = '', param) {
        let {product_price, node_price, quantity, price_per_set} = this.getAllPluginFields(mdApiName);
        let des = this.getDescribe(mdApiName, param);
        let nodePriceDec = des[node_price] && des[node_price].decimal_places;
        let priceDec = des[product_price] && des[product_price].decimal_places;
        let quantityDec = des[quantity] && des[quantity].decimal_places;
        let price_per_setDec = des[price_per_set] && des[price_per_set].decimal_places;
        data = PPM.isArray(data) ? data : [data];
        data.forEach(item => {
            if (PPM.hasValue(priceDec) && PPM.hasValue(item[product_price]) && !item.isGroup) item[product_price] = PPM.formatDecimalPlace(item[product_price], priceDec);
            if (PPM.hasValue(nodePriceDec) && PPM.hasValue(item[node_price]) && !item.isGroup) item[node_price] = PPM.formatDecimalPlace(item[node_price], nodePriceDec);
            if (PPM.hasValue(quantityDec) && PPM.hasValue(item[quantity]) && !item.isGroup) item[quantity] = PPM.formatDecimalPlace(item[quantity], quantityDec);
            if (PPM.hasValue(price_per_setDec) && PPM.hasValue(item[price_per_set]) && !item.isGroup) item[price_per_set] = PPM.formatDecimalPlace(item[price_per_set], price_per_setDec);
        })
    }

    /**
     * @desc 新数据匹配老数据；兼容new_bom_path 和 bom_id
     * @param newRow
     * @param oldData
     * @param fields 映射字段
     * @returns {number | * | BigInt | T}
     * @private
     */
    _findOld(newRow = {}, oldData = [], fields = {}){
        let {new_bom_path, bom_id} = fields;
        return oldData.find(r => r[new_bom_path] ? r[new_bom_path] === newRow.new_bom_path : r[bom_id] === (newRow.bom_id || newRow._id));
    }

    /**
     * @desc 更新BOM子节点数据
     * @param newChildren 子节点数据
     * @param rootData 根节点数据
     * @param mdApiName 从对象apiname
     * @param recordType 业务类型
     * @param from 来源 主要是用来区分手动二次配置还是querybom更新
     * @param param 插件参数
     * @returns {Promise<Array>}
     */
    async updateBomChildren({
                                newChildren = [],
                                rootData = {},
                                mdApiName = '',
                                recordType = '',
                                from = '',
                                noCalBomPrice = false,
                            } = {}, param) {
        let _this = this;
        let res = [];
        let newAddRow = [];
        let allData = param.dataGetter.getDetail(mdApiName);
        let oldChildrenData = PPM.getChildren({
            rowId: rootData.rowId,
            details: allData
        });
        let fieldMap = this.getAllFields(mdApiName);
        let {
            bom_id,
            product_id,
            product_price,
            quantity,
            node_discount,
            node_price,
            share_rate,
            node_subtotal,
            discount,
            sales_price,
            price_book_discount,
            price_book_id,
            price_book_product_id,
            new_bom_path,
            product_group_id,
            related_core_id,
            bom_version,


        } = fieldMap;
        let periodProductFields = this.getPeriodProductPluginFields(mdApiName);

        this.updateParentRowId(newChildren, rootData);
        await this.deleteUnselectedChildren(oldChildrenData, newChildren, mdApiName, param, from);
        this.addInsertMark(newChildren, rootData, oldChildrenData);

        async function _fn(children, parent) {
            let nc = {};
            for (let c of children) {
                let findOld;
                // 二次配置匹配数据
                if (from === 'secondConfig' || c.isGroup) {
                    // 临时子件需要用rowId匹配，普通的用bom_id 匹配
                    findOld = _this.isTempNode(c) ? findOld = oldChildrenData.find(r => r.rowId === c.rowId) : findOld = _this._findOld(c, oldChildrenData, fieldMap);
                } else {
                    // querybom接口匹配数据用虚拟key
                    findOld = oldChildrenData.find(r => r.rowId === c.prod_pkg_key);
                }

                // 有旧数据，则只替换价格、默认数量、children，重新计算数量；
                if (findOld) {
                    let up = {};
                    if (!findOld.isGroup) {
                        if(!noCalBomPrice){
                            up.defQuantity = c.amount;
                            if(c.amount_any) up[quantity] = up.defQuantity;
                            up[product_id] = c.product_id;
                            up[product_id + '__r'] = c.product_id__r;
                            up.root_product_id = rootData[product_id];
                            up.root_product_id__r = rootData[product_id + '__r'];
                            up[bom_id] = c.bom_id;
                            up[product_price] = up.modified_adjust_price = c.modified_adjust_price;
                            up[new_bom_path] = c.new_bom_path;
                            up.__adjust_price = c.__adjust_price;
                            if(c.related_core_id) up[related_core_id] = c.related_core_id;
                            if(c.node_bom_core_version) up[bom_version] = c.node_bom_core_version;

                            // 如果定价模式是 价目表价格，则回填子节点的价目表
                            if (c.price_mode == '2') {
                                up[price_book_id] = c.price_book_id;
                                up[price_book_id + '__r'] = c.price_book_id__r;
                                up[price_book_product_id] = c.price_book_product_id;
                                up[price_book_product_id + '__r'] = c.price_book_product_id__r;
                            } else {
                                up[price_book_id] = up[price_book_id + '__r'] = up[price_book_product_id] = up[price_book_product_id + '__r'] = null;
                            }
                            // 周期性产品
                            if(periodProductFields){
                                let {price_per_set, pricing_period, pricing_mode } = periodProductFields;
                                up[price_per_set] = c.price_per_set;
                                up[pricing_period] = c.pricing_period;
                                // if(c.product_id__ro) up[pricing_mode] = c.product_id__ro.pricing_mode;
                            }
                        }

                        // 描述有分摊比例字段；
                        if (_this.isSupportShareRate(mdApiName, param)) {
                            if (c.hasOwnProperty(share_rate)) up[share_rate] = c.share_rate;
                            // 有分摊比例的子产品才回填这些字段；
                            [node_subtotal, discount, sales_price, node_discount].forEach(f => {
                                if (c.hasOwnProperty(f)) up[f] = PPM.hasValue(c[share_rate]) || _this.noClearSalesPrice() ? c[f] : null;
                            });
                            up[node_price] = c.hasOwnProperty('node_price') ? c.node_price : c.adjust_price;
                            if (rootData.hasOwnProperty(price_book_discount)) up[price_book_discount] = rootData[price_book_discount];
                        }
                    }
                    _this.parsePriceDecimalPlaces(up, mdApiName, param);

                    // 如果是分组，只更新productId
                    if (findOld.isGroup) {
                        up[product_id] = c.product_id;
                    }
                    nc = findOld;
                    await _this.parent.runPlugin('bom.updateBomChildren', {
                        newChildren: c,
                        oldChildren: findOld,
                        mdApiName,
                        param
                    });
                    param.dataUpdater.updateDetail(mdApiName, findOld.rowId, up);
                    res.push(findOld);
                } else {
                    nc = await _this.parseNewChildren({
                        newChildren: [c],
                        rootData,
                        mdApiName,
                        recordType
                    }, param);
                    nc = nc[0];
                    nc.parent_rowId = parent.rowId;
                    newAddRow.push(nc);
                }
                if (c.children && c.children.length) await _fn(c.children, nc)
            }
        }

        await _fn(newChildren, rootData);

        if (newAddRow.length) {
            let nc = this.insertNewAddData(newAddRow, mdApiName, param);
            res = res.concat(nc);

            // 处理子件数量；
            this.setSubQuantity({
                allData: [].concat(res).concat([rootData]),
                rootQuantity: rootData[quantity],
                mdApiName,
            }, param);

            let ignoreCalFields = [product_price, quantity, discount, product_group_id ];
            await this.calculateChildren({
                mdApiName,
                children: newAddRow,
                ignoreCalFields,
                triggerUIEvent: true
            }, param);
            this.resetTempNodeFields(newAddRow, param);
        } else {
            // 处理子件数量；
            this.setSubQuantity({
                allData: [].concat(res).concat([rootData]),
                rootQuantity: rootData[quantity],
                mdApiName,
                noAlert: true,
            }, param);
        }
        this.updateProdKey(res, rootData);
        return res;
    }

    __parseDataToTree(data = []) {
        let childrenArr = [];
        let root = data.filter(item => {
            let f = data.find(d => d.rowId === item.parent_rowId);
            if (f) {
                childrenArr.push(item);
            } else {
                return item;
            }
        });

        function findChildren(parent_rowId) {
            return childrenArr.filter(item => {
                return item.parent_rowId == parent_rowId;
            })
        }

        function toTree(list) {
            list.forEach(item => {
                if (!PPM.isObject(item)) return;
                let children = findChildren(item.rowId);
                if (children.length) {
                    item.children = children;
                    toTree(item.children)
                }
            })
        }

        toTree(root);
        return root;
    }

    insertNewAddData(newAddRow, mdApiName, param) {
        let res = [];
        let nData = this.__parseDataToTree(newAddRow);
        let obj = nData.reduce((result, item) => {
            if(item.__insertRowId){
                if(!result[item.__insertRowId]) result[item.__insertRowId] = [];
                result[item.__insertRowId].push(item);
            }
            return result;
        }, {});

        PPM.each(obj, (arr, key) => {
            let d = PPM.parseTreeToNormal(arr) ;
            param.dataUpdater.insert(mdApiName, key, d);
            res = res.concat(d);
        });
        return res;
    }

    // 添加插入行位置标记；需要分成组来插入
    addInsertMark(newChildren, rootData, oldChildrenData) {
        if (!newChildren) return;
        let fieldMap = this.getAllFields();
        PPM.forEachTreeData(newChildren, c => {
            let findOld;
            if (this.isTempNode(c)) {
                findOld = oldChildrenData.find(r => r.rowId === c.rowId);
            } else {
                findOld = this._findOld(c, oldChildrenData, fieldMap);
            }
            findOld ? c.rowId = findOld.rowId : c.__isNew = true;
        });

        function _fn(list, pData) {
            if (!Array.isArray(list)) return;
            list.forEach((item, index) => {
                if (!PPM.isObject(item)) return;
                if (!index) {
                    item.__insertRowId = pData.rowId;
                } else {
                    let last = list[index - 1];
                    item.__insertRowId = last.__isNew ? last.__insertRowId : last.rowId;
                }
                if (item.children && !item.__isNew) _fn(item.children, item);
            })
        }

        _fn(newChildren, rootData)
    }

    // 是否是临时子件
    isTempNode(row) {
        let {node_type} = this.getAllFields();
        return row[node_type] === 'temp' || row.node_type === 'temp';
    }

    // 删除已经取消勾选得子产品
    async deleteUnselectedChildren(oldChildren = [], newChildren = [], mdApiName = '', param, from = '') {
        let {
            bom_id,
            new_bom_path,
        } = this.getAllFields();
        let r = [];
        newChildren = PPM.parseTreeToNormal(newChildren);
        oldChildren.forEach(item => {
            let f;
            if (this.isTempNode(item)) {
                // 手动二次配置产品包，需要判断临时子件是否还保留；如果是querybom，就不用管临时子件了
                if (from === 'secondConfig') {
                    f = newChildren.find(c => c.rowId === item.rowId);
                } else {
                    return;
                }
            } else {
                f = newChildren.find(c => item[new_bom_path] ? c.new_bom_path === item[new_bom_path] : c.bom_id === item[bom_id]);
            }
            if (!f){
                r.push(item);
                param.dataUpdater.del(mdApiName, item.rowId);
            }
        });
        if(r.length){
            let p = {
                operateType: 'mdDel',
                // dataIndex: r,
                objApiName: mdApiName,
                delDatas: r,
            };
            // 删除子件触发计算
            await param.triggerCalAndUIEvent(p);
        }
    }

    getMdData(mdApiName, rowId, param) {
        rowId = Array.isArray(rowId) ? rowId : [rowId];
        return rowId.map(r => {
            return param?.dataGetter?.getData?.(mdApiName, r)
        })
    }

    // 如果修改行>1，则表示是批量编辑，需要过滤掉子产品；
    filterChangeRow(changeRow) {
        if (changeRow.length > 1) {
            return changeRow.filter(item => !item.parent_rowId);
        }
        return changeRow;
    }

    // 校验是否已经计算过产品包；
    checkRootData(rootData, bomList) {
        let f = bomList.find(item => item.rowId === rootData.rowId);
        if (!f) bomList.push(rootData);
        return !f;
    }

    // 单元格编辑后；
    async _editAfter(plugin, param) {
        await this._editAfterTodo.apply(this, arguments);
    }

    // 单元格编辑之后，底层已经走完
    async _editEnd(plugin, param) {
        // delete param.filterFields;
        // await this._editEndTodo.apply(this, arguments);
        // await this._triggerCalAfterUIEvent(plugin, param, 'edit');
    }

    // 根节点的数量变化后，子产品的数据需要跟着变
    setChildrenQuantityAndCalculate({rootData = {}, mdApiName = ''} = {}, param, plugin) {
        let {quantity} = this.getAllFields(param);
        let details = param.dataGetter.getDetail(mdApiName);
        let children = PPM.getChildren({
            rowId: rootData.rowId,
            details
        });
        let rq = param.changeData[rootData.rowId][quantity];
        let r = this.setSubQuantity({
            allData: [].concat(children).concat([rootData]),
            rootQuantity: rq,
            mdApiName
        }, param, plugin);
        return {
            children,
            res: r
        };
    }

    // 编辑后事件
    async _editAfterTodo(plugin = {}, param = {}) {
        if(this.parent.isGrayDeleteRoot()) return;
        let mdApiName = param.objApiName;
        let allFields = this.getAllFields(mdApiName);
        let {
            discount,
            sales_price,
            subtotal,
            dynamic_amount,
            quantity,
            node_discount,
            price_book_id,
            product_price,
            node_price,
            root_prod_pkg_key,
            parent_prod_pkg_key,
            product_id,
        } = allFields;
        // let changeIndex = Object.keys(param.changeData);
        let changeRow = this.getMdData(mdApiName, param.dataIndex, param);
        let details = param?.dataGetter?.getDetail(mdApiName);
        let masterData = param?.dataGetter?.getMasterData();
        let masterApiName = param.masterObjApiName;
        let recordType = param.recordType;
        let needCal = [product_price, quantity, node_discount];
        let triggerShareFields = [discount, sales_price, subtotal, quantity, dynamic_amount];
        let changeField = param.fieldName;
        let bomList = [];
        let needCalData = [];
        let allCalData = [];
        let r1 = this._mergeChangeRow(changeRow, changeField, param);
        if(r1 && r1.changeField) changeField = r1.changeField;

        for (let i = 0; i < changeRow.length; i++) {
            let item = changeRow[i];
            if(!item) return;
            param.dataUpdater.updateDetail(mdApiName, item.rowId, param.changeData[item.rowId]);
            allCalData.push(item);
            // 修改产品包的数据，需要同步计算包中的子产品数量
            if (PPM.isBom(item, allFields).isPackage && !item.parent_rowId) {
                if (changeField === quantity) {
                    let o = this.setChildrenQuantityAndCalculate({rootData: item, mdApiName}, param, plugin);
                    if (!o.res) return plugin.skipPlugin();
                    allCalData = allCalData.concat(o.children);
                }
                // 如果改包相关字段，需要触发分摊
                if (this.isSupportShareRate(mdApiName, param) && triggerShareFields.includes(changeField)) {
                    needCalData.push(item);
                    bomList.push(item);
                }
            } else if (item.parent_rowId && !item.isGroup) {  // 编辑的是子产品
                let newVal = item[changeField];
                let rootData = param.dataGetter.getData(mdApiName, item[root_prod_pkg_key]);
                // 需要校验数量规则，如果校验通过，需要更新defQuantity;
                if (changeField === quantity) {
                    let r = this.checkSubQuantity({
                        num: newVal,
                        rowData: item,
                        mdApiName,
                        rootData
                    }, param);
                    if (!r) return plugin.skipPlugin();
                }
                // 编辑选配折扣，需要计算子产品价格；
                param.dataUpdater.updateDetail(mdApiName, item.rowId, {
                    [changeField]: newVal
                });
                // 改选配折扣需要计算子产品价格
                if (changeField === node_discount) {
                    let val = PPM.multiplicational(item[node_price], newVal / 100);
                    param.dataUpdater.updateDetail(mdApiName, item.rowId, {
                        [product_price]: val
                    });
                }
                // 需要重新计算BOM
                if (needCal.includes(changeField)) {
                    needCalData.push(rootData);
                    bomList.push(item);
                }
            }
            
            // 如果改的是产品包，需要更新产品包的数据
            if(changeField === product_id){
                let lookUpRootData = param.lookupData;
                let currentRow = item;
                // 如果切换之后没有根节点数据，则需要删除子件
                if(!lookUpRootData){
                    this.deleteChildren({currentRow, mdApiName, details, allFields, param});
                }
                if(lookUpRootData && PPM.isBom(lookUpRootData).isPackage){
                    let nc = await this.changeProductId({lookUpRootData, currentRow, mdApiName, allFields, recordType, details, param});
                    if(nc?.length){
                        allCalData = allCalData.concat(nc);
                    }
                    needCalData.push(currentRow);
                    bomList.push(currentRow);
                    // 设置产品组合字段的编辑性
                    this.setFieldsReadonly({
                        data: [currentRow].concat(nc || []),
                        mdApiName,
                        recordType
                    }, param);
                }
            }
        }   

        await this.editCalBom({needCalData, changeRow: bomList, allCalData, mdApiName,  masterData, masterApiName, recordType, changeField, param});

        if (changeField === price_book_id) this.setChildrenPriceBookId(changeRow, details, mdApiName, param);

        let children = allCalData.filter(item => item[parent_prod_pkg_key]);
        await this.parent.runPlugin('bom.md.edit.after', {
            param,
            list: allCalData,
            children,
            objectApiName: masterApiName,
            detailObjectApiName: mdApiName,
        });
    }

    // 删除某行的子件
    deleteChildren({currentRow, mdApiName, details, allFields, param} = {}){   
        if(PPM.isBom(currentRow, allFields).isPackage){
            let children = PPM.getChildren({
                rowId: currentRow.rowId,
                details
            });
            if(children?.length){
                param.dataUpdater.del(mdApiName, children.map(c => c.rowId));
            }
        }
    }   

    // 修改产品id,需要更新产品包的数据
    async changeProductId({lookUpRootData = {}, currentRow = {}, mdApiName = '', allFields = {}, recordType = '', details = [], param} = {}){
        if(lookUpRootData && PPM.isBom(lookUpRootData).isPackage){
            // 如果当前行是产品包，则删除他的所有子件
            this.deleteChildren({currentRow, mdApiName, details, allFields, param});

            // 更新产品包的数据
            this.updateRootDataForAdd({lookUpRootData, rootData: currentRow, mdApiName, updateAll: true, param});
            // 如果产品包有子件，add 子件数据
            if (lookUpRootData.children && lookUpRootData.children.length) {
                let newChildren = [];
                // 更新pid
                this.updateParentRowId(lookUpRootData.children, currentRow);
                // 子产品补字段；
                newChildren = PPM.parseTreeToNormal(lookUpRootData.children);
                newChildren = await this.parseNewChildren({
                    newChildren, // usefulFields_md
                    rootData: currentRow,
                    mdApiName,
                    recordType
                }, param);
                // 添加子件数据
                let nc = param.dataUpdater.add(newChildren);
                return nc;
            }
        }
    }

    // 编辑计算和改参数
    async editCalBom({needCalData, changeRow, allCalData, mdApiName, masterData, masterApiName, recordType, changeField, param} = {}){
        if (needCalData.length) {
            needCalData = PPM.uniq(needCalData, item => item.rowId);
            allCalData = PPM.uniq(allCalData, item => item.rowId);
            await this.calculateBomPrice({
                data: needCalData,
                changeRow,
                mdApiName,
                masterData,
                masterApiName,
                recordType,
                changeField,
                from: 'edit',
                triggerUIEvent: false,
                // noTriggerCal: true,
            }, param);
        }
        this._changeCalParam({
            allCalData,
            mdApiName,
        }, param);
    }

    // 合并报价器更改行，如果报价器改了数量，以数量为主触发点；
    _mergeChangeRow(changeRow, changeField, param){
        let mdApiName = param.objApiName;
        let {quantity, product_price} = this.getAllFields(mdApiName);
        let details = param.dataGetter.getDetail(mdApiName);
        let f = details.filter(item => item._isChangedByQuoter);
        if(!f.length) return;
        f.forEach(item => {
            let f = changeRow.find(c => c.rowId === item.rowId);
            if(!f) changeRow.push(item);
            if(item._isChangeQuantityByQuoter || item._isChangePricingByQuoter){
                changeField = quantity;
            }else {
                changeField = product_price;
            }
        });
        return {
            changeField,
        }
    }

    // 设置子节点的价目表字段；取根结点的价目表字段；
    setChildrenPriceBookId(changeRow = [], details = [], mdApiName = '', param) {
        let allFields = this.getAllFields(mdApiName);
        let {
            price_book_id
        } = allFields;
        changeRow.forEach(item => {
            // 修改产品包的数据，需要同步计算包中的子产品数量
            if (PPM.isBom(item, allFields).isPackage) {
                let children = PPM.getChildren({
                    rowId: item.rowId,
                    details
                });
                children.forEach(c => {
                    if (!c.isGroup && c.price_mode == '2') {
                        param.dataUpdater.updateDetail(mdApiName, c.rowId, {
                            [price_book_id]: item[price_book_id],
                            [price_book_id + '__r']: item[price_book_id + '__r'],
                        })
                    }
                })
            }
        })
    }


    /**
     * @desc 校验子产品的数量，必须能整除产品包的数量，且需要符合产品包配置的数量规则；
     * @param num 当前数量值
     * @param rowData
     * @param mdApiName
     * @param rootData
     * @param param
     * @returns {boolean}
     */
    checkSubQuantity({
                         num,
                         rowData,
                         mdApiName,
                         rootData
                     } = {}, param) {
        let {
            product_id,
            quantity,
            max_amount,
            min_amount,
            increment,
            amount_any,
            related_core_id,
        } = this.getAllFields(mdApiName);
        if (!PPM.hasValue(num)) return true;
        // 校验必须能整除 父级复用 bom 或者 产品包的数量，优先复用 bom
        let des = this.getDescribe(mdApiName, param, quantity);
        let parentBom = !rowData[amount_any + '__v'] && rowData.__parentBomRowId ? param.dataGetter.getData(mdApiName, rowData.__parentBomRowId) : rootData;
        let dq = rowData[amount_any + '__v'] ? num : PPM.division(Number(num), Number(parentBom[quantity]));
        let decNum = String(dq).split('.')[1]?.length;
        if(des && decNum > des.decimal_places){
            let msg = this.parent.i18n('crm.bom.amount1') + dq + '，' + `【 ${rowData[product_id + '__r']} 】` + this.parent.i18n('产品的数量小数位越界，请重新修改');
            this.pluginService.api.alert(msg);
            return false;
        }
        if (des) dq = PPM.formatDecimalPlace(dq, des.decimal_places);
        // 校验数量规则
        let res = PPM.checkChildrenQuantity({
            data: rowData,
            curAmount: dq,
            $t: this.parent.i18n.bind(this.parent),
            fields: {
                'max_amount': max_amount,
                'min_amount': min_amount,
                'increment': increment,
            }
        });
        if (!res.status) {
            let productId__r = product_id + '__r';
            let m = res.msg;
            // 复用 bom 的子件，提示修改；
            if(rowData.__parentBomRowId){
                if(res.type === '1'){  // 超过单包内子件最大数量
                    m =  this.parent.i18n('crm.bom.amountMax', { // 一个【{{pb}}】内子件【{{child}}】最大数量为{{max}}，请重新修改
                        pb: parentBom[productId__r],
                        child: rowData[productId__r],
                        max: res.max,
                    })
                }else if(res.type === '2'){ // 超过单包内子件最小数量
                    m =  this.parent.i18n('crm.bom.amountMin', { // 一个【{{pb}}】内子件【{{child}}】最小数量为{{min}}，请重新修改
                        pb: parentBom[productId__r],
                        child: rowData[productId__r],
                        min: res.min,
                    })
                }
            }
            this.pluginService.api.alert(m);
            return false;
        }

        // 如果改的是复用 bom 数量，子件数量需要成倍
        if(rowData[related_core_id] && rowData.children?.length){
            let details = param.dataGetter.getDetail(mdApiName);
            let children = PPM.getChildren({
                rowId: rowData.rowId,
                details
            });

            let r = this.setSubQuantity({
                allData: [].concat(children).concat([rowData]),
                rootQuantity: num,
                mdApiName
            }, param);
            if(!r) return false;
        }

        // 校验通过，更新单个包的子产品对应数量
        param.dataUpdater.updateDetail(mdApiName, rowData.rowId, {
            defQuantity: dq
        });
        return true;
    }

    // 校验数量小数位是否越界；
    __validAmountDP(newNum, mdApiName, param){
        let { quantity} = this.getAllFields(mdApiName);
        let des = this.getDescribe(mdApiName, param, quantity);
        let dp = des?.decimal_places;
        let decNum = String(newNum).split('.')[1]?.length || 0;
        return decNum <= dp;
    }

    /**
     * @desc 包的数量变化时，子产品的数量也要变; 子产品数量 = 默认数量 * 包的数量
     * @param allData
     * @param rootQuantity 更改后的数量
     * @param mdApiName
     */
    setSubQuantity({
                       allData = [],
                       rootQuantity,
                       mdApiName = '',
                       noAlert= false,
                   } = {}, param, plugin) {
        let fieldMapping = this.getAllFields(mdApiName);
        let {product_id, amount_any, quantity, related_core_id, is_package} = fieldMapping;
        let des = this.getDescribe(mdApiName, param, quantity);
        let dp = des?.decimal_places;
        let r = [];
        let cloneData = CRM.util.cloneBomData(allData, ['rowId', 'parent_rowId', 'defQuantity', quantity, related_core_id, amount_any, amount_any + '__v', product_id + '__r', is_package, is_package + '__v']);
        cloneData.forEach(item => item[quantity] = rootQuantity);
        CRM.util.setChildrenAmount(cloneData, {
            baseAmount: 'defQuantity',
            amount: quantity,
            amount_any: amount_any,
            related_core_id: related_core_id,
        },  (val, row, parentBom) => {
            let r1 = this.__validAmountDP(val, mdApiName, param);
            if(!r1)  r.push(row[product_id + '__r']);
            val = PPM.formatDecimalPlace(val, dp);  // 格式化小数位
            let f = allData.find(c => c.rowId === row.rowId);
            f[quantity] = val;
            let up = {[quantity]: val};
            if(parentBom) f.__parentBomRowId = up.__parentBomRowId = parentBom.rowId;
            param.dataUpdater.updateDetail(mdApiName, row.rowId, up)
        }, fieldMapping);
        if(r.length && !noAlert) {
            let msg = `【 ${r.join()} 】` + this.parent.i18n('crm.bom.amount3'); // 数量的小数位已四舍五入
            this.pluginService.api.alert(msg);
        }
        return true;
    }

    // 设置Bom数据编辑性
    setFieldsReadonly({
                          data = [],
                          mdApiName = '',
                          recordType = ''
                      } = {}, param) {
        let allFields = this.getAllPluginFields(mdApiName);
        let {
            bom_id,
            node_price,
            node_discount,
            product_price,
            quantity,
            parent_prod_package_id,
            product_id,

            price_book_product_id,
            price_book_id,

            discount,
            sales_price,
            subtotal,
            price_book_price,
            price_book_discount,
            price_editable,
            amount_editable,
            pricing_mode,



        } = allFields;

        let masterApiName = param.masterObjApiName;
        let specialFields = this.getSpecialFields(mdApiName, param).allFields;

        data.forEach(item => {
            // 普通产品的选配价格和选配折扣设为只读；
            if (!item.parent_rowId && !PPM.isBom(item, allFields).isPackage) {
                let fields = [node_price, node_discount, bom_id, parent_prod_package_id, product_id];
                param.dataUpdater.setReadOnly({
                    fieldName: fields,
                    dataIndex: item.rowId,
                    objApiName: mdApiName,
                    recordType: recordType || item.record_type,
                    status: true
                });
            }
            // 产品包的价格、选配价格和选配折扣设为只读；
            if (PPM.isBom(item, allFields).isPackage) {
                param.dataUpdater.setReadOnly({
                    fieldName: [node_price, node_discount, product_price, price_book_product_id, price_book_id, bom_id, parent_prod_package_id, price_book_price, product_id],
                    dataIndex: item.rowId,
                    objApiName: mdApiName,
                    recordType: recordType || item.record_type,
                    status: true
                });
            }
            // 分组都不能编辑；
            if (item.isGroup) {
                param.dataUpdater.setReadOnly({
                    whiteFieldName: [],
                    dataIndex: item.rowId,
                    objApiName: mdApiName,
                    recordType: recordType || item.record_type,
                    status: true
                });
            } else if (item.parent_rowId) {
                let disabledFields = [price_book_product_id, price_book_id, discount, sales_price, subtotal, price_book_price, price_book_discount, bom_id, parent_prod_package_id, product_id, ];
                disabledFields = disabledFields.concat(specialFields);
                // 固定搭配子件字段不允许编辑
                if(this.getFixedCollocationOpenStatus()) {
                    disabledFields = disabledFields.concat(Object.values(allFields));
                }

                disabledFields = _.uniq(disabledFields, f => f);

                if(!(this.isEditForSubPriceOrQuantity(price_editable, item) || this.isEditForSubPriceOrQuantity('price_editable', item))){
                    disabledFields.push(product_price);
                }

                // 周期性产品，且有子件的，价格禁止编辑
                if(pricing_mode && item[pricing_mode] !== 'one'){
                    let hasChildren = data.find(d => d.parent_rowId === item.rowId);
                    if(hasChildren)disabledFields.push(product_price)
                }

                if(!(this.isEditForSubPriceOrQuantity(amount_editable, item) || this.isEditForSubPriceOrQuantity('amount_editable', item))){
                    disabledFields.push(quantity);
                }

                // 子件不允许编辑字段
                param.dataUpdater.setReadOnly({
                    fieldName: disabledFields,
                    dataIndex: item.rowId,
                    objApiName: mdApiName,
                    recordType: recordType || item.record_type,
                    status: true
                });

                  // 自定义对象用黑名单，预置对象用白名单
                // if (masterApiName.includes('__c')) {
                //     param.dataUpdater.setReadOnly({
                //         fieldName: disabledFields,
                //         dataIndex: item.rowId,
                //         objApiName: mdApiName,
                //         recordType: recordType || item.record_type,
                //         status: true
                //     });
                // } else {
                //     param.dataUpdater.setReadOnly({
                //         whiteFieldName: childrenAllowEdit,
                //         dataIndex: item.rowId,
                //         objApiName: mdApiName,
                //         recordType: recordType || item.record_type,
                //         status: true
                //     });
                // }
            }
        });
    }

    // 判断字段值，__v优先
    isEditForSubPriceOrQuantity(field, data) {
        let fv = field + '__v';
        return data.hasOwnProperty(fv) ? data[fv] : data[field];
    }

    // BOM子产品这些字段需要展示为'--'
    getSpecialFields(mdApiName, param) {
        const {
            sales_price,
            selling_price,
            discount,
            subtotal,
            extra_discount,
            extra_discount_amount,
            system_discount_amount,
            sales_amount,
            total_discount,
            base_subtotal,
            base_sales_price,
            base_order_product_amount,
            base_delivery_amount,
            base_sales_amount,
            base_total_amount,
            base_selling_price,
            executed_order_subtotal,
            unexecuted_order_subtotal,
            unexecuted_quantity,
            executed_quantity,
            node_subtotal,
            price_book_price,
            price_book_subtotal,
            policy_dynamic_amount,
            policy_subtotal,
            policy_price,
            policy_discount,
            gift_amortize_price,
            gift_amortize_subtotal
        } = this.getAllFields(mdApiName);
        let res = {
            allFields: [sales_price, selling_price, discount, subtotal, extra_discount, extra_discount_amount, system_discount_amount, sales_amount, total_discount, base_subtotal, base_sales_price,
                base_order_product_amount, base_delivery_amount, base_sales_amount, base_total_amount, base_selling_price,
                /* 销售合同产品 */
                executed_order_subtotal, unexecuted_order_subtotal, unexecuted_quantity, executed_quantity,
                // 780 分摊
                node_subtotal, price_book_price, price_book_subtotal, policy_dynamic_amount, policy_subtotal, policy_price, policy_discount, gift_amortize_price, gift_amortize_subtotal
            ],
            shareFields: [sales_price, discount, node_subtotal,]
        };
        // 支持扩展处理字段
        // let r = await this.parent.runPlugin('bom.getSpecialFields.after', {
        //     mdApiName, // 从对象数据
        //     param
        // });
        // if (r && r.data) res.allFields = res.allFields.concat(r.data);
        if(this.noClearSalesPrice()){
            res.allFields = this.deleteSalesPrice(res.allFields)
        }
        return res;
    }

    deleteSalesPrice(fieldList){
        const {
            sales_price,
            selling_price,
            discount,

        } = this.getAllFields();
        fieldList = fieldList.filter(f => ![sales_price, selling_price, discount, 'extra_discount'].includes(f));
        return fieldList;
    }

    // 清除子产品上的一些字段；
    setBomSpecialFieldsVal({
                               data,
                               mdApiName,
                               val = null,
                               onlyChangeData = false
                           } = {}, param) {
        let des = this.getDescribe(mdApiName, param);
        let {
            share_rate,
            parent_prod_pkg_key
        } = this.getAllFields(mdApiName);
        let sf = this.getSpecialFields(mdApiName, param);
        let allFields = sf.allFields;
        let shareFields = sf.shareFields;
        data = Array.isArray(data) ? data : [data];
        data.forEach(item => {
            if (item[parent_prod_pkg_key] || item.parent_rowId) {
                allFields.forEach(field => {
                    if (des[field]) {
                        if (this.isSupportShareRate(mdApiName, param) && PPM.hasValue(item[share_rate]) && shareFields.includes(field)) {
                            if (!PPM.hasValue(item[field])) {
                                param.dataUpdater.updateDetail(mdApiName, item.rowId, {
                                    [field]: '0'
                                });
                                if (onlyChangeData) item[field] = '0'
                            }
                        } else {
                            param.dataUpdater.updateDetail(mdApiName, item.rowId, {
                                [field]: val
                            });
                            item[field] = val;
                        }
                    }
                })
            }
        })
    }

    /**
     * @desc 二次配置Bom后，更新+计算数据
     * @param obj = {
     *     data:[], 二次配置的子产品数据，树状结构
     *     newRootData:{}, 根节点的最新价目表产品数据
     *     rootDataBomId:'', 根节点的bom_id
     *     totalMoney:'' 包的调整后总金额
     * }
     * @param rootData      根节点数据
     * @param mdApiName     从对象apiname
     * @param recordType    业务类型
     * @param masterData    主对象数据
     * @param masterApiName 主对象apiname
     * @param param
     * @returns {Promise<void>}
     */
    async updateBomForTwiceConfig({
                                      obj = {},
                                      rootData = {},
                                      mdApiName = '',
                                      recordType = '',
                                      masterData = {},
                                      masterApiName = ''
                                  } = {}, param) {
        if (!mdApiName) return console.error('mdApiName cannot empty');

        let r1 = await this.parent.runPlugin('bom.updateBomForTwiceConfig.before', {
            obj,
            rootData,
            mdApiName,
            recordType,
            param
        });
        if(r1 && !r1.status) return;
        this._isTwiceConfig = true;
        param.objApiName = mdApiName;
        let {
            product_price,
            price_book_id,
            quantity,
            pricing_period
        } = this.getAllPluginFields(mdApiName);
        this._addToCache(rootData.rowId);
        this._rootPriceChange = [];
        let {
            newRootData,
            newChildren
        } = await this.updateBom({
            newChildren: obj.data,
            newRootData: obj.newRootData,
            rootDataBomId: obj.rootDataBomId,
            rootDataCoreId: obj.rootDataCoreId,
            rootDataPrice: obj.totalMoney,
            originalTotalMoney: obj.originalTotalMoney,
            rootData,
            mdApiName,
            recordType,
            from: 'secondConfig'

        }, param);
        this._addChangeData(param, newChildren);
        await this.parent.runPlugin('bom.twiceConfig.enter', {
            rootData:newRootData,
            children: newChildren,
            mdApiName,
            recordType,
            param
        });
        let nChildren = newChildren;
        rootData = newRootData;
        // 根节点价目表有变化，需要重算根节点数据；todo:子节点的目前没有重算
        if (this._price_book_id_change) await this.calculate({
            data: [].concat([rootData]).concat(nChildren),
            mdApiName,
            changeField: price_book_id,
        }, param);
        // 计算bom
        await this.calculateBomPrice({
            data: [rootData],
            mdApiName,
            masterData,
            masterApiName,
            recordType,
            changeField: [product_price, quantity]
        }, param);
        if(newChildren.length){
            let allModify = [];
            newChildren.forEach(item => {
                if(!item.isGroup) allModify.push(item.rowId)
            });
            await param.triggerUIEvent({
                triggerField: quantity,
                objApiName: mdApiName,
                operateType: 'mdEdit',
                dataIndex: allModify,
            });
        }

        // 二次配置完，走一遍期数计算；
        if(newChildren.length){
            let changePeriod = {};
            newChildren.forEach(item => {
                changePeriod[item.rowId] = {
                    "pricing_period": item[pricing_period]
                }
            });
            await this.parent.runPlugin('period-product.triggerCalculate', {
                mdApiName,
                changeData: changePeriod,
                operateType: 'mdEdit',
                masterData,
                // triggerUIEvent: false,
                detailData: newChildren,
                param
            })
        }

        await this.parent.runPlugin('bom.twiceConfig.after', {
            rootData,
            mdApiName,
            recordType,
            param
        });
        this._isTwiceConfig = false;
    }

    // 二次配置时，给 param 补一个 changeData；报价器会用到；
    _addChangeData(param, children){
        let {
            product_price,
            quantity
        } = this.getAllFields(param.objApiName);
        let obj = {};
        children.forEach(item => {
            if(item.isGroup) return;
            obj[item.rowId] = {
                [product_price]: item[product_price],
                [quantity]: item[quantity],
            }
        });
        param.changeData = obj
    }

    // 编辑之前事件
    _editBefore(plugin, param) {
        // let mdApiName = param.objApiName;
        // let {
        //     quantity,
        //     product_price
        // } = this.getAllFields(mdApiName);
        // let changeField = param.fieldName;
        // let childrenRowId = [];
        // // 批量编辑价格 数量时，需要过滤掉子产品
        // if (param?.dataIndex?.length > 1 && [quantity, product_price].includes(changeField)) {
        //     param.dataIndex.forEach(rowId => {
        //         let rowData = param.dataGetter.getData(mdApiName, rowId);
        //         if (rowData.parent_rowId) childrenRowId.push(rowId);
        //     });
        //     return {
        //         filterRowIds: childrenRowId
        //     }
        // }
    }

    // 复制数据，需要设置字段可编辑，更新虚拟key；
    _copyAfter(plugin, param) {
        let allFields = this.getAllFields();
        let mdApiName = param.objApiName;
        let recordType = param.recordType;
        let copyData = this.getMdData(mdApiName, param.dataIndex, param);
        this.setFieldsReadonly({
            data: copyData,
            mdApiName,
            recordType
        }, param);
        copyData.forEach(item => {
            if (PPM.isBom(item, allFields).isPackage) {
                let children = PPM.getChildren({
                    rowId: item.rowId,
                    details: copyData
                });
                this.updateProdKey(children, item);
            }
        });
    }

    // 处理子产品虚拟key，字段可编辑，字段只读，字段展示;
    async _bom_parseChildren(plugin, opt) {
        let {
            details,
            mdApiName,
            recordType,
            param
        } = opt;
        let allFields = this.getAllFields();
        for (let i = 0; i < details.length; i++) {
            let item = details[i];
            if (PPM.isBom(item, allFields).isPackage && !item.parent_rowId) {
                let rootData = item;
                let children = PPM.getChildren({
                    rowId: rootData.rowId,
                    details: details,
                });
                this.updateProdKey(children, rootData);
            }
        }
        this.setBomSpecialFieldsVal({
            data: details,
            mdApiName
        }, param);
        this.setFieldsReadonly({
            data: details,
            mdApiName,
            recordType
        }, param);
        PPM.addDefQuantity({
            data: details,
            allFields,
            mdApiName
        }, param);
    }

    // 向外提供的方法；计算BOM
    async _queryBomAndCalculate(plugin, options) {
        let {obj, param} = options;
        await this.calculateBomPrice(obj, param);
    }

    // 价格政策执行完后的勾子，bom分摊
    async _pricePolicy_end(plugin, options) {
        let {modifyIndex, param} = options;
        if (!modifyIndex.length) return;

        let masterApiName = param.masterObjApiName;
        let masterData = param.dataGetter.getMasterData();
        let addData = param.addDatas || [];
        if (addData.length) {
            let addIndex = addData.map(item => item.rowId);
            modifyIndex = PPM.uniq(modifyIndex.concat(addIndex));
        }
        let data = this.getMdData('', modifyIndex, param);
        let mdApiName = data[0].object_describe_api_name;
        if (!this.isSupportShareRate(mdApiName, param)) return;
        let recordType = data[0].record_type;
        let {subtotal} = this.getAllFields(mdApiName);
        await this.calculateBomPrice({
            data,
            mdApiName,
            masterApiName,
            masterData,
            recordType,
            changeField: subtotal,
            noCalBomPrice: false,
            noCalPriceBookPrice: true,
        }, param);
        // 每次只触发一次价格变化事件，防止死循环
        if (this._triggerPriceChange) {
            this._triggerPriceChange = false;
            return
        }
        // 产品包价格有变化，触发勾子
        if (!this._formAdd && this._rootPriceChange && this._rootPriceChange.length) {
            this._triggerPriceChange = true;
            await this.parent.runPlugin('bom.priceChange', {
                priceChangeRow: this._rootPriceChange,
                mdApiName,
                recordType,
                param
            });
        }
        this._formAdd = false;
    }

    // 是否开启了临时子件
    isOpenTempNode() {
        return this.parent.getConfig('bom_temp_node');
    }

    // 删除子产品标记
    deleteChildrenProdKey(children){
        let {parent_prod_pkg_key, root_prod_pkg_key, bom_id, product_group_id, new_bom_path, quantity, bom_core_id, related_core_id} = this.getAllFields();
        let fields = ['parent_rowId', 'pid', 'bom_id', parent_prod_pkg_key, root_prod_pkg_key, bom_id, product_group_id, new_bom_path, bom_core_id, related_core_id];
        children.forEach(item => {
            item[quantity] = item.amount;
            item.__isChildren = true;
            fields.forEach(k => {
                delete item[k];
            })
        })
    }

    // 删除根节点数据
    deleteRoot(allData, rootList, mdApiName, param){
        if(!rootList.length) return;
        rootList.forEach(item => {
            param.dataUpdater.del(mdApiName, item.rowId);
        });
        let len = allData.length;
        while (len--){
            let item = allData[len];
            let f = rootList.find(c => c.rowId === item.rowId);
            if(f) allData.splice(len, 1);
        }
    }

    // todo：临时方案；在UI事件执行完后，根据UI事件返回的更新数据，触发计算；870后去掉
    async _triggerCalAfterUIEvent(plugin, param = {}, type){
        if(!this.parent.isGrayDeleteRoot()) return;
        if(!param.uiAndCalRst || !param.uiAndCalRst.uiData) return;
        let mdApiName = param.objApiName;
        let uiRes = param.uiAndCalRst.uiData?.data[mdApiName];
        if(type === 'edit'){
            let up = uiRes?.u;
            if(!up) return;
            let calIndexs = [];
            let changeFields = [];
            PPM.each(up, (val, key) => {
                calIndexs.push(key);
                let keys = Object.keys(val);
                keys.forEach(k => {
                    if(!changeFields.includes(k)) changeFields.push(k);
                })
            });
            let p = {
                changeFields,
                operateType: 'mdEdit',
                dataIndex: calIndexs,
                objApiName: mdApiName,
                filterFields: {
                    [mdApiName]: changeFields
                },
            };
            await param.triggerCal(p);
        }else if(type === 'del'){
            let p = {
                operateType: 'mdDel',
                objApiName: mdApiName,
            };
            await param.triggerCal(p);
        }
    }

    // todo：临时方案；在UI事件执行完后，根据UI事件返回的更新数据，触发计算；870后去掉
    async _delEnd(plugin, param){
        await this._triggerCalAfterUIEvent(plugin, param, 'del');
    }

    // 缓存二次配置的产品包，保存前校验用
    _addToCache(rowId){
        if(this._cacheEditRootRow.includes(rowId)) return;
        this._cacheEditRootRow.push(rowId);
    }

    // 清掉缓存
    _delCache(rowId){
        rowId = Array.isArray(rowId) ? rowId : [rowId];
        PPM.deleteArrChildren(this._cacheEditRootRow, rowId);
    }

    // 保存前校验；有黑名单
    async _formSubmitBefore(plugin, param){
        if(CRM.util.isGrayScale('CRM_NOCHECK_BOM_PRICE') || !this._cacheEditRootRow?.length) return;
        let cacheData = this._cacheEditRootRow.map(rowId => {
            return param.dataGetter.getData(null, rowId);
        });
        let masterApiName = param.masterObjApiName;
        let masterData = param.dataGetter.getMasterData();
        let recordType = masterData.record_type;
        this.pluginService.api.showLoading();
        this._validRes = [];
        let reqList = cacheData.map(rootData => {
            let mdApiName = rootData.object_describe_api_name;
            let details = param.dataGetter.getDetail(mdApiName);
            let children = PPM.getChildren({
                rowId: rootData.rowId,
                details: details,
            });
            return this._validBomPrice({
                rowData: rootData ,
                rootData,
                children,
                changeField:[],
                masterData,
                masterApiName,
                mdApiName,
                recordType
            }, param)
        });
        let r = await Promise.all(reqList);
        this.pluginService.api.hideLoading();
        if(this._validRes.length){
            this.parent.sendLog({
                eventId: 'fs-crm-sfa-plugin-log',
                eventType: 'PROD',
                eventName: 'pv',
                // 对象ApiName
                apiName:'bom',
                data: this._validRes
            })
        }
        this._validRes = [];
        console.log(r);
        // plugin.skipPlugin();
    }

    // 保存前再校验一次包的价格
    async _validBomPrice({
                       rowData ,
                       rootData = {},
                       children = [],
                       changeField,
                       masterData = {},
                       masterApiName = '',
                       mdApiName = '',
                       recordType = ''
                   } = {}, param){
        let rq = this.getParamForCalBomPrice({
            rowData,
            rootData,
            children,
            masterData,
            changeField,
            masterApiName,
            mdApiName,
        }, param);
        if(!rq) return;
        let {
            product_price,
            product_id,
        } = this.getAllFields();
        let newData = await this.requestBomPrice(rq);
        let newRoot = newData.find(item => item.prod_pkg_key === rootData.rowId);
        if(!newRoot){
            console.error('未匹配到根节点');
            return;
        }
        // 价格不一致，发送日志
        if(Number(newRoot.adjust_price) !== Number(rootData[product_price]) ){
            this._validRes.push({
                oldPrice: rootData[product_price],
                newPrice: newRoot.adjust_price,
                product_id: rootData[product_id],
                product_id__r: rootData[product_id + '__r'],
                rootRowId: rootData.rowId,
                mdApiName,
                bomList: rq.bomList,

            })
        }else{
            this._delCache(rootData.rowId)
        }
    }

    // 报价器执行完，更新子件信息；
    _quoterAfter(plugin, obj){
        let {changeInfo, param,} = obj;
        let mdApiName = param.objApiName;
        let allFields = this.getAllPluginFields(mdApiName);
        let {quantity, product_price, amount_any, related_core_id, pricing_period, price_per_set } = allFields;
        let details = param.dataGetter.getDetail();
        if(!changeInfo) return;
        // 高级公式改了数量；更新顺序，先更新子件，后更新母件
        let changeQuantity = changeInfo[quantity];
        if(changeQuantity?.length){
            let des = this.getDescribe(mdApiName, param, quantity);
            let dp = des?.decimal_places;
            let rootData = [];
            details.forEach(item => {
                if(changeQuantity.includes(item.rowId)){
                    if(PPM.isBom(item, allFields).isPackage || item[related_core_id]){
                        rootData.push(item)
                    }

                    if(item.parent_rowId){
                        // 改了子件数量，更新子件单包数量
                        item.defQuantity = item[quantity];
                        param.dataUpdater.updateDetail(mdApiName, item.rowId, {
                            defQuantity: item[quantity]
                        });
                        // 改了子件数量，查找最近一个复用 bom 的数量，成倍
                        if(!item[amount_any + '__v']){
                            let root = param.dataGetter.getData(mdApiName, item.root_prod_pkg_key);
                            let parentBom = !item[amount_any + '__v'] && item.__parentBomRowId ? param.dataGetter.getData(mdApiName, item.__parentBomRowId) : root;
                            let q = PPM.formatDecimalPlace(PPM.multiplicational(parentBom[quantity], item.defQuantity),dp);
                            item[quantity] = q;
                            param.dataUpdater.updateDetail(mdApiName, item.rowId, {
                                [quantity]: q
                            });
                        }
                        item._isChangedByQuoter = true;
                        item._isChangeQuantityByQuoter = true;
                    }
                }
            });
            console.log(rootData);

            // 改了母件数量，子件数量成倍
            if(rootData.length){
                rootData.forEach(item => {
                    let children = PPM.getChildren(item.rowId, details, true);
                    let r = this.setSubQuantity({
                        allData: [].concat(children).concat([item]),
                        rootQuantity: item[quantity],
                        mdApiName
                    }, param);
                })
            }
        }

        // 高级公式改了价格
        let changePrice = changeInfo[product_price];
        if(changePrice?.length){
            details.forEach(item => {
                if(changePrice.includes(item.rowId)){
                    if(item.parent_rowId){
                        item._isChangedByQuoter = true;
                        item.advancePriceFlag = true;
                    }
                }
            });
        }
        let changePP = changeInfo[pricing_period];
        if(changePP?.length){
            details.forEach(item => {
                if(changePP.includes(item.rowId)){
                    if(item.parent_rowId){
                        item._isChangedByQuoter = true;
                        item._isChangePricingByQuoter = true;
                    }
                }
            });
        }

    }

    // 新增数据先不走高级公式
    _quoterBefore(){
        return {
            execute: false
        }
    }

    // 报价器过滤不计算的字段
    _quoterExecuteConfig(plugin, obj){
        let {param} = obj;
        return {
            excludedBomDefFields: this._getChildrenNoCalculateFields(param)
        }
    }

    getRootDataForPeriodProduct(data, details, mdApiName){
        let {root_prod_pkg_key, prod_pkg_key} = this.getAllFields(mdApiName);
        let res = [];
        data.forEach(item => {
            let root = details.find(c => c[prod_pkg_key] === item[root_prod_pkg_key]);
            if(root) res.push(root);
        });
        return res;
    }

    getRootAndChildren(rootList, details){
        let res = [];
        rootList.forEach(item => {
            res.push(item);
            let allChildren = PPM.getChildren({
                rowId: item.rowId,
                details,
                excludeGroup: true
            });
            res = res.concat(allChildren)
        });
        return res;
    }

    // 周期性产品计算后，如果期数有变化，则重新计算产品包价格；
    async _periodProductCalEnd(plugin, obj){
        if(this._isTwiceConfig) return;
        let {modifyPricingPeriodIndex, mdApiName, param} = obj;
        let operateType = param.operateType;
        let {root_prod_pkg_key, prod_pkg_key, pricing_period} = this.getAllPluginFields(mdApiName);
        let masterData = param.dataGetter.getMasterData();
        let masterApiName = param.masterObjApiName;

        if(this._needCalBom_add){
            if(!this._cacheAllAddData?.length) return this._needCalBom_add  = null;
            let recordType = this._cacheAllAddData[0].record_type;
            await this.calculateBomPrice({
                data: this._cacheAllAddData,
                mdApiName,
                masterData,
                masterApiName,
                recordType,
                needCalBom: true,
                noCalBomPrice: false,   // 新添加数据，不用 server 计算包的价格，只分摊就行
                from: 'add',
                triggerUIEvent: false,
                noTriggerCal: false,   // 新添加数据，不用再重复走计算了
            }, param);
            this._needCalBom_add  = null;
            return
        }

        if(modifyPricingPeriodIndex?.length){
            let details = param.dataGetter.getDetail(mdApiName);
            let data = details.filter(c => c[root_prod_pkg_key] && modifyPricingPeriodIndex.includes(c.rowId));
            if(!data.length) return;
            let recordType = data[0].record_type;
            let bomData = _.uniq(data,c => c[root_prod_pkg_key]);
            let rootList = this.getRootDataForPeriodProduct(bomData, details, mdApiName,);
            let allCalData = this.getRootAndChildren(rootList, details);
            if(operateType === 'mdEdit'){
                this._calBomForEdit = true;
                await this.editCalBom({
                    needCalData: rootList,
                    changeRow: data,
                    allCalData,
                    mdApiName,
                    masterData,
                    masterApiName,
                    recordType,
                    changeField: pricing_period,
                    param
                });
            }else{
                await this.calculateBomPrice({
                    data: rootList,
                    changeRow:data,
                    mdApiName,
                    masterData,
                    masterApiName,
                    recordType,
                    triggerUIEvent: false,
                }, param);
                this.setBomSpecialFieldsVal({
                    data,
                    mdApiName
                }, param);
            }
        }
    };

    // 添加数据，周期计算之前，补上子件
    _periodProductBatchAddBefore(plugin, obj){
        let {operateType, data, param} = obj;
        if (operateType === 'mdAdd'){
            return { data: data.concat(this._cacheAllChildren)};
        }
    }

    _periodProductBatchAddEnd(plugin, obj){
        let { data, param} = obj;
        return { data: data.concat(this._cacheAllChildren)};
    }

    _periodProductBatchAddAfter_parseAddData(plugin, obj){
        let { data, param} = obj;
        return { data: data.concat(this._cacheAllChildren)};
    }

    // 手工改价计算之前，需要过滤下 bom 子件
    _changePriceCalBefore(plugin, obj){
        let {calIndex, param} = obj;
        if(!calIndex?.length) return;
        let mdApiName = param.objApiName;
        let {parent_prod_key} = this.getAllFields(mdApiName);
        let resCalIndex = [];
        calIndex.forEach(rowId => {
            let item = param.dataGetter.getData(mdApiName, rowId);
            if(!item || item && !item[parent_prod_key]){
                resCalIndex.push(rowId);
            }
        });
        return {calIndex: resCalIndex};
    }

    getHook() {
        return [
            {
                event: 'md.edit.before',
                functional: this._editBefore.bind(this)
            }, {
                event: 'md.edit.after',
                functional: this._editAfter.bind(this)
            }, {
                event: 'md.edit.end',
                functional: this._editEnd.bind(this)
            }, {
                event: 'md.batchAdd.after',
                functional: this._batchAddAfter.bind(this)
            },
            {
                event: 'md.batchAdd.end',
                functional: this._batchAddEnd.bind(this)
            },
            {
                event: 'md.copy.after',
                functional: this._copyAfter.bind(this)
            }, {
                event: 'bom.parseChildren',
                functional: this._bom_parseChildren.bind(this)
            }, {
                event: 'bom.queryBomAndCalculate',
                functional: this._queryBomAndCalculate.bind(this)
            }, {
                event: 'pricePolicy.executeAndUpdate.end',
                functional: this._pricePolicy_end.bind(this)
            }, {
                event: 'md.del.end',
                functional: this._delEnd.bind(this)
            }, {
                event: 'form.submit.before',
                functional: this._formSubmitBefore.bind(this)
            }, {
                event: 'quoter.execute.after',
                functional: this._quoterAfter.bind(this)
            },{
                event: 'quoter.execute.before',
                functional: this._quoterBefore.bind(this)
            }, {
                event: 'quoter.execute.config',
                functional: this._quoterExecuteConfig.bind(this)
            },{
                event: 'period-product.triggerCalculate.end',
                functional: this._periodProductCalEnd.bind(this)
            },{
                event: 'period-product.batchAddUIEvent.before',
                functional: this._periodProductBatchAddBefore.bind(this)
            },{
                event: 'period-product.mdBatchAddEnd.before',
                functional: this._periodProductBatchAddEnd.bind(this)
            },{
                event: 'period-product.mdBatchAddAfter.parseAddData',
                functional: this._periodProductBatchAddAfter_parseAddData.bind(this)
            },{
                event: 'change_price.cal.before',
                functional: this._changePriceCalBefore.bind(this)
            },
        ];
    }

};