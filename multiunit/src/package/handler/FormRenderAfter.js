import {<PERSON>Handler} from "./BaseHandler";
import {isMultipleUnit, multiUnitPlacesDecimal} from "../utils";
import {emitEvent} from "../events";

export class FormRenderAfter extends BaseHandler {

    constructor(multiUnitContext) {
        super(multiUnitContext);
    }

    async handleEvent(options, pluginExecResult) {
        let {sourceAction, dataGetter, dataUpdater, formType, masterObjApiName} = options;
        let objApiName = this.multiUnitContext.getFirstDetailObjApiName();
        let {getDetail, getDescribe} = dataGetter || {};
        let detailDataList = getDetail && getDetail(objApiName);
        let {actual_unit, other_unit, is_multiple_unit, base_unit_count, conversion_ratio, stat_unit_count, price_book_product_id, product_id} = this.getDetailFields(objApiName);
        detailDataList && detailDataList.length && detailDataList.forEach(detailData => {
            let isMultiUnit = isMultipleUnit(detailData, is_multiple_unit);
            let dataIndex = this.getDataIndex(detailData);
            [actual_unit, other_unit].forEach(fieldName => {
                dataUpdater && dataUpdater.setReadOnly && dataUpdater.setReadOnly({
                    objApiName,
                    dataIndex: dataIndex,
                    fieldName: fieldName,
                    status: !isMultiUnit
                });
            });
        });
        //映射到销售订单时，订单产品要进行多单位取价
        if (objApiName === 'SalesOrderProductObj' && (sourceAction === 'Convert' || sourceAction === 'convert' || formType === 'mapping')) {
            let calcResult;
            let params = this.buildMultiUnitParams(detailDataList, objApiName);
            if (params && params.length) {
                let param = {
                    params,
                    masterObjectApiName: masterObjApiName,
                    detailObjectApiName: objApiName
                };
                await this.multiUnitContext.runPlugin(emitEvent.multiUnit_form_calcPriceParam, Object.assign({}, options, {
                    param,
                    detailDataList
                }));
                calcResult = await this.multiUnitContext.calcPriceByUnit(param);
            }
            let detailObjDescribe = getDescribe(objApiName);
            detailDataList && detailDataList.forEach(detailData => {
                let multiUnitResult = this.getMultiUnitResult(detailData, calcResult, detailObjDescribe);
                if (multiUnitResult) {
                    let {
                        base_unit_count: baseUnitCount, conversion_ratio: conversionRatio, stat_unit_count: statUnitCount, places_decimal = null,
                        needChangePriceBookProduct, changePriceBookProduct
                    } = multiUnitResult;
                    let dataIndex = this.getDataIndex(detailData);
                    let updateData = {
                        [base_unit_count]: baseUnitCount,
                        [conversion_ratio]: conversionRatio,
                        [stat_unit_count]: statUnitCount,
                        [multiUnitPlacesDecimal]: places_decimal
                    };
                    if (needChangePriceBookProduct && changePriceBookProduct) {
                        let {id, _id, name} = changePriceBookProduct;
                        updateData[price_book_product_id] = id || _id;
                        updateData[`${price_book_product_id}__r`] = name;
                    }
                    dataUpdater && dataUpdater.updateDetail(objApiName, dataIndex, updateData);
                }
            });
        }

        // 特殊处理映射的单位
        if(objApiName === 'SalesOrderProductObj' && formType === 'mapping' && CRM && CRM.util && CRM.util.isGrayScale('CRM_MAPPING_MULTIUNIT')){
            detailDataList && detailDataList.forEach(detailData => {
                if (!detailData.hasOwnProperty(actual_unit)) {
                    let updateData = {
                        [actual_unit]: detailData.unit__v,
                    };
                    let dataIndex = this.getDataIndex(detailData);
                    dataUpdater && dataUpdater.updateDetail(objApiName, dataIndex, updateData);
                }
            });
        }

        return {
            __execResult:{
                filterBatchEditFields: [price_book_product_id, product_id, actual_unit, other_unit , base_unit_count],
            },
            __mergeDataType:{
                array: 'concat'
            }
        }
    }

    buildMultiUnitParams(detailDataList, objApiName) {
        let {product_id, quantity, price_book_product_id, actual_unit, other_unit, is_multiple_unit} = this.getDetailFields(objApiName);
        return detailDataList && detailDataList.length && detailDataList.filter(it => {
            return isMultipleUnit(it, is_multiple_unit) && (it[actual_unit] !== undefined && it[actual_unit] !== null);
        }).map(it => {
            let {[product_id]: productId, [quantity]: count, [price_book_product_id]: priceBookProductId, [actual_unit]: unitId, [other_unit]: otherUnitId} = it;
            let rowId = this.getRowId(it, true);
            return {productId, priceBookProductId, unitId, otherUnitId, count, rowId}
        });
    }

    getMultiUnitResult(detailData, calcResults, objectDescribe) {
        let {api_name: objApiName, fields} = objectDescribe || {};
        let {actual_unit, quantity} = this.getDetailFields(objApiName);
        let {[actual_unit]: unitId, [quantity]: count} = detailData || {};
        if (!unitId) {//明细中单位字段没有值，不处理
            return;
        }
        let orgRowId = this.getRowId(detailData);
        let calcResult = calcResults && calcResults.length && calcResults.find(it => {
            let {rowId} = it || {};
            return orgRowId === rowId;
        });
        if (!calcResult) {//如果没有找到计算结果，则特殊创建一个计算结果
            let actualUnitField = fields && fields[actual_unit];
            let selectOption = actualUnitField && actualUnitField.options && actualUnitField.options.find(it => it.value === unitId);
            let unitLabel = selectOption && selectOption.label || '';
            let stat_unit_count = (count + unitLabel);
            calcResult = {
                conversion_ratio: 1,
                base_unit_count: count,
                stat_unit_count: stat_unit_count
            }
        }
        return calcResult;
    }
}