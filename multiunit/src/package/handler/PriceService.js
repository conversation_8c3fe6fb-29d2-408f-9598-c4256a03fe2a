import {<PERSON>Handler} from "./BaseHandler";
import {events} from "../events";

export class PriceService extends BaseHandler {

    constructor(multiUnitContext) {
        super(multiUnitContext)
    }

    handleEvent(options, pluginExecResult, event) {
        if (event === events.md_batchAdd_selectSkuConfig) {
            let preData = pluginExecResult && pluginExecResult.preData || {};
            let openMultiUnitPriceBook = this.multiUnitContext.isOpenMultiUnitPriceBook();
            return Object.assign({}, preData, {
                isOpenMultiUnit: true,//开启了多单位标识
                isOpenMultiUnitPriceBook: openMultiUnitPriceBook,//是否开启了多单位价目表定价
            });
        } else if (event === events.priceService_batchAdd_getPriceParam) {
            let {params, lookupDatas} = options;
            params && params.fullProductList && params.fullProductList.length && params.fullProductList.forEach((it, index) => {
                let lookupData = lookupDatas && lookupDatas.length && lookupDatas[index];
                let {object_describe_api_name, unit, unit__v, actual_unit} = lookupData || {};
                let isProduct = object_describe_api_name === 'ProductObj';
                let unitId = isProduct ? unit : (actual_unit || unit__v);
                let baseUnit = isProduct ? unit : unit__v;
                Object.assign(it, {unit: unitId, baseUnit: baseUnit,})
            })
        } else if (event === events.priceService_batchAdd_matchGetPriceResult) {
            let {lookupData} = options;
            let {object_describe_api_name, unit, unit__v, actual_unit} = lookupData || {};
            let isProduct = object_describe_api_name === 'ProductObj';
            let unitId = isProduct ? unit : (actual_unit || unit__v);
            let preData = pluginExecResult && pluginExecResult.preData || {};
            return Object.assign({}, preData, {
                param_unit: unitId,
            });
        } else if (event === events.priceService_form_getPriceParam) {
            let {objApiName, params, detailDataList} = options;
            let {unit, actual_unit} = this.getDetailFields(objApiName);
            params && params.fullProductList && params.fullProductList.length && params.fullProductList.forEach((it, index) => {
                let detailData = detailDataList && detailDataList.length && detailDataList[index];
                let {[`${unit}__v`]: unitId, [actual_unit]: actualUnit} = detailData;
                Object.assign(it, {unit: actualUnit, baseUnit: unitId})
            })
        } else if (event === events.priceService_form_matchGetPriceResult) {
            let {objApiName, detailObjectData} = options;
            let {actual_unit} = this.getDetailFields(objApiName);
            let {[actual_unit]: actualUnit} = detailObjectData || {};
            let preData = pluginExecResult && pluginExecResult.preData || {};
            return Object.assign({}, preData, {
                param_unit: actualUnit,
            });
        } else if (event === events.priceService_batchAddAfter_parseData) { // 选数据，走取价之前，补充单位信息
            let {data, lookupDatas, param} = options;
            data.forEach((item, index) => {
                if(!item.unit && lookupDatas[index].unit){
                    item.unit = lookupDatas[index].unit;
                    item.unit__v = lookupDatas[index].unit__v;
                }
            });
            return data;
        }
    }
}