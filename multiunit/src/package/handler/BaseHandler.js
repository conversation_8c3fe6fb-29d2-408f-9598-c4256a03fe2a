import {isMultipleUnit, multiUnitPlacesDecimal, rowId, uuid} from "../utils";

export class BaseHandler {

    constructor(multiUnitContext) {
        this.multiUnitContext = multiUnitContext;
    }

    handleEvent(options, pluginExecResult, event) {
    }

    toMultiUnitData(multiUnitResult, objApiName) {
        let {
            price, count, unitId,
            base_unit_count: baseUnitCount,
            conversion_ratio: conversionRatio,
            stat_unit_count: statUnitCount,
            other_unit: otherUnit,
            other_unit_quantity: otherUnitQuantity,
            priceBookPrice,
            needChangePriceBookProduct, changePriceBookProduct
        } = multiUnitResult || {};
        let {
            price: product_price, quantity, places_decimal,
            actual_unit, conversion_ratio, base_unit_count, stat_unit_count, other_unit, other_unit_quantity,
            price_book_product_id, price_book_price, price_book_id
        } = this.getDetailFields(objApiName);
        let result = {
            [product_price]: price,
            [actual_unit]: unitId,
            [base_unit_count]: baseUnitCount,
            [conversion_ratio]: conversionRatio,
            [stat_unit_count]: statUnitCount,
            [other_unit]: otherUnit,
            [other_unit_quantity]: otherUnitQuantity,
            [multiUnitPlacesDecimal]: places_decimal,
            [price_book_price]: priceBookPrice
        };
        if (count) {
            result[quantity] = count;
        }
        if (needChangePriceBookProduct && changePriceBookProduct) {
            let {id, _id, name} = changePriceBookProduct;
            result[price_book_product_id] = id || _id;
            result[`${price_book_product_id}__r`] = name;
            let containerDocument = changePriceBookProduct.containerDocument;
            if(containerDocument){
                Object.assign(result, {
                    [price_book_id]: containerDocument.pricebook_id,
                    [`${price_book_id}__r`]: containerDocument.pricebook_id__r
                });
            }
        }

        return result;
    }

    getMasterFields() {
        return this.multiUnitContext.getMasterFields() || {}
    }

    getDetailFields(objApiName) {
        return this.multiUnitContext.getDetailFields(objApiName) || {}
    }

    getDataIndex(objectData) {
        let {rowId, dataIndex} = objectData || {};
        return rowId || dataIndex;
    }

    i18n(msg){
        return this.multiUnitContext.pluginService.api.i18n(msg);
    }

    /**
     * @desc 设置字段只读
     * @param list 从对象数据
     * @param pluginParam 底层插件提供的方法集
     */
    setFieldReadonly( {list = [], pluginParam = {}} = {}) {
        let {dataUpdater,  objectApiName} = pluginParam || {};
        let {actual_unit, other_unit} = this.getDetailFields(objectApiName);
        let fields = [actual_unit, other_unit];
        list.forEach(item => {
            let isMultiUnit = isMultipleUnit(item, 'is_multiple_unit');
            dataUpdater && dataUpdater.setReadOnly && dataUpdater.setReadOnly({
                objApiName: objectApiName,
                dataIndex: item.rowId,
                fieldName: fields,
                status: !isMultiUnit
            });
            // 多单位产品的单位字段，设为必填
            if(isMultiUnit){
                dataUpdater && dataUpdater.setRequired && dataUpdater.setRequired({
                    objApiName: objectApiName,
                    dataIndex: item.rowId,
                    fieldName: [actual_unit],
                    status: true
                });
            }
        });
    }

    getRowId(objectData, isRegenerate) {
        let _rowId;
        if (isRegenerate) {
            _rowId = uuid();
        } else {
            _rowId = objectData[rowId] || uuid();
        }
        objectData[rowId] = _rowId;
        return _rowId;
    }
}