import {BaseHandler} from "./BaseHandler";
import {isMultipleUnit} from "../utils";
import {events} from "../events";
import { BatchTodoMultiunit } from "./BatchTodoMultiunit";

export class Bom extends BaseHandler {

    constructor(multiUnitContext) {
        super(multiUnitContext)
        this.batchTodoMultiunit = new BatchTodoMultiunit(multiUnitContext);
    }

    handleEvent(options, pluginExecResult, event) {
        if (event === events.bom_md_edit_after || event === events.bom_md_batchAdd_end) {
            return this.handleMultiUnitParse(options);
        }
    }


    /**
     * 选配完回填明细处理子件的多单位字段
     * 编辑母件数量处理子件的多单位字段
     * @param {object} options
     * @param {any[]} options.list 所有行
     * @param {any[]} options.children 子件行
     * @returns 
     */
    async handleMultiUnitParse(options, event) {
        const {list = [], children = [], objectApiName, detailObjectApiName, param} = options;
        const {price, quantity, price_book_price} = this.getDetailFields(detailObjectApiName);

        // 只有在编辑母件的数量时，才需要处理子件的多单位
        if (event === events.bom_md_edit_after && param.fieldName !== quantity) {
            return;
        }

        const result = await this.batchTodoMultiunit.todoMultiunit({
            list: children,
            objectApiName,
            detailObjectApiName,
            ignoreFields: [price, quantity, price_book_price],
            pluginParam: param,
            noCalculate: false,
            setFieldReadOnly: false,
            setNonMultipleUnitField: true,
        });

        // 更新数据到明细行
        result.forEach((it) => {
            param.dataUpdater.updateDetail(detailObjectApiName, it.rowId, it);
        })
    }
}
