/**
 * @desc 复制触发的事件
 */
import {BaseHandler} from "./BaseHandler";
import {isMultipleUnit, multiUnitPlacesDecimal} from "../utils";

export class CopyAfter extends BaseHandler {

    constructor(multiUnitContext) {
        super(multiUnitContext);
    }

    // 复制时，处理字段可编辑
    handleEvent(options, pluginExecResult) {
        let {sourceAction, dataGetter, dataUpdater, dataIndex} = options;
        let objApiName = this.multiUnitContext.getFirstDetailObjApiName();
        let { getData} = dataGetter || {};
        let {actual_unit, other_unit, is_multiple_unit, } = this.getDetailFields(objApiName);
        dataIndex.forEach(rowId => {
            let detailData = getData(objApiName, rowId);
            let isMultiUnit = isMultipleUnit(detailData, is_multiple_unit);
            [actual_unit, other_unit].forEach(fieldName => {
                dataUpdater && dataUpdater.setReadOnly && dataUpdater.setReadOnly({
                    objApiName,
                    dataIndex: rowId,
                    fieldName: fieldName,
                    status: !isMultiUnit
                });
            });
        });

    }

}