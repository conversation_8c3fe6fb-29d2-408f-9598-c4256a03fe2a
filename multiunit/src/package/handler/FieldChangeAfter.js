import {isArray, isEmpty, isMultipleUnit, multiUnitPlacesDecimal} from "../utils";
import {BaseHandler} from "./BaseHandler";
import {emitEvent} from "../events";

const key_selected_price_book_product = 'key_selected_price_book_product';//选择价目表返回的价目表明细数据

export class FieldChangeAfter extends BaseHandler {

    constructor(multiUnitContext) {
        super(multiUnitContext);
    }

    async handleEvent(options, pluginExecResult) {
        let {fieldName, objApiName, masterObjApiName} = options || {};
        if (masterObjApiName === objApiName) {//变更的是主对象字段，不处理
            return;
        }
        let {quantity, actual_unit, other_unit, product_id, price_book_product_id, price_book_id} = this.getDetailFields(objApiName);
        if (fieldName === quantity) {//变更的是数量
            return await this.handleQuantityChanged(...arguments);
        } else if ([actual_unit, other_unit].includes(fieldName)) {//变更的是单位、其他单位字段
            return await this.handleUnitChanged(...arguments);
        } else if ([product_id, price_book_product_id].includes(fieldName)) {//变更的是产品、价目表产品字段
            return await this.handleSKUChanged(...arguments);
        } else if ([price_book_id].includes(fieldName)) {//变更的是价目表字段
            return await this.handlePriceBookChanged(...arguments);
        }
    }

    // 更新数据的价目表明细id和价目表id
    _updatePriceBookInfo(data = {}, changePriceBookProduct = {}, allFields = {}){
        let {price_book_product_id, price_book_id} = allFields;
        let {id, _id, name, containerDocument} = changePriceBookProduct;
        Object.assign(data, {
            [price_book_product_id]: id || _id,
            [`${price_book_product_id}__r`]: name
        });
        if(containerDocument){
            Object.assign(data, {
                [price_book_id]: containerDocument.pricebook_id,
                [`${price_book_id}__r`]: containerDocument.pricebook_id__r
            });
        }
    }

    async _quantityChange(options, _dataIndex){
        let {dataGetter, objApiName, dataIndex, changeData} = options || {};
        let {getMasterData, getDetail, getDescribe} = dataGetter;
        let _changeData = this.getChangeData(changeData, _dataIndex);//web端的changeData是多个数据，小程序是单个值

        let detailDataList = getDetail && getDetail(objApiName);

        let objectData = detailDataList && detailDataList.length && detailDataList.find(detailData => {
            let dataIndex = this.getDataIndex(detailData);
            return _dataIndex === dataIndex;
        });

        let changedData = Object.assign({}, objectData, _changeData);

        let {quantity, is_multiple_unit, actual_unit, base_unit_count, conversion_ratio, stat_unit_count, other_unit, other_unit_quantity, price_book_product_id, price_book_id} = this.getDetailFields(objApiName);
        let isMultiUnit = isMultipleUnit(changedData, is_multiple_unit);
        if (isMultiUnit) {
            let masterData = getMasterData && getMasterData();
            let multiUnitResult = await this.calcPrice(masterData, changedData, objApiName, options);
            let {
                base_unit_count: baseUnitCount,
                conversion_ratio: conversionRatio,
                stat_unit_count: statUnitCount,
                other_unit_quantity: otherUnitQuantity,
                needChangePriceBookProduct, changePriceBookProduct
            } = multiUnitResult || {};
            Object.assign(_changeData, {
                [base_unit_count]: baseUnitCount,
                [conversion_ratio]: conversionRatio,
                [stat_unit_count]: statUnitCount,
                [other_unit_quantity]: otherUnitQuantity
            });
            if (needChangePriceBookProduct && changePriceBookProduct) {
                this._updatePriceBookInfo(_changeData, changePriceBookProduct, {price_book_product_id, price_book_id})
            }
        } else {
            let objectDescribe = getDescribe && getDescribe(objApiName);
            let {[actual_unit]: actualUnit, [quantity]: count} = changedData;
            let actualUnitField = objectDescribe && objectDescribe.fields && objectDescribe.fields[actual_unit];
            let selectedOption = actualUnitField && actualUnitField.options && actualUnitField.options.find(it => it.value === actualUnit);
            let actualUnitLabel = selectedOption && selectedOption.label || '';
            let statUnitCount = (count + actualUnitLabel);
            Object.assign(_changeData, {
                [base_unit_count]: count,
                [conversion_ratio]: 1,
                [stat_unit_count]: statUnitCount,
                [other_unit]: null,
                [other_unit_quantity]: null,
            });
        }
    }

    async handleQuantityChanged(options) {
        let {dataGetter, objApiName, dataIndex, changeData} = options || {};
        // let {getMasterData, getDetail, getDescribe} = dataGetter;
        let dataIndexList = isArray(dataIndex) ? dataIndex : [dataIndex];//web端dataIndex为数组，取第一个值；小程序是单个值
        // todo: 改动太大，先循环执行了
        for (let i = 0; i < dataIndexList.length; i++){
            let _dataIndex = dataIndexList[i];
            await this._quantityChange(options, _dataIndex)
        }
    }

    async handleUnitChanged(options) {
        let {masterObjApiName, dataGetter, objApiName, fieldName, dataIndex, changeData, dataUpdater} = options || {};
        let {getMasterData, getDetail} = dataGetter;
        let _dataIndex = isArray(dataIndex) ? dataIndex[0] : dataIndex;//web端dataIndex为数组，取第一个值；小程序是单个值
        let _changeData = this.getChangeData(changeData, _dataIndex);//web端的changeData是多个数据，小程序是单个值
        let detailDataList = getDetail && getDetail(objApiName);
        let objectData = detailDataList && detailDataList.length && detailDataList.find(detailData => {
            let dataIndex = this.getDataIndex(detailData);
            return _dataIndex === dataIndex;
        });
        let changedData = Object.assign({}, objectData, _changeData);
        let {
            price: product_price, is_multiple_unit, price_book_price, quantity,
            actual_unit, other_unit, base_unit_count, conversion_ratio, stat_unit_count, other_unit_quantity,
            price_book_product_id, price_book_id
        } = this.getDetailFields(objApiName);
        let isMultiUnit = isMultipleUnit(changedData, is_multiple_unit);
        let fieldValue = _changeData[fieldName];
        if (!isMultiUnit) {//非多单位产品不处理
            return;
        }
        if (fieldValue) {
            let masterData = getMasterData && getMasterData();
            let multiUnitResult = await this.calcPrice(masterData, changedData, objApiName, options);
            let {
                price = 0,
                base_unit_count: baseUnitCount,
                conversion_ratio: conversionRatio,
                stat_unit_count: statUnitCount,
                other_unit_quantity: otherUnitQuantity,
                places_decimal = null,
                priceBookPrice,
                count,
                needChangePriceBookProduct, changePriceBookProduct
            } = multiUnitResult || {};
            let updateData = {
                [product_price]: price,
                [base_unit_count]: baseUnitCount,
                [conversion_ratio]: conversionRatio,
                [stat_unit_count]: statUnitCount,
                [other_unit_quantity]: otherUnitQuantity,
                [multiUnitPlacesDecimal]: places_decimal
            };
            if (fieldName === actual_unit) {
                if (needChangePriceBookProduct && changePriceBookProduct) {
                    this._updatePriceBookInfo(updateData, changePriceBookProduct, {price_book_product_id, price_book_id})
                }
                /*
                * 先通过dataUpdater更新price_book_price，走计算接口计算price_book_price相关的计算字段
                * https://www.tapd.cn/21309261/bugtrace/bugs/view?bug_id=1121309261001302699
                * https://www.tapd.cn/21309261/bugtrace/bugs/view/1121309261001306747
                * */
                let updateDetailData = {
                    [price_book_price]: priceBookPrice
                };
                let calculateFields = {};
                let objectDescribe = dataGetter.getDescribe && dataGetter.getDescribe(objApiName);
                let priceBookPriceField = objectDescribe && objectDescribe.fields && objectDescribe.fields[price_book_price];
                let relateFields = priceBookPriceField && priceBookPriceField.calculate_relation && priceBookPriceField.calculate_relation.relate_fields;
                if (!isEmpty(relateFields)) {
                    Object.keys(relateFields).forEach(objectApiName => {
                        let calcFields = relateFields[objectApiName];
                        if (calcFields && calcFields.length) {
                            if (!calculateFields[objectApiName]) {
                                calculateFields[objectApiName] = [];
                            }
                            calculateFields[objectApiName].push(...calcFields);
                        }
                    })
                    Object.assign(objectData, _changeData, {
                        [price_book_price]: priceBookPrice
                    })
                    let detailDataMapObj = {};
                    detailDataList && detailDataList.forEach(it => {
                        let dataIndex = this.getDataIndex(it);
                        detailDataMapObj[dataIndex] = it;
                    });
                    let calcResult = await this.multiUnitContext.batchCalculate({
                        masterObjectApiName: masterObjApiName,
                        masterData,
                        detailDataMap: {
                            [objApiName]: detailDataMapObj
                        },
                        modifiedObjectApiName: objApiName,
                        modifiedDataIndexList: [_dataIndex],
                        calculateFields: calculateFields
                    });
                    let detailResult = calcResult && calcResult[objApiName] && calcResult[objApiName][_dataIndex];
                    let masterResult = calcResult && calcResult[masterObjApiName] && calcResult[masterObjApiName][0] || {};
                    Object.assign(updateDetailData, detailResult);
                    dataUpdater.updateMaster(masterResult)
                }
                dataUpdater && dataUpdater.updateDetail(objApiName, _dataIndex, updateDetailData);
            }
            if (count) {
                updateData[quantity] = count;
            }
            Object.assign(_changeData, updateData);
        } else {
            if (fieldName === actual_unit) {
                this.multiUnitContext.alert && this.multiUnitContext.alert(this.i18n('多单位产品单位不可为空'));
                delete _changeData[fieldName];
            } else if (fieldName === other_unit) {
                Object.assign(_changeData, {
                    [other_unit_quantity]: null
                });
            }
        }
    }

    async handleSKUChanged(options) {
        let {dataGetter, dataUpdater, objApiName, dataIndex, changeData, lookupData} = options || {};
        let {getMasterData, getDetail, getDescribe} = dataGetter;
        let _dataIndex = isArray(dataIndex) ? dataIndex[0] : dataIndex;//web端dataIndex为数组，取第一个值；小程序是单个值
        let _changeData = this.getChangeData(changeData, _dataIndex);//web端的changeData是多个数据，小程序是单个值
        let isMultiUnit = lookupData && isMultipleUnit(lookupData, 'is_multiple_unit');
        let {
            product_id, price_book_product_id, price: product_price, quantity,
            actual_unit, other_unit, base_unit_count, conversion_ratio, stat_unit_count, other_unit_quantity, price_book_id
        } = this.getDetailFields(objApiName);
        [actual_unit, other_unit].forEach(it => {
            dataUpdater && dataUpdater.setReadOnly && dataUpdater.setReadOnly({
                objApiName,
                dataIndex: _dataIndex,
                fieldName: it,
                status: !isMultiUnit
            });
        });
        let detailDataList = getDetail && getDetail(objApiName);
        let objectData = detailDataList && detailDataList.length && detailDataList.find(detailData => {
            let dataIndex = this.getDataIndex(detailData);
            return _dataIndex === dataIndex;
        });
        if (lookupData) {
            let objectDescribe = getDescribe && getDescribe(objApiName);
            let {object_describe_api_name, unit, unit__v, actual_unit: actualUnit, _id, product_id: _productId, price, pricebook_sellingprice} = lookupData;
            let isProductObj = object_describe_api_name === 'ProductObj';//选择的是否是产品
            let unitId = isProductObj ? unit : (actualUnit || unit__v);
            let productId = isProductObj ? _id : _productId;
            let priceBookProductId = isProductObj ? null : _id;
            let {[quantity]: count} = objectData;
            if (isMultiUnit) {
                let otherUnitField = objectDescribe && objectDescribe.fields && objectDescribe.fields[other_unit];
                let otherUnitOption = otherUnitField ? await this.getFieldFirstOption(productId, unitId, objApiName) : null;
                let otherUnitId = otherUnitOption && otherUnitOption.value;
                let masterData = getMasterData && getMasterData();
                let multiUnitResult = await this.calcPrice(masterData, {
                    [product_id]: productId,
                    [price_book_product_id]: priceBookProductId,
                    [actual_unit]: unitId,
                    [other_unit]: otherUnitId,
                    [quantity]: count
                }, objApiName, options);
                let {
                    price = 0, unitId: actualUnit,
                    base_unit_count: baseUnitCount,
                    conversion_ratio: conversionRatio,
                    stat_unit_count: statUnitCount,
                    other_unit: otherUnit,
                    other_unit_quantity: otherUnitQuantity,
                    places_decimal = null, count: _quantity,
                    needChangePriceBookProduct, changePriceBookProduct
                } = multiUnitResult || {};
                let updateData = {
                    [product_price]: price,
                    [actual_unit]: actualUnit,
                    [base_unit_count]: baseUnitCount,
                    [conversion_ratio]: conversionRatio,
                    [stat_unit_count]: statUnitCount,
                    [other_unit]: otherUnit,
                    [other_unit_quantity]: otherUnitQuantity,
                    [multiUnitPlacesDecimal]: places_decimal
                };
                if (_quantity) {
                    updateData[quantity] = _quantity;
                }
                if (needChangePriceBookProduct && changePriceBookProduct) {
                    this._updatePriceBookInfo(updateData, changePriceBookProduct, {price_book_product_id, price_book_id})
                }
                Object.assign(_changeData, updateData);
            } else {//如果选择的是非多单位产品，处理相关字段
                let actualUnitField = objectDescribe && objectDescribe.fields && objectDescribe.fields[actual_unit];
                let selectedOption = actualUnitField && actualUnitField.options.find(option => option.value === unitId);
                let actualUnitLabel = selectedOption && selectedOption.label || '';
                let statUnitCount = (count + actualUnitLabel);
                Object.assign(_changeData, {
                    [actual_unit]: unitId,
                    [base_unit_count]: count,
                    [conversion_ratio]: 1,
                    [stat_unit_count]: statUnitCount,
                    [other_unit]: null,
                    [other_unit_quantity]: null,
                    [multiUnitPlacesDecimal]: null
                });
                let productPriceField = objectDescribe && objectDescribe.fields && objectDescribe.fields[product_price];
                if (productPriceField && isEmpty(productPriceField.default_value)) {//如果价格字段默认值公式是空的， 则回填价格
                    let p = isProductObj ? price : pricebook_sellingprice;
                    Object.assign(_changeData, {
                        [product_price]: p,
                    })
                }
            }
        } else {//未选择产品时，清空多单位相关字段信息
            Object.assign(_changeData, {
                [actual_unit]: null,
                [base_unit_count]: null,
                [conversion_ratio]: null,
                [stat_unit_count]: null,
                [other_unit]: null,
                [other_unit_quantity]: null,
                [multiUnitPlacesDecimal]: null
            });
        }
    }

    async handlePriceBookChanged(options) {
        let {lookupData} = options || {};
        let priceBookProduct = lookupData && lookupData[key_selected_price_book_product] || lookupData;//选择的价目表明细数据
        await this.handleSKUChanged(Object.assign({}, options, {//走skuChanged逻辑
            lookupData: priceBookProduct
        }))
    }

    async calcPrice(masterData, objectData, objectApiName, options) {
        let {product_id, price_book_product_id, actual_unit, other_unit, quantity} = this.getDetailFields(objectApiName);
        let {[product_id]: productId, [price_book_product_id]: priceBookProductId, [actual_unit]: unitId, [other_unit]: otherUnitId, [quantity]: count} = objectData;
        let param = {
            params: [{productId, priceBookProductId, unitId, otherUnitId, count}],
            masterObjectApiName: masterData && masterData.object_describe_api_name,
            detailObjectApiName: objectApiName,
        };
        await this.multiUnitContext.runPlugin(emitEvent.multiUnit_form_calcPriceParam, Object.assign({}, options, {
            param,
            detailDataList: [objectData]
        }));

        let res = await this.multiUnitContext.calcPriceByUnit(param);
        return res ? res[0] : {
            base_unit_count: null,
            conversion_ratio: null,
            stat_unit_count: null,
            other_unit_quantity: null
        }
    }

    getFieldFirstOption(productId, unitId, objApiName) {
        return this.multiUnitContext.getLazyLoadOptions(productId, objApiName)
            .then(options => {
                return options && options.length && options.find(option => option.value !== unitId);
            });
    }

    getChangeData(changeData, dataIndex) {
        let temp = changeData[dataIndex];
        return temp || changeData;
    }
}