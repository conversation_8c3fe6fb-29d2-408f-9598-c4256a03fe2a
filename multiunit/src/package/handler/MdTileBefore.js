import {BaseHandler} from "./BaseHandler";
// import {events} from "../events";
// import {isMultipleUnit} from "../utils";

export class MdTileBefore extends BaseHandler {

    constructor(multiUnitContext) {
        super(multiUnitContext)
    }

    handleEvent(options, pluginExecResult, event) {
        let objApiName = this.multiUnitContext.getFirstDetailObjApiName();
        let {actual_unit, other_unit} = this.getDetailFields(objApiName);
        return {
            pluginFields:[actual_unit, other_unit]
        }
    }
}