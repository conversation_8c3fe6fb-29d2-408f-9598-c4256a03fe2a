import {BaseHandler} from "./BaseHandler";

export class BatchAddBeforeCalUiEvent extends BaseHandler {

    constructor(multiUnitContext) {
        super(multiUnitContext);
    }

    handleEvent(options, pluginExecResult) {
        let {objApiName} = options;
        let {quantity, price: product_price, price_book_price} = this.getDetailFields(objApiName);
        let preNoCalFields = pluginExecResult && pluginExecResult.preData && pluginExecResult.preData.noCalFields || [];
        let noCalFields = [...preNoCalFields, quantity, product_price];
        let isOpenPriceBook = this.multiUnitContext.isOpenPriceBook();
        if (isOpenPriceBook) {
            noCalFields.push(price_book_price);
        }
        return {noCalFields: noCalFields};
    }
}