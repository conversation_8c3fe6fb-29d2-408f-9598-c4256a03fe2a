/**
 * @Description: 提取多单位基本逻辑；设置字段可编辑，走多单位接口，计算； ps：目前只有web可用，小程序底层api还未对齐web
 * <AUTHOR>
 * @date 2022/9/14
*/
import {isMultipleUnit} from "../utils";
import {BaseHandler} from "./BaseHandler";

/**给其他插件用,订单从历史添加用到,固定搭配多单位子件*/
export class BatchTodoMultiunit extends BaseHandler {

    constructor(multiUnitContext) {
        super(multiUnitContext);
    }

    handleEvent(options) {
        return this.todoMultiunit(options);
    }

    /**
     * @desc 执行多单位逻辑
     * @param list 从对象数据
     * @param objectApiName 主对象apiname
     * @param detailObjectApiName 从对象apiname
     * @param pluginParam 底层插件提供的方法集
     * @param noCalculate 不触发计算
     * @param ignoreFields 忽略字段
     * @param setFieldReadOnly 是否设置相关字段可读/只读
     * @param setNonMultipleUnitField 设置非多单位字段补全
     * @returns {Promise<*>}
     */
    async todoMultiunit(
        {
            list = [],
            objectApiName = '',
            detailObjectApiName = '',
            pluginParam = {},
            noCalculate = false,
            ignoreFields = [],
            setFieldReadOnly = true,
            setNonMultipleUnitField = false,
            updateData = false,
        } = {}
    ) {
        let {
            actual_unit,
            other_unit,
            is_multiple_unit,
            product_id,
            quantity,
            price_book_product_id,
            price: product_price,
            base_unit_count,
            conversion_ratio,
            stat_unit_count,
            other_unit_quantity
        } = this.getDetailFields(detailObjectApiName);

        // 固定搭配设置非多单位产品字段补全
        if (setNonMultipleUnitField) {
            list = list.map((item) => {
                if (isMultipleUnit(item, is_multiple_unit)) return item;
                let objectDescribe = pluginParam.dataGetter.getDescribe(detailObjectApiName) || {};
                let {[actual_unit]: actualUnit, [quantity]: count} = item;
                let actualUnitField = (objectDescribe.fields && objectDescribe.fields[actual_unit]) || {};
                let selectedOption = (actualUnitField.options || []).find(it => it.value === actualUnit) || {};
                let statUnitCount = count + (selectedOption.label || '');
                return Object.assign(item, {
                    [base_unit_count]: count,
                    [conversion_ratio]: 1,
                    [stat_unit_count]: statUnitCount,
                    [other_unit]: null,
                    [other_unit_quantity]: null,
                });
            })
        }
        // 需要计算的多单位rowId
        let modifyIndex = [];
        // 筛选多单位数据
        let params = list && list.length && list.filter(it => {
            let isMul = isMultipleUnit(it, is_multiple_unit);
            if(isMul) modifyIndex.push(it.rowId);
            return isMul;
        }).map(it => {
            let {[product_id]: productId, [price_book_product_id]: priceBookProductId, [actual_unit]: unitId, [other_unit]: otherUnitId, [quantity]: count, rowId} = it;
            return {
                productId, priceBookProductId, unitId, otherUnitId, count, rowId
            }
        });
        // 设置数据字段可编辑
        if (setFieldReadOnly) this.setFieldReadonly({list, pluginParam});
        // 走多单位接口
        if (!params || !params.length) {
            return list;
        }
        let param = {params, detailObjectApiName, masterObjectApiName: objectApiName};
        let calcResult = await this.multiUnitContext.calcPriceByUnit(param);
        if (!calcResult || !calcResult.length) {
            return list;
        }
        // 回填多单位接口数据
        let newList = list.map(item => {
            let {[product_id]: _productId, [actual_unit]: actualUnit, rowId} = item;
            let result = calcResult.find(it => {
                return it && it.productId === _productId && it.unitId === actualUnit && (it.rowId ? it.rowId === rowId : true);
            });
            if (result) {
                let multiUnitData = this.toMultiUnitData(result, detailObjectApiName);
                if(ignoreFields && ignoreFields.length){
                    ignoreFields.forEach(f => {
                        delete multiUnitData[f];
                    })
                }
                if(updateData) pluginParam.dataUpdater.updateDetail(detailObjectApiName, item.rowId, multiUnitData);
                return Object.assign(item, multiUnitData);
            }
            return item;
        });
        if(noCalculate) return newList;
        // 计算多单位数据；因为价格可能有变化，所以只计算价格相关的字段；
        let mdApiName = pluginParam.objectApiName;
        let changeFields = [product_price, base_unit_count, conversion_ratio,];
        changeFields = changeFields.filter(f => !ignoreFields.includes(f));
        let o = {
            operateType: 'mdEdit',
            dataIndex: modifyIndex,
            objApiName: mdApiName,
            changeFields,
        };
        await pluginParam.triggerCalAndUIEvent(o);

        return newList;
    }
}
