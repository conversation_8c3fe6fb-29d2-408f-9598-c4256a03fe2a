/*
 * @Author: your name
 * @Date: 2022-03-21 18:17:47
 * @LastEditTime: 2022-04-02 16:59:43
 * @LastEditors: wangshaoh
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: /plugins/multiunit/src/package/handler/FieldChangeBeforeHandler.js
 */
import {BaseHandler} from "./BaseHandler";
import {isArray} from "../utils";

const UnitOptionsCache = {};

export class FieldChangeBefore extends BaseHandler {

    constructor(multiUnitContext) {
        super(multiUnitContext);
    }

    async handleEvent(opt, pluginExecResult) {
        let {fieldName, objApiName, dataIndex, dataGetter, options} = opt || {};
        let {product_id, actual_unit, other_unit} = this.getDetailFields(objApiName);
        if ([actual_unit, other_unit].includes(fieldName)) {
            let detailDataList = dataGetter.getDetail && dataGetter.getDetail(objApiName);
            let _dataIndex = isArray(dataIndex) ? dataIndex[0] : dataIndex;//web端dataIndex为数组，取第一个值；小程序是单个值
            let objectData = detailDataList && detailDataList.length && detailDataList.find(detailData => {
                let dataIndex = this.getDataIndex(detailData);
                return _dataIndex === dataIndex;
            });
            let productId = objectData && objectData[product_id];
            if (!productId) {
                return {options}
            }
            let usableOptions = UnitOptionsCache[productId];
            if (usableOptions && usableOptions.length) {
                return {options: usableOptions}
            }
            let serverOptions = await this.multiUnitContext.getLazyLoadOptions(productId, objApiName);
            UnitOptionsCache[productId] = serverOptions;
            return {options: serverOptions}
        }
        return {options}
    }
}
