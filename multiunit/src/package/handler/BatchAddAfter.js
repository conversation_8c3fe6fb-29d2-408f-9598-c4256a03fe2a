import {getRealPriceResult, isEmpty, isMultipleUnit} from "../utils";
import {BaseHandler} from "./BaseHandler";
import {emitEvent} from "../events";
import PPM from 'plugin_public_methods'
// import {equals} from "../../../../pluginbase-ava/package";

const key_other_unit_id = 'key_other_unit_id';//其他单位id
export class BatchAddAfter extends BaseHandler {

    constructor(multiUnitContext) {
        super(multiUnitContext);
    }

    handleEvent(options, pluginExecResult) {
        return this.handleMultiUnitCalc(options);
    }

    async handleMultiUnitCalc(options) {
        let {dataGetter, dataUpdater, lookupDatas, lookupField, newDataIndexs, objApiName, masterObjApiName, addDatas} = options || {};
        let {target_api_name} = lookupField || {};
        if (target_api_name !== 'ProductObj' && target_api_name !== 'PriceBookProductObj') {//选择的不是产品也不是价目表产品不处理
            return;
        }
        let {product_id} = this.getDetailFields(objApiName);
        await this.calcQuantityValue(options);
        /* 对lookupDatas中的unit__v字段进行一次补偿，web端通过商品选价目表产品时，返回的数据中没有unit__v
        https://www.tapd.cn/21309261/bugtrace/bugs/view/1121309261001278874 */
        lookupDatas && lookupDatas.length && lookupDatas.forEach(it => {
            let {unit, unit__v, object_describe_api_name} = it;
            if (object_describe_api_name === 'PriceBookProductObj') {
                if (unit__v === undefined || unit__v === null) {
                    it.unit__v = it.product_id__ro ? it.product_id__ro.unit__v : unit;
                }
            } else if (object_describe_api_name === "ProductObj") {
                if (unit__v) {//通过商品选产品，web端返回的unit的label，unit__v是code，兼容下
                    it.unit = unit__v;
                }
            }
        });
        this.setFieldReadonly(options);
        let detailObjDescribe = dataGetter && dataGetter.getDescribe(objApiName);
        let {other_unit, quantity} = this.getDetailFields(objApiName);
        let otherUnitField = detailObjDescribe.fields[other_unit];
        await this.fillOtherUnit(otherUnitField, lookupDatas, objApiName);
        let calcResult;
        let params = this.buildParams(lookupDatas);
        if (params && params.length) {
            let param = {
                params,
                masterObjectApiName: masterObjApiName,
                detailObjectApiName: objApiName
            };
            await this.multiUnitContext.runPlugin(emitEvent.multiUnit_batchAdd_calcPriceParam, Object.assign({}, options, {
                param,
                lookupDatas
            }));
            calcResult = await this.multiUnitContext.calcPriceByUnit(param);
        }
        this.filterPrice(objApiName, options);
        newDataIndexs && newDataIndexs.length && newDataIndexs.forEach((dataIndex, index) => {
            let fad = addDatas.find(d => d.rowId === dataIndex);
            if(!fad || !fad[product_id]) return;
            let lookupData = lookupDatas[index];
            let multiUnitResult = this.getMultiUnitResult(lookupData, calcResult, detailObjDescribe);
            let updateData = multiUnitResult ? this.toMultiUnitData(multiUnitResult, objApiName) : {};
            if (!isEmpty(lookupData._selected_num) && isEmpty(updateData[quantity])) {
                updateData[quantity] = lookupData._selected_num;
            }
            dataUpdater && dataUpdater.updateDetail(objApiName, dataIndex, updateData, false);
        })
    }

    async fillOtherUnit(otherUnitField, selectedList = [], objApiName) {
        if (!otherUnitField || !selectedList || !selectedList.length) {
            return;
        }
        let self = this;
        for (let i = 0; i < selectedList.length; i++) {
            let selected = selectedList[i];
            let isMultiUnit = isMultipleUnit(selected, 'is_multiple_unit');
            if (isMultiUnit) {
                let {object_describe_api_name, _id, product_id, unit, unit__v, actual_unit} = selected;
                let isProduct = object_describe_api_name === 'ProductObj';
                let productId = isProduct ? _id : product_id;
                let unitId = isProduct ? unit : (actual_unit || unit__v);
                let option = await self.getFirstOption(productId, unitId, objApiName);
                selected[key_other_unit_id] = option ? option.value : null;
            }
        }
    }

    getFirstOption(productId, unitId, objApiName) {
        return this.multiUnitContext.getLazyLoadOptions(productId, objApiName)
            .then(options => {
                return options && options.length && options.find(option => option.value !== unitId);
            });
    }

    buildParams(lookupDataList) {
        return lookupDataList && lookupDataList.length && lookupDataList.filter(it => {
            return isMultipleUnit(it, 'is_multiple_unit');
        }).map(it => {
            let {
                _id, product_id, unit, unit__v, actual_unit, object_describe_api_name, _selected_num, [key_other_unit_id]: otherUnitId,
                [getRealPriceResult]: getRealPriceResultData
            } = it;
            let _priceBookProductId = this.getPriceBookProductId(getRealPriceResultData);
            let isProduct = object_describe_api_name === 'ProductObj';
            let productId = isProduct ? _id : product_id;
            let priceBookProductId = isProduct ? _priceBookProductId : _id;
            let unitId = isProduct ? unit : (actual_unit || unit__v);
            let rowId = this.getRowId(it, true);
            return {productId, priceBookProductId, unitId, count: _selected_num || 1, otherUnitId, rowId};
        });
    }

    getMultiUnitResult(selected, multiUnitResults, objectDescribe) {
        let {
            unit, unit__v, actual_unit, object_describe_api_name, price, pricebook_sellingprice, pricebook_price, _selected_num,
            [getRealPriceResult]: getRealPriceResultData
        } = selected;
        let isProduct = object_describe_api_name === 'ProductObj';
        let unitId = isProduct ? unit : (actual_unit || unit__v);
        let orgRowId = this.getRowId(selected);
        let result = multiUnitResults && multiUnitResults.length && multiUnitResults.find(it => {
            let {rowId} = it || {};
            return orgRowId === rowId;
        });
        if (!result) {//如果没有找到计算结果，则特殊创建一个计算结果
            let {pricebook_price: _priceBookPrice, selling_price: sellingPrice} = getRealPriceResultData || {};
            let priceBookPrice;
            if (_priceBookPrice !== undefined && _priceBookPrice !== null) {
                priceBookPrice = _priceBookPrice;
            } else {
                priceBookPrice = isProduct ? undefined : pricebook_price;
            }
            let realPrice;
            if (sellingPrice !== undefined && sellingPrice !== null) {
                realPrice = sellingPrice;
            } else {
                realPrice = isProduct ? price : pricebook_sellingprice;
            }
            let count = _selected_num || 1;
            let {api_name: objApiName, fields} = objectDescribe || {};
            let {actual_unit} = this.getDetailFields(objApiName);
            let actualUnitField = fields && fields[actual_unit];
            let selectOption = actualUnitField && actualUnitField.options && actualUnitField.options.find(it => it.value === unitId);
            let unitLabel = selectOption && selectOption.label || '';
            let stat_unit_count = (count + unitLabel);
            result = {
                price: realPrice,
                unitId,
                conversion_ratio: 1,
                base_unit_count: count,
                stat_unit_count,
                priceBookPrice: priceBookPrice,
                count: _selected_num
            }
        }
        return result;
    }

    // 过滤计算字段；这些字段不走计算接口计算
    filterPrice(objApiName, param) {
        let {quantity, price: product_price, price_book_price, price_book_product_id, price_book_discount} = this.getDetailFields(objApiName);
        param.filterFields = (param.filterFields || {});
        param.filterFields[objApiName] = (param.filterFields[objApiName] || []);
        param.filterFields[objApiName].push(quantity, product_price);
        let isOpenPriceBook = this.multiUnitContext.isOpenPriceBook();
        if (isOpenPriceBook){
            price_book_discount = price_book_discount || 'price_book_discount';
            let detailObjDescribe = param.dataGetter.getDescribe(objApiName);
            let priceBookPriceField = detailObjDescribe.fields[price_book_price];
            let variable1 = `$${price_book_product_id}__r.pricebook_sellingprice$`;
            let variable2 = `$${price_book_product_id}__r.discount$`;
            let variable3 = `$${product_price}$`;//$product_price$*$price_book_discount$
            let variable4 = `$${price_book_discount}$`;
            let defaultFormula = priceBookPriceField && priceBookPriceField.default_value;

            if(`${variable1}*${variable2}` == defaultFormula || `${variable3}*${variable4}` == defaultFormula){
                // param.filterFields[objApiName].push(price_book_price);

                // 原过滤方法，与 bom 逻辑冲突，换了一种过滤计算的写法
                // 多单位数据计算接口过滤 price_book_price 字段计算
                const _excludeFields = {
                    [objApiName]: param.addDatas.reduce((pre, cur, i) => {
                        if (isMultipleUnit(param.lookupDatas[i], 'is_multiple_unit')) {
                            pre[cur.rowId] = [{fieldName: price_book_price, order: 1}];
                        }
                        return pre;
                    }, {})
                }
                const _parseParam = param.parseParam;
                param.parseParam = (p = {}) => {
                    if (_parseParam) p = _parseParam(p);
                    p.excludedDetailCalculateFields = p.excludedDetailCalculateFields || {};
                    this.mergeCalculateFields(p.excludedDetailCalculateFields, _excludeFields);
                    return p;
                }
            }
        }
    }

      // 合并计算过滤字段；
      mergeCalculateFields(base, self){
        PPM.each(self, (val, key) => {
            base[key] = base[key] || {};
            PPM.each(val, (arr, rowId) => {
                if(base[key][rowId]){
                    base[key][rowId] = base[key][rowId].concat(arr)
                }else{
                    base[key][rowId] = arr;
                }
            })
        })
    }

    setFieldReadonly(options) {
        let {dataUpdater, lookupDatas, newDataIndexs, objApiName} = options || {};
        let {actual_unit, other_unit} = this.getDetailFields(objApiName);
        newDataIndexs && newDataIndexs.length && newDataIndexs.forEach((dataIndex, index) => {
            let lookupData = lookupDatas[index];
            let isMultiUnit = isMultipleUnit(lookupData, 'is_multiple_unit');
            dataUpdater && dataUpdater.setReadOnly && dataUpdater.setReadOnly({
                objApiName,
                dataIndex: dataIndex,
                fieldName: [actual_unit, other_unit],
                status: !isMultiUnit
            });
            // 多单位产品的单位字段，设为必填
            if(isMultiUnit){
                dataUpdater && dataUpdater.setRequired && dataUpdater.setRequired({
                    objApiName,
                    dataIndex: dataIndex,
                    fieldName: [actual_unit],
                    status: true
                });
            }
        });
    }

    getPriceBookProductId(getRealPriceResult) {
        if (getRealPriceResult) {
            let {_id, object_describe_api_name} = getRealPriceResult;
            return object_describe_api_name === 'PriceBookProductObj' ? _id : null;
        }
    }

    async calcQuantityValue(options) {
        let {dataGetter, lookupDatas, newDataIndexs, objApiName, masterObjApiName} = options || {};
        let isCalcQuantity = lookupDatas && lookupDatas.length && lookupDatas.some(lookupData => {
            return isEmpty(lookupData._selected_num);
        });
        if (isCalcQuantity) {//如果选择的sku数据中_selected_num为空，则通过计算接口来计算出数量的值
            let {quantity} = this.getDetailFields(objApiName);
            let masterData = dataGetter.getMasterData();
            let detailDataList = dataGetter.getDetail(objApiName);
            let detailObjDescribe = dataGetter && dataGetter.getDescribe(objApiName);
            let quantityField = detailObjDescribe.fields[quantity];
            let {default_is_expression, default_value} = quantityField || {};
            if (default_is_expression) {
                let detailDataMapObj = {};
                detailDataList && detailDataList.forEach(it => {
                    let dataIndex = this.getDataIndex(it);
                    detailDataMapObj[dataIndex] = it;
                });
                lookupDatas.forEach((lookupData, index) => lookupData.dataIndex = newDataIndexs[index]);//为lookupData的dataIndex赋值
                let calcResult = await this.multiUnitContext.batchCalculate({
                    masterObjectApiName: masterObjApiName,
                    masterData,
                    detailDataMap: {
                        [objApiName]: detailDataMapObj
                    },
                    modifiedObjectApiName: objApiName,
                    modifiedDataIndexList: newDataIndexs,
                    calculateFields: {//计算数量字段
                        [objApiName]: [{
                            fieldName: quantity,
                            order: 1
                        }]
                    }
                }).catch(err => {
                    this.multiUnitContext.alert(err);
                    return null;
                });
                let detailResult = calcResult && calcResult[objApiName];
                if (detailResult) {
                    lookupDatas.forEach(lookupData => {
                        let dataIndex = lookupData.dataIndex;
                        let result = detailResult[dataIndex];//通过dataIndex取结果
                        lookupData._selected_num = result && result[quantity] || 1;
                    })
                } else {
                    lookupDatas.forEach(lookupData => {
                        lookupData._selected_num = (default_value || 1)
                    })
                }
            } else {
                lookupDatas.forEach(lookupData => {
                    lookupData._selected_num = (default_value || 1)
                })
            }
        }
    }
}