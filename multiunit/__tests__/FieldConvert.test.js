import { FieldConvert } from '../src/package/FieldConvert';

describe('FieldConvert', () => {
    let fieldConvert;
    let mockPluginParam;

    beforeEach(() => {
        mockPluginParam = {
            params: {
                fieldMapping: {
                    custom_master_field: 'custom_master_value'
                },
                details: [
                    {
                        objectApiName: 'TestObject',
                        fieldMapping: {
                            custom_detail_field: 'custom_detail_value'
                        }
                    },
                    {
                        objectApiName: 'AnotherObject',
                        fieldMapping: {
                            another_field: 'another_value'
                        }
                    }
                ]
            }
        };
        fieldConvert = new FieldConvert(mockPluginParam);
    });

    test('should initialize correctly', () => {
        expect(fieldConvert).toBeDefined();
        expect(fieldConvert.masterFieldMap).toEqual({
            custom_master_field: 'custom_master_value'
        });
        expect(fieldConvert.detailFieldMap).toEqual({
            'TestObject': {
                custom_detail_field: 'custom_detail_value'
            },
            'AnotherObject': {
                another_field: 'another_value'
            }
        });
    });

    test('should handle null or undefined plugin param', () => {
        const emptyFieldConvert = new FieldConvert(null);
        expect(emptyFieldConvert.masterFieldMap).toBeUndefined();
        expect(emptyFieldConvert.detailFieldMap).toEqual({});

        const undefinedFieldConvert = new FieldConvert(undefined);
        expect(undefinedFieldConvert.masterFieldMap).toBeUndefined();
        expect(undefinedFieldConvert.detailFieldMap).toEqual({});
    });

    test('should handle empty details array', () => {
        const emptyDetailsParam = {
            params: {
                fieldMapping: {
                    field: 'value'
                },
                details: []
            }
        };
        const emptyDetailsFieldConvert = new FieldConvert(emptyDetailsParam);
        expect(emptyDetailsFieldConvert.detailFieldMap).toEqual({});
    });

    test('should get master fields', () => {
        const masterFields = fieldConvert.getMasterFields();
        expect(masterFields).toEqual({
            form_account_id: 'form_account_id',
            form_partner_id: 'form_partner_id',
            form_price_book_id: 'form_price_book_id',
            custom_master_field: 'custom_master_value'
        });
    });

    test('should get detail fields for specific object', () => {
        const detailFields = fieldConvert.getDetailFields('TestObject');
        expect(detailFields).toEqual({
            price: 'product_price',
            quantity: 'quantity',
            product_id: 'product_id',
            price_book_product_id: 'price_book_product_id',
            price_book_id: 'price_book_id',
            unit: 'unit',
            actual_unit: 'actual_unit',
            conversion_ratio: 'conversion_ratio',
            base_unit_count: 'base_unit_count',
            stat_unit_count: 'stat_unit_count',
            other_unit: 'other_unit',
            other_unit_quantity: 'other_unit_quantity',
            is_multiple_unit: 'is_multiple_unit',
            price_book_price: 'price_book_price',
            custom_detail_field: 'custom_detail_value'
        });
    });

    test('should get detail fields for non-existent object', () => {
        const detailFields = fieldConvert.getDetailFields('NonExistentObject');
        expect(detailFields).toEqual({
            price: 'product_price',
            quantity: 'quantity',
            product_id: 'product_id',
            price_book_product_id: 'price_book_product_id',
            price_book_id: 'price_book_id',
            unit: 'unit',
            actual_unit: 'actual_unit',
            conversion_ratio: 'conversion_ratio',
            base_unit_count: 'base_unit_count',
            stat_unit_count: 'stat_unit_count',
            other_unit: 'other_unit',
            other_unit_quantity: 'other_unit_quantity',
            is_multiple_unit: 'is_multiple_unit',
            price_book_price: 'price_book_price'
        });
    });

    test('should get detail object api names', () => {
        const apiNames = fieldConvert.getDetailObjApiNames();
        expect(apiNames).toEqual(['TestObject', 'AnotherObject']);
    });

    test('should handle missing details in plugin param', () => {
        const noDetailsParam = {
            params: {
                fieldMapping: {
                    field: 'value'
                }
            }
        };
        const noDetailsFieldConvert = new FieldConvert(noDetailsParam);
        expect(noDetailsFieldConvert.detailFieldMap).toEqual({});
        expect(noDetailsFieldConvert.getDetailObjApiNames()).toEqual([]);
    });
}); 