import MultiUnitPlugin from '../src/index';
import {HandlerManager} from '../src/package/HandlerManager';
import {MultiUnitContext} from '../src/package/MultiUnitContext';
import {events} from '../src/package/events';

describe('MultiUnit Plugin', () => {
    let multiUnitPlugin;
    let mockPluginService;
    let mockPluginParam;

    beforeEach(() => {
        // Mock plugin service
        mockPluginService = {
            request: jest.fn(),
            runPlugin: jest.fn(),
            api: {
                bizLog: {
                    log: jest.fn()
                }
            }
        };

        // Mock plugin parameters
        mockPluginParam = {
            describe: {
                objectApiName: 'test_object',
                pluginApiName: 'test_plugin'
            },
            dataGetter: {
                getDetail: jest.fn()
            },
            dataUpdater: {
                updateDetail: jest.fn()
            }
        };

        multiUnitPlugin = new MultiUnitPlugin(mockPluginService, mockPluginParam);
    });

    test('should initialize correctly', () => {
        expect(multiUnitPlugin).toBeDefined();
        expect(multiUnitPlugin.handlerManager).toBeInstanceOf(HandlerManager);
        expect(mockPluginService.api.bizLog.log).toHaveBeenCalledWith({
            eventId: 'fs-crm-sfa-plugin-used',
            eventType: 'PROD',
            eventName: 'pv',
            apiName: 'test_object',
            module: 'test_plugin',
        });
    });

    test('should register all events', () => {
        const hooks = multiUnitPlugin.apply();
        expect(Array.isArray(hooks)).toBe(true);
        expect(hooks.length).toBe(Object.keys(events).length);
        
        hooks.forEach(hook => {
            expect(hook).toHaveProperty('event');
            expect(hook).toHaveProperty('functional');
            expect(typeof hook.functional).toBe('function');
        });
    });

    test('should handle events correctly', async () => {
        const mockEvent = 'test.event';
        const mockResult = { data: 'test' };
        const mockOptions = { option: 'test' };

        // Mock handleEvent method
        multiUnitPlugin.handlerManager.handleEvent = jest.fn();
        
        // Get hooks and find our test event handler
        const hooks = multiUnitPlugin.apply();
        const handler = hooks[0].functional;
        
        // Call the handler
        await handler(mockResult, mockOptions);
        
        // Verify handleEvent was called with correct parameters
        expect(multiUnitPlugin.handlerManager.handleEvent).toHaveBeenCalledWith(
            hooks[0].event,
            mockResult,
            mockOptions
        );
    });
}); 