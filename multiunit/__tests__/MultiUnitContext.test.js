import { MultiUnitContext } from '../src/package/MultiUnitContext';
import { MultiUnitApi } from '../src/package/MultiUnitApi';
import { FieldConvert } from '../src/package/FieldConvert';
import mock from '../testmock/mock';

const { multiUnitContextMock, createTestOptions, createDetailData, createMockHandler } = mock;

jest.mock('../src/package/MultiUnitApi');
jest.mock('../src/package/FieldConvert');

describe('MultiUnitContext', () => {
    let multiUnitContext;
    let mockPluginService;
    let mockPluginParam;

    beforeEach(() => {
        jest.clearAllMocks();

        mockPluginService = {
            api: {
                request: jest.fn(),
                alert: jest.fn(),
                showLoading: jest.fn(),
                hideLoading: jest.fn()
            },
            run: jest.fn()
        };

        mockPluginParam = {
            bizStateConfig: {
                openPriceList: true,
                multi_unit_price_book: '1'
            }
        };

        // 设置 FieldConvert 的模拟实现
        FieldConvert.prototype.getMasterFields = jest.fn().mockReturnValue({ field1: 'value1' });
        FieldConvert.prototype.getDetailFields = jest.fn().mockReturnValue({ field2: 'value2' });
        FieldConvert.prototype.getDetailObjApiNames = jest.fn().mockReturnValue(['objApi1', 'objApi2']);

        // 设置 MultiUnitApi 的模拟实现
        MultiUnitApi.prototype.calc = jest.fn().mockResolvedValue({ success: true });
        MultiUnitApi.prototype.getOptions = jest.fn().mockResolvedValue(['option1', 'option2']);
        MultiUnitApi.prototype.batchCalculate = jest.fn().mockResolvedValue({ results: [] });

        multiUnitContext = new MultiUnitContext(mockPluginService, mockPluginParam);
        multiUnitContext = createMockHandler(multiUnitContext, { detailFields: multiUnitContextMock.detailFields });
    });

    test('should initialize correctly', () => {
        expect(multiUnitContext).toBeDefined();
        expect(multiUnitContext.pluginService).toBe(mockPluginService);
        expect(multiUnitContext.pluginParam).toBe(mockPluginParam);
        expect(multiUnitContext.multiUnitApi).toBeInstanceOf(MultiUnitApi);
        expect(multiUnitContext.fieldConvert).toBeInstanceOf(FieldConvert);
    });

    test('should show alert', () => {
        const message = 'Test alert';
        multiUnitContext.alert(message);
        expect(mockPluginService.api.alert).toHaveBeenCalledWith(message);
    });

    test('should get master fields', () => {
        const fields = multiUnitContext.getMasterFields();
        expect(fields).toEqual({ field1: 'value1' });
        expect(multiUnitContext.fieldConvert.getMasterFields).toHaveBeenCalled();
    });

    test('should get detail fields', () => {
        const objApiName = 'testApi';
        const fields = multiUnitContext.getDetailFields(objApiName);
        expect(fields).toEqual(multiUnitContextMock.detailFields);
    });

    test('should get first detail object api name', () => {
        const apiName = multiUnitContext.getFirstDetailObjApiName();
        expect(apiName).toBe('objApi1');
        expect(multiUnitContext.fieldConvert.getDetailObjApiNames).toHaveBeenCalled();
    });

    test('should calculate price by unit', async () => {
        const param = { price: 100 };
        const result = await multiUnitContext.calcPriceByUnit(param);
        
        expect(result).toEqual({ success: true });
        expect(mockPluginService.api.showLoading).toHaveBeenCalled();
        expect(mockPluginService.api.hideLoading).toHaveBeenCalled();
        expect(multiUnitContext.multiUnitApi.calc).toHaveBeenCalledWith(param);
    });

    test('should handle error in calcPriceByUnit', async () => {
        const error = new Error('Test error');
        MultiUnitApi.prototype.calc.mockRejectedValue(error);

        await expect(multiUnitContext.calcPriceByUnit({})).rejects.toThrow(error);
        expect(mockPluginService.api.showLoading).toHaveBeenCalled();
        expect(mockPluginService.api.hideLoading).toHaveBeenCalled();
    });

    test('should get lazy load options', async () => {
        const productId = 'product1';
        const objApiName = 'testApi';
        const result = await multiUnitContext.getLazyLoadOptions(productId, objApiName);
        
        expect(result).toEqual(['option1', 'option2']);
        expect(mockPluginService.api.showLoading).toHaveBeenCalled();
        expect(mockPluginService.api.hideLoading).toHaveBeenCalled();
        expect(multiUnitContext.multiUnitApi.getOptions).toHaveBeenCalledWith(productId, objApiName);
    });

    test('should handle error in getLazyLoadOptions', async () => {
        const error = new Error('Test error');
        MultiUnitApi.prototype.getOptions.mockRejectedValue(error);

        await expect(multiUnitContext.getLazyLoadOptions('', '')).rejects.toThrow(error);
        expect(mockPluginService.api.showLoading).toHaveBeenCalled();
        expect(mockPluginService.api.hideLoading).toHaveBeenCalled();
    });

    test('should batch calculate', async () => {
        const opts = { items: [] };
        const result = await multiUnitContext.batchCalculate(opts);
        
        expect(result).toEqual({ results: [] });
        expect(mockPluginService.api.showLoading).toHaveBeenCalled();
        expect(mockPluginService.api.hideLoading).toHaveBeenCalled();
        expect(multiUnitContext.multiUnitApi.batchCalculate).toHaveBeenCalledWith(opts);
    });

    test('should handle error in batchCalculate', async () => {
        const error = new Error('Test error');
        MultiUnitApi.prototype.batchCalculate.mockRejectedValue(error);

        await expect(multiUnitContext.batchCalculate({})).rejects.toThrow(error);
        expect(mockPluginService.api.showLoading).toHaveBeenCalled();
        expect(mockPluginService.api.hideLoading).toHaveBeenCalled();
    });

    test('should check if price book is open', () => {
        expect(multiUnitContext.isOpenPriceBook()).toBe(true);
        
        multiUnitContext.pluginParam.bizStateConfig.openPriceList = false;
        expect(multiUnitContext.isOpenPriceBook()).toBe(false);
    });

    test('should check if multi unit price book is open', () => {
        expect(multiUnitContext.isOpenMultiUnitPriceBook()).toBe(true);
        
        multiUnitContext.pluginParam.bizStateConfig.multi_unit_price_book = '0';
        expect(multiUnitContext.isOpenMultiUnitPriceBook()).toBe(false);

        multiUnitContext.pluginParam.bizStateConfig.multi_unit_price_book = true;
        expect(multiUnitContext.isOpenMultiUnitPriceBook()).toBe(true);
    });

    test('should run plugin', async () => {
        mockPluginService.run.mockResolvedValue({
            StatusCode: 0,
            Value: { data: 'test' }
        });

        const result = await multiUnitContext.runPlugin('test.plugin', { param: 'test' });
        expect(result).toEqual({ data: 'test' });
        expect(mockPluginService.run).toHaveBeenCalledWith('test.plugin', { param: 'test' });
    });

    test('should handle failed plugin run', async () => {
        mockPluginService.run.mockResolvedValue({
            StatusCode: 1,
            Value: null
        });

        const result = await multiUnitContext.runPlugin('test.plugin', {});
        expect(result).toBeUndefined();
    });

    test('should show and hide loading', () => {
        multiUnitContext.showLoading();
        expect(mockPluginService.api.showLoading).toHaveBeenCalled();

        multiUnitContext.hideLoading();
        expect(mockPluginService.api.hideLoading).toHaveBeenCalled();
    });
}); 