import { events, emitEvent } from '../src/package/events';

describe('events 常量对象测试', () => {
    test('events 对象应该被冻结（不可修改）', () => {
        expect(Object.isFrozen(events)).toBe(true);
    });

    test('events 对象应该包含所有预定义的事件常量', () => {
        const expectedEvents = [
            'form_render_after',
            'md_batchAdd_after',
            'field_change_before',
            'field_change_after',
            'md_edit_before',
            'md_edit_after',
            'md_batchAdd_before_cal_uievent',
            'multiunit_batchTodoMultiunit',
            'set_field_readonly',
            'md_batchAdd_selectSkuConfig',
            'priceService_batchAdd_getPriceParam',
            'priceService_batchAdd_matchGetPriceResult',
            'priceService_form_getPriceParam',
            'priceService_form_matchGetPriceResult',
            'priceService_batchAddAfter_parseData',
            'bom_md_edit_after',
            'bom_md_batchAdd_end',
            'md_render_before',
            'md_copy_after',
            'md_tile_before'
        ];

        expectedEvents.forEach(eventName => {
            expect(events).toHaveProperty(eventName);
            expect(typeof events[eventName]).toBe('string');
        });
    });

    test('events 对象的值应该是正确的字符串', () => {
        expect(events.form_render_after).toBe('form.render.after');
        expect(events.md_batchAdd_after).toBe('md.batchAdd.after');
        expect(events.field_change_before).toBe('field.change.before');
        // 测试几个关键的事件值
        expect(events.set_field_readonly).toBe('multiunit.setFieldReadOnly');
        expect(events.md_tile_before).toBe('md.tile.before');
    });
});

describe('emitEvent 常量对象测试', () => {
    test('emitEvent 对象应该被冻结（不可修改）', () => {
        expect(Object.isFrozen(emitEvent)).toBe(true);
    });

    test('emitEvent 对象应该包含所有预定义的事件常量', () => {
        const expectedEmitEvents = [
            'multiUnit_batchAdd_calcPriceParam',
            'multiUnit_form_calcPriceParam'
        ];

        expectedEmitEvents.forEach(eventName => {
            expect(emitEvent).toHaveProperty(eventName);
            expect(typeof emitEvent[eventName]).toBe('string');
        });
    });

    test('emitEvent 对象的值应该是正确的字符串', () => {
        expect(emitEvent.multiUnit_batchAdd_calcPriceParam).toBe('multiUnit.batchAdd.calcPriceParam');
        expect(emitEvent.multiUnit_form_calcPriceParam).toBe('multiUnit.form.calcPriceParam');
    });

    test('不能修改 emitEvent 对象的属性', () => {
        expect(() => {
            emitEvent.newProperty = 'test';
        }).toThrow();
    });
}); 