import {FieldChangeAfter} from '../../src/package/handler/FieldChangeAfter';
import {MultiUnitContext} from '../../src/package/MultiUnitContext';
import {isMultipleUnit, isArray, isEmpty, multiUnitPlacesDecimal} from '../../src/package/utils';
import {emitEvent} from '../../src/package/events';

jest.mock('../../src/package/utils', () => ({
    isMultipleUnit: jest.fn(),
    isArray: jest.fn(),
    isEmpty: jest.fn(),
    multiUnitPlacesDecimal: 'multiUnitPlacesDecimal'
}));

const quantity = 'quantity';
const product_id = 'product_id';
const price_book_product_id = 'price_book_product_id';
const actual_unit = 'actual_unit';
const other_unit = 'other_unit';
const unit = 'unit';
const unit__v = 'unit__v';
const price = 'price';
const pricebook_sellingprice = 'pricebook_sellingprice';

describe('FieldChangeAfter', () => {
    let mockMultiUnitContext;
    let mockDataGetter;
    let mockDataUpdater;
    let handler;
    let detailFields;

    beforeEach(() => {
        detailFields = {
            product_id: 'product_id',
            price_book_product_id: 'price_book_product_id',
            price: 'price',
            quantity: 'quantity',
            actual_unit: 'actual_unit',
            other_unit: 'other_unit',
            base_unit_count: 'base_unit_count',
            conversion_ratio: 'conversion_ratio',
            stat_unit_count: 'stat_unit_count',
            other_unit_quantity: 'other_unit_quantity',
            price_book_id: 'price_book_id',
            price_book_price: 'price_book_price',
            is_multiple_unit: 'is_multiple_unit'
        };
        mockMultiUnitContext = {
            alert: jest.fn(),
            i18n: jest.fn(),
            pluginService: {
                api: {
                    i18n: jest.fn(msg => msg)
                }
            },
            calcPriceByUnit: jest.fn().mockResolvedValue([{
                price: 100,
                needChangePriceBookProduct: true,
                changePriceBookProduct: {id: 'pbp1'},
                priceBookPrice: 120
            }]),
            runPlugin: jest.fn(),
            batchCalculate: jest.fn().mockResolvedValue({
                test_obj: {
                    row1: {
                        field1: 'value1',
                        field2: 'value2'
                    }
                },
                master_obj: [
                    {
                        field1: 'value1',
                        field2: 'value2'
                    }
                ]
            }),
            getLazyLoadOptions: jest.fn().mockResolvedValue([
                {value: 'unit1', label: 'Unit 1'},
                {value: 'unit2', label: 'Unit 2'}
            ]),
            getDetailFields: () => ({
                quantity: 'quantity',
                price_book_price: 'price_book_price',
                product_id: 'product_id',
                unit: 'unit',
                actual_unit: 'actual_unit',
                other_unit: 'other_unit',
                price: 'price',
                is_multiple_unit: 'is_multiple_unit',
                price_book_product_id: 'price_book_product_id',
                price_book_id: 'price_book_id',
                base_unit_count: 'base_unit_count',
                conversion_ratio: 'conversion_ratio',
                stat_unit_count: 'stat_unit_count',
                other_unit_quantity: 'other_unit_quantity',
                multiUnitPlacesDecimal: 'multiUnitPlacesDecimal'
            }),
            calcPrice: jest.fn().mockResolvedValue({
                price: '100',
                price_book_price: '100',
                price_book_product_id: '123',
                price_book_id: '456',
                base_unit_count: '1',
                conversion_ratio: '1',
                stat_unit_count: '1',
                other_unit_quantity: '10',
                places_decimal: 2,
                priceBookPrice: '100',
                count: '10',
                needChangePriceBookProduct: false,
                changePriceBookProduct: false,
                calculate_relation: {
                    relate_fields: {
                        'test_obj': ['field1', 'field2']
                    }
                }
            }),
            getFieldFirstOption: jest.fn().mockResolvedValue({
                value: 'unit1',
                label: 'Unit 1'
            }),
            getChangeData: jest.fn().mockImplementation((changeData, dataIndex) => {
                return changeData;
            }),
            getDataIndex: jest.fn().mockImplementation((detailData) => {
                return detailData._dataIndex;
            }),
            isMultipleUnit: jest.fn().mockImplementation((data, field) => {
                return data[field] === true;
            })
        };
        mockDataGetter = {
            getMasterData: jest.fn(),
            getDetail: jest.fn(),
            getDescribe: jest.fn()
        };
        mockDataUpdater = {
            updateDetail: jest.fn(),
            updateMaster: jest.fn(),
            setReadOnly: jest.fn(),
            setRequired: jest.fn()
        };
        handler = new FieldChangeAfter(mockMultiUnitContext);
        isArray.mockImplementation(Array.isArray);
        isEmpty.mockImplementation(val => !val || (Array.isArray(val) && !val.length) || (typeof val === 'object' && !Object.keys(val).length));
        isMultipleUnit.mockImplementation((data, field) => data[field] === true);
    });

    describe('handleEvent', () => {
        it('should not process when field change is on master object', async () => {
            const options = {
                fieldName: 'test_field',
                objApiName: 'test_obj',
                masterObjApiName: 'test_obj'
            };

            const result = await handler.handleEvent(options);
            expect(result).toBeUndefined();
        });

        it('should call handleQuantityChanged when quantity field changes', async () => {
            const options = {
                fieldName: detailFields.quantity,
                objApiName: 'detail_obj',
                masterObjApiName: 'master_obj'
            };

            const spy = jest.spyOn(handler, 'handleQuantityChanged').mockResolvedValue();
            await handler.handleEvent(options);
            expect(spy).toHaveBeenCalledWith(options);
        });
    });

    describe('handleQuantityChanged', () => {
        it('should handle quantity change for multiple unit product', async () => {
            const options = {
                dataGetter: mockDataGetter,
                objApiName: 'detail_obj',
                dataIndex: 'row1',
                changeData: {
                    row1: { quantity: 10 }
                }
            };

            mockDataGetter.getDetail.mockReturnValue([{
                rowId: 'row1',
                is_multiple_unit: true,
                quantity: 5
            }]);

            mockMultiUnitContext.calcPriceByUnit.mockResolvedValue([{
                base_unit_count: 10,
                conversion_ratio: 1,
                stat_unit_count: '10个',
                other_unit_quantity: null
            }]);

            await handler.handleQuantityChanged(options);

            expect(mockMultiUnitContext.calcPriceByUnit).toHaveBeenCalled();
            expect(options.changeData.row1).toEqual({
                quantity: 10,
                base_unit_count: 10,
                conversion_ratio: 1,
                stat_unit_count: '10个',
                other_unit_quantity: null
            });
        });
    });

    describe('handleUnitChanged', () => {
        it('should handle unit change for multiple unit product', async () => {
            const options = {
                dataGetter: mockDataGetter,
                dataUpdater: mockDataUpdater,
                objApiName: 'detail_obj',
                fieldName: detailFields.actual_unit,
                dataIndex: 'row1',
                changeData: {
                    row1: { actual_unit: 'unit1' }
                }
            };

            mockDataGetter.getDetail.mockReturnValue([{
                rowId: 'row1',
                is_multiple_unit: true,
                actual_unit: 'unit1'
            }]);

            mockMultiUnitContext.calcPriceByUnit.mockResolvedValue([{
                price: 100,
                base_unit_count: 10,
                conversion_ratio: 1,
                stat_unit_count: '10个',
                other_unit_quantity: null,
                places_decimal: 2,
                priceBookPrice: 90
            }]);

            mockDataGetter.getDescribe.mockReturnValue({
                fields: {
                    price_book_price: {
                        calculate_relation: {
                            relate_fields: {
                                detail_obj: ['field1', 'field2']
                            }
                        }
                    }
                }
            });

            mockMultiUnitContext.batchCalculate.mockResolvedValue({
                detail_obj: {
                    row1: {
                        field1: 'value1',
                        field2: 'value2'
                    }
                },
                master_obj: [
                    {
                        field1: 'value1',
                        field2: 'value2'
                    }
                ]
            });

            await handler.handleUnitChanged(options);

            expect(mockMultiUnitContext.calcPriceByUnit).toHaveBeenCalled();
            expect(mockDataUpdater.updateDetail).toHaveBeenCalledWith(
                'detail_obj',
                'row1',
                expect.objectContaining({
                    price_book_price: 90,
                    field1: 'value1',
                    field2: 'value2'
                })
            );
        });

        it('should show alert when actual unit is cleared for multiple unit product', async () => {
            const options = {
                dataGetter: mockDataGetter,
                objApiName: 'detail_obj',
                fieldName: detailFields.actual_unit,
                dataIndex: 'row1',
                changeData: {
                    row1: { actual_unit: null }
                }
            };

            mockDataGetter.getDetail.mockReturnValue([{
                rowId: 'row1',
                is_multiple_unit: true
            }]);

            await handler.handleUnitChanged(options);

            expect(mockMultiUnitContext.alert).toHaveBeenCalled();
            expect(options.changeData.row1.actual_unit).toBeUndefined();
        });
    });

    describe('handleSKUChanged', () => {
        it('should handle SKU change for multiple unit product', async () => {
            const options = {
                dataGetter: mockDataGetter,
                dataUpdater: mockDataUpdater,
                objApiName: 'detail_obj',
                dataIndex: 'row1',
                changeData: {
                    row1: {}
                },
                lookupData: {
                    is_multiple_unit: true,
                    object_describe_api_name: 'ProductObj',
                    unit: 'unit1',
                    _id: 'prod1',
                    price: 100
                }
            };

            mockDataGetter.getDetail.mockReturnValue([{
                rowId: 'row1',
                quantity: 5
            }]);

            mockMultiUnitContext.getLazyLoadOptions.mockResolvedValue([
                { value: 'unit1', label: '个' },
                { value: 'unit2', label: '箱' }
            ]);

            mockMultiUnitContext.calcPriceByUnit.mockResolvedValue([{
                price: 100,
                unitId: 'unit1',
                base_unit_count: 5,
                conversion_ratio: 1,
                stat_unit_count: '5个',
                other_unit: 'unit2',
                other_unit_quantity: 2,
                places_decimal: 2
            }]);

            await handler.handleSKUChanged(options);

            expect(mockDataUpdater.setReadOnly).toHaveBeenCalledWith({
                objApiName: 'detail_obj',
                dataIndex: 'row1',
                fieldName: 'actual_unit',
                status: false
            });

            expect(options.changeData.row1).toEqual(expect.objectContaining({
                price: 100,
                actual_unit: 'unit1',
                base_unit_count: 5,
                conversion_ratio: 1,
                stat_unit_count: '5个',
                other_unit: 'unit2',
                other_unit_quantity: 2,
                multiUnitPlacesDecimal: 2
            }));
        });
    });

    describe('handlePriceBookChanged', () => {
        it('should delegate to handleSKUChanged with price book product data', async () => {
            const options = {
                lookupData: {
                    key_selected_price_book_product: {
                        is_multiple_unit: true,
                        product_id: 'prod1'
                    }
                }
            };

            const spy = jest.spyOn(handler, 'handleSKUChanged').mockResolvedValue();
            await handler.handlePriceBookChanged(options);

            expect(spy).toHaveBeenCalledWith(expect.objectContaining({
                lookupData: {
                    is_multiple_unit: true,
                    product_id: 'prod1'
                }
            }));
        });
    });

    describe('calcPrice', () => {
        it('should calculate price with correct parameters', async () => {
            const masterData = {
                object_describe_api_name: 'master_obj'
            };

            const objectData = {
                product_id: 'prod1',
                price_book_product_id: 'pbp1',
                actual_unit: 'unit1',
                other_unit: 'unit2',
                quantity: 5
            };

            mockMultiUnitContext.calcPriceByUnit.mockResolvedValue([{
                price: 100,
                base_unit_count: 5,
                conversion_ratio: 1,
                stat_unit_count: '5个',
                other_unit_quantity: 2
            }]);

            const result = await handler.calcPrice(masterData, objectData, 'detail_obj', {});

            expect(mockMultiUnitContext.runPlugin).toHaveBeenCalledWith(
                emitEvent.multiUnit_form_calcPriceParam,
                expect.objectContaining({
                    param: {
                        params: [{
                            productId: 'prod1',
                            priceBookProductId: 'pbp1',
                            unitId: 'unit1',
                            otherUnitId: 'unit2',
                            count: 5
                        }],
                        masterObjectApiName: 'master_obj',
                        detailObjectApiName: 'detail_obj'
                    },
                    detailDataList: [objectData]
                })
            );

            expect(result).toEqual({
                price: 100,
                base_unit_count: 5,
                conversion_ratio: 1,
                stat_unit_count: '5个',
                other_unit_quantity: 2
            });
        });
    });
});