import { MdTileBefore } from '../../src/package/handler/MdTileBefore';

describe('MdTileBefore', () => {
    let mockMultiUnitContext;
    let handler;
    let detailFields;

    beforeEach(() => {
        detailFields = {
            product_id: 'product_id',
            price_book_product_id: 'price_book_product_id',
            price: 'price',
            quantity: 'quantity',
            actual_unit: 'actual_unit',
            other_unit: 'other_unit',
            base_unit_count: 'base_unit_count',
            conversion_ratio: 'conversion_ratio',
            stat_unit_count: 'stat_unit_count',
            other_unit_quantity: 'other_unit_quantity',
            price_book_id: 'price_book_id',
            price_book_price: 'price_book_price'
        };

        mockMultiUnitContext = {
            getDetailFields: () => detailFields,
            getFirstDetailObjApiName: jest.fn().mockReturnValue('detail_obj')
        };

        handler = new MdTileBefore(mockMultiUnitContext);
    });

    describe('handleEvent', () => {
        it('should return plugin fields correctly', () => {
            const result = handler.handleEvent();
            
            expect(result).toEqual({
                pluginFields: ['actual_unit', 'other_unit']
            });

            expect(mockMultiUnitContext.getFirstDetailObjApiName).toHaveBeenCalled();
        });
    });
}); 