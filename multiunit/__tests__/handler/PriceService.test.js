import { PriceService } from '../../src/package/handler/PriceService';
import { events } from '../../src/package/events';
import mock from '../../testmock/mock';

const { priceServiceMock, createMockHandler } = mock;

describe('PriceService', () => {
    let mockMultiUnitContext;
    let handler;

    beforeEach(() => {
        mockMultiUnitContext = {
            isOpenMultiUnitPriceBook: jest.fn().mockReturnValue(true),
            getLazyLoadOptions: jest.fn()
        };
        
        handler = new PriceService(mockMultiUnitContext);
        handler = createMockHandler(handler);
    });
    
    afterEach(() => {
        jest.clearAllMocks();
    });

    describe('handleEvent', () => {
        it('should handle md_batchAdd_selectSkuConfig event', () => {
            const result = handler.handleEvent({}, { preData: priceServiceMock.preData }, events.md_batchAdd_selectSkuConfig);
            
            expect(mockMultiUnitContext.isOpenMultiUnitPriceBook).toHaveBeenCalled();
            expect(result).toEqual(priceServiceMock.expectedResults.selectSkuConfig);
        });

        it('should handle priceService_batchAdd_getPriceParam event', () => {
            const { params, lookupDatas } = priceServiceMock.batchAddData;

            handler.handleEvent({ params, lookupDatas }, null, events.priceService_batchAdd_getPriceParam);

            expect(params.fullProductList[0]).toEqual(priceServiceMock.expectedResults.batchAddProduct);
            expect(params.fullProductList[1]).toEqual(priceServiceMock.expectedResults.batchAddNonProduct);
        });

        it('should handle priceService_batchAdd_matchGetPriceResult event', () => {
            const result = handler.handleEvent(
                { lookupData: priceServiceMock.productData },
                { preData: priceServiceMock.preData },
                events.priceService_batchAdd_matchGetPriceResult
            );

            expect(result).toEqual(priceServiceMock.expectedResults.matchGetPriceResult);
        });

        it('should handle priceService_form_getPriceParam event', () => {
            const { params, detailDataList } = priceServiceMock.formData;

            handler.handleEvent(
                { objApiName: 'TestObj', params, detailDataList },
                null,
                events.priceService_form_getPriceParam
            );

            expect(params.fullProductList[0]).toEqual({
                id: 1,
                unit: 'unit1',
                baseUnit: 'baseUnit1'
            });
            expect(params.fullProductList[1]).toEqual({
                id: 2,
                unit: 'unit2',
                baseUnit: 'baseUnit2'
            });
        });

        it('should handle priceService_form_matchGetPriceResult event', () => {
            const detailObjectData = {
                actual_unit: 'unit1'
            };

            const result = handler.handleEvent(
                { objApiName: 'TestObj', detailObjectData },
                { preData: priceServiceMock.preData },
                events.priceService_form_matchGetPriceResult
            );

            expect(result).toEqual(priceServiceMock.expectedResults.matchGetPriceResult);
        });

        it('should handle priceService_batchAddAfter_parseData event', () => {
            const { data, lookupDatas } = priceServiceMock.batchAddAfterData;

            const result = handler.handleEvent(
                { data, lookupDatas },
                null,
                events.priceService_batchAddAfter_parseData
            );

            expect(result).toEqual([
                { id: 1, unit: 'unit1', unit__v: 'baseUnit1' },
                { id: 2, unit: 'existingUnit', unit__v: 'existingBaseUnit' }
            ]);
        });

        it('should return undefined for unknown events', () => {
            const result = handler.handleEvent({}, {}, 'unknown_event');
            expect(result).toBeUndefined();
        });
    });
}); 