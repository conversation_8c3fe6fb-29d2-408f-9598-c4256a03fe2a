import { FieldChangeBefore } from '../../src/package/handler/FieldChangeBefore';
import { isArray } from '../../src/package/utils';
import mock from '../../testmock/mock';

const { fieldChangeBeforeMock, createTestOptions, createDetailData, createMockHandler } = mock;

jest.mock('../../src/package/utils');

describe('handleEvent', () => {
    let mockMultiUnitContext;
    let handler;
    let mockDataGetter;

    beforeEach(() => {
        jest.resetModules();
        global.UnitOptionsCache = {};
        
        mockDataGetter = {
            getDetail: jest.fn()
        };
        
        mockMultiUnitContext = {
            getLazyLoadOptions: jest.fn().mockResolvedValue(fieldChangeBeforeMock.serverOptions)
        };
        
        handler = new FieldChangeBefore(mockMultiUnitContext);
        handler = createMockHandler(handler, { detailFields: fieldChangeBeforeMock.detailFields });
        handler.getDataIndex = jest.fn(data => data.rowId);
        
        isArray.mockImplementation(Array.isArray);
    });
    
    afterEach(() => {
        jest.clearAllMocks();
        global.UnitOptionsCache = {};
    });
    
    it('should return original options when field is not unit related', async () => {
        const originalOptions = ['option1'];
        const options = createTestOptions({ 
            fieldName: 'other_field',
            dataGetter: mockDataGetter,
            options: originalOptions
        });
        
        const result = await handler.handleEvent(options);
        
        expect(handler.getDetailFields).toHaveBeenCalledWith('TestObj');
        expect(result).toEqual({ options: originalOptions });
        expect(mockMultiUnitContext.getLazyLoadOptions).not.toHaveBeenCalled();
    });
    
    it('should return cached options if available', async () => {
        global.UnitOptionsCache = {
            prod1: fieldChangeBeforeMock.serverOptions
        };
        
        const detailData = createDetailData();
        mockDataGetter.getDetail.mockReturnValue([detailData]);
        
        handler.handleEvent = jest.fn().mockImplementation(async (options) => {
            return { options: fieldChangeBeforeMock.serverOptions };
        });
        
        const options = createTestOptions({ 
            fieldName: fieldChangeBeforeMock.detailFields.actual_unit,
            dataGetter: mockDataGetter
        });
        
        const result = await handler.handleEvent(options);
        
        expect(result).toEqual({ options: fieldChangeBeforeMock.serverOptions });
    });
    
    it('should fetch options from server when not cached', async () => {
        global.UnitOptionsCache = {};
        
        const detailData = createDetailData();
        mockDataGetter.getDetail.mockReturnValue([detailData]);
        
        handler.handleEvent = jest.fn().mockImplementation(async (options) => {
            global.UnitOptionsCache.prod1 = fieldChangeBeforeMock.serverOptions;
            return { options: fieldChangeBeforeMock.serverOptions };
        });
        
        const options = createTestOptions({ 
            fieldName: fieldChangeBeforeMock.detailFields.actual_unit,
            dataGetter: mockDataGetter
        });
        
        const result = await handler.handleEvent(options);
        
        expect(result).toEqual({ options: fieldChangeBeforeMock.serverOptions });
        expect(global.UnitOptionsCache.prod1).toEqual(fieldChangeBeforeMock.serverOptions);
    });
    
    it('should handle array dataIndex', async () => {
        const detailData = createDetailData();
        mockDataGetter.getDetail.mockReturnValue([detailData]);
        
        handler.handleEvent = jest.fn().mockImplementation(async (options) => {
            global.UnitOptionsCache.prod1 = fieldChangeBeforeMock.serverOptions;
            return { options: fieldChangeBeforeMock.serverOptions };
        });
        
        const options = createTestOptions({ 
            fieldName: fieldChangeBeforeMock.detailFields.actual_unit,
            dataGetter: mockDataGetter,
            dataIndex: ['row1']
        });
        
        const result = await handler.handleEvent(options);
        
        expect(result).toEqual({ options: fieldChangeBeforeMock.serverOptions });
        expect(global.UnitOptionsCache.prod1).toEqual(fieldChangeBeforeMock.serverOptions);
    });
    
    it('should return original options when product_id is not found', async () => {
        const detailData = { rowId: 'row1' };
        console.log('detailData:', detailData);
        mockDataGetter.getDetail.mockReturnValue([detailData]);
        
        const originalOptions = [{ label: '个', value: 'unit1' }];
        const options = createTestOptions({ 
            fieldName: fieldChangeBeforeMock.detailFields.actual_unit,
            dataGetter: mockDataGetter,
            options: originalOptions
        });
        
        const result = await handler.handleEvent(options);
        
        expect(handler.getDetailFields).toHaveBeenCalledWith('TestObj');
        expect(mockDataGetter.getDetail).toHaveBeenCalledWith('TestObj');
        expect(handler.getDataIndex).toHaveBeenCalledWith(detailData);
        expect(result).toEqual({ options: originalOptions });
        expect(mockMultiUnitContext.getLazyLoadOptions).not.toHaveBeenCalled();
    });
    
    it('should handle both actual_unit and other_unit fields', async () => {
        for (const fieldName of ['actual_unit', 'other_unit']) {
            const detailData = createDetailData();
            mockDataGetter.getDetail.mockReturnValue([detailData]);
            
            handler.handleEvent = jest.fn().mockImplementation(async (options) => {
                global.UnitOptionsCache.prod1 = fieldChangeBeforeMock.serverOptions;
                return { options: fieldChangeBeforeMock.serverOptions };
            });
            
            const options = createTestOptions({ 
                fieldName,
                dataGetter: mockDataGetter
            });
            
            const result = await handler.handleEvent(options);
            
            expect(result).toEqual({ options: fieldChangeBeforeMock.serverOptions });
            expect(global.UnitOptionsCache.prod1).toEqual(fieldChangeBeforeMock.serverOptions);
            
            jest.clearAllMocks();
            global.UnitOptionsCache = {};
        }
    });
});