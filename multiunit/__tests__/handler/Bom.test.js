import { Bom } from '../../src/package/handler/Bom';
import { BatchTodoMultiunit } from '../../src/package/handler/BatchTodoMultiunit';
import { events } from '../../src/package/events';

// Mock BatchTodoMultiunit
jest.mock('../../src/package/handler/BatchTodoMultiunit');

describe('Bom', () => {
    let bom;
    let mockMultiUnitContext;
    let mockBatchTodoMultiunit;

    beforeEach(() => {
        // 重置所有的mock
        jest.clearAllMocks();

        // 创建mock的MultiUnitContext
        mockMultiUnitContext = {
            getDetailFields: jest.fn().mockReturnValue({
                price: 'price__c',
                quantity: 'quantity__c',
                price_book_price: 'price_book_price__c'
            })
        };

        // 创建mock的BatchTodoMultiunit实例
        mockBatchTodoMultiunit = {
            todoMultiunit: jest.fn()
        };
        BatchTodoMultiunit.mockImplementation(() => mockBatchTodoMultiunit);

        bom = new Bom(mockMultiUnitContext);
    });

    describe('handleEvent', () => {
        test('当事件为bom_md_edit_after时应该调用handleMultiUnitParse', async () => {
            const options = { test: 'test' };
            const mockHandleMultiUnitParse = jest.spyOn(bom, 'handleMultiUnitParse')
                .mockResolvedValue(undefined);
            
            await bom.handleEvent(options, {}, events.bom_md_edit_after);
            
            expect(mockHandleMultiUnitParse).toHaveBeenCalledWith(options);
        });

        test('当事件为bom_md_batchAdd_end时应该调用handleMultiUnitParse', async () => {
            const options = { test: 'test' };
            const mockHandleMultiUnitParse = jest.spyOn(bom, 'handleMultiUnitParse')
                .mockResolvedValue(undefined);
            
            await bom.handleEvent(options, {}, events.bom_md_batchAdd_end);
            
            expect(mockHandleMultiUnitParse).toHaveBeenCalledWith(options);
        });

        test('当事件不匹配时不应该调用handleMultiUnitParse', async () => {
            const options = { test: 'test' };
            const mockHandleMultiUnitParse = jest.spyOn(bom, 'handleMultiUnitParse');
            
            await bom.handleEvent(options, {}, 'other_event');
            
            expect(mockHandleMultiUnitParse).not.toHaveBeenCalled();
        });
    });

    describe('handleMultiUnitParse', () => {
        test('应该正确处理多单位数据并更新明细', async () => {
            const list = [
                { rowId: 'row1', data: 'data1' },
                { rowId: 'row2', data: 'data2' }
            ];
            const mockDataUpdater = {
                updateDetail: jest.fn()
            };
            const options = {
                list,
                objectApiName: 'QuoteObj',
                detailObjectApiName: 'QuoteLinesObj',
                param: {
                    dataUpdater: mockDataUpdater
                }
            };

            const todoMultiunitResult = [
                { rowId: 'row1', updatedData: 'data1' },
                { rowId: 'row2', updatedData: 'data2' }
            ];
            mockBatchTodoMultiunit.todoMultiunit.mockResolvedValue(todoMultiunitResult);

            await bom.handleMultiUnitParse(options);

            // 验证调用todoMultiunit的参数
            expect(mockBatchTodoMultiunit.todoMultiunit).toHaveBeenCalledWith({
                list,
                objectApiName: 'QuoteObj',
                detailObjectApiName: 'QuoteLinesObj',
                ignoreFields: ['price__c', 'quantity__c', 'price_book_price__c'],
                pluginParam: options.param,
                noCalculate: false,
                setFieldReadOnly: false,
                setNonMultipleUnitField: true
            });

            // 验证更新明细的调用
            expect(mockDataUpdater.updateDetail).toHaveBeenCalledTimes(2);
            expect(mockDataUpdater.updateDetail).toHaveBeenCalledWith('QuoteLinesObj', 'row1', todoMultiunitResult[0]);
            expect(mockDataUpdater.updateDetail).toHaveBeenCalledWith('QuoteLinesObj', 'row2', todoMultiunitResult[1]);
        });

        test('当list为空时应该正常处理', async () => {
            const options = {
                objectApiName: 'QuoteObj',
                detailObjectApiName: 'QuoteLinesObj',
                param: {
                    dataUpdater: {
                        updateDetail: jest.fn()
                    }
                }
            };

            mockBatchTodoMultiunit.todoMultiunit.mockResolvedValue([]);

            await bom.handleMultiUnitParse(options);

            expect(mockBatchTodoMultiunit.todoMultiunit).toHaveBeenCalledWith(expect.objectContaining({
                list: [],
                objectApiName: 'QuoteObj',
                detailObjectApiName: 'QuoteLinesObj'
            }));
            expect(options.param.dataUpdater.updateDetail).not.toHaveBeenCalled();
        });
    });
}); 