import { FormRenderAfter } from '../../src/package/handler/FormRenderAfter';
import { isMultipleUnit } from '../../src/package/utils';
import { emitEvent } from '../../src/package/events';

// Mock utils
jest.mock('../../src/package/utils', () => ({
    isMultipleUnit: jest.fn(),
    multiUnitPlacesDecimal: 'places_decimal__c',
    rowId: 'rowId',
    uuid: jest.fn().mockReturnValue('mock-uuid')
}));

describe('FormRenderAfter', () => {
    let formRenderAfter;
    let mockMultiUnitContext;
    let mockDataGetter;
    let mockDataUpdater;

    beforeEach(() => {
        // 重置所有的mock
        jest.clearAllMocks();

        // 创建mock的MultiUnitContext
        mockMultiUnitContext = {
            getFirstDetailObjApiName: jest.fn().mockReturnValue('QuoteLinesObj'),
            getDetailFields: jest.fn().mockReturnValue({
                actual_unit: 'actual_unit__c',
                other_unit: 'other_unit__c',
                is_multiple_unit: 'is_multiple_unit__c',
                base_unit_count: 'base_unit_count__c',
                conversion_ratio: 'conversion_ratio__c',
                stat_unit_count: 'stat_unit_count__c',
                price_book_product_id: 'price_book_product_id__c',
                product_id: 'product_id__c',
                quantity: 'quantity__c'
            }),
            runPlugin: jest.fn(),
            calcPriceByUnit: jest.fn()
        };

        // 创建mock的dataGetter和dataUpdater
        mockDataGetter = {
            getDetail: jest.fn(),
            getDescribe: jest.fn()
        };

        mockDataUpdater = {
            setReadOnly: jest.fn(),
            updateDetail: jest.fn()
        };

        formRenderAfter = new FormRenderAfter(mockMultiUnitContext);
    });

    describe('handleEvent', () => {
        test('应该正确设置字段的只读状态', async () => {
            const detailDataList = [
                { rowId: 'row1' },
                { rowId: 'row2' }
            ];

            mockDataGetter.getDetail.mockReturnValue(detailDataList);
            isMultipleUnit.mockReturnValueOnce(true).mockReturnValueOnce(false);

            await formRenderAfter.handleEvent({
                dataGetter: mockDataGetter,
                dataUpdater: mockDataUpdater
            });

            expect(mockDataUpdater.setReadOnly).toHaveBeenCalledTimes(4);
            expect(mockDataUpdater.setReadOnly).toHaveBeenCalledWith({
                objApiName: 'QuoteLinesObj',
                dataIndex: 'row1',
                fieldName: 'actual_unit__c',
                status: false
            });
            expect(mockDataUpdater.setReadOnly).toHaveBeenCalledWith({
                objApiName: 'QuoteLinesObj',
                dataIndex: 'row1',
                fieldName: 'other_unit__c',
                status: false
            });
        });

        test('当映射到销售订单时应该处理多单位取价', async () => {
            const detailDataList = [
                {
                    rowId: 'row1',
                    product_id__c: 'prod1',
                    price_book_product_id__c: 'pbp1',
                    actual_unit__c: 'unit1',
                    other_unit__c: 'unit2',
                    quantity__c: 10,
                    is_multiple_unit__c: true
                }
            ];

            mockDataGetter.getDetail.mockReturnValue(detailDataList);
            mockDataGetter.getDescribe.mockReturnValue({
                api_name: 'SalesOrderProductObj',
                fields: {
                    actual_unit__c: {
                        options: [
                            { value: 'unit1', label: '个' }
                        ]
                    }
                }
            });

            const calcResult = [{
                rowId: 'row1',
                base_unit_count: 20,
                conversion_ratio: 2,
                stat_unit_count: '20个',
                places_decimal: 2,
                needChangePriceBookProduct: true,
                changePriceBookProduct: {
                    id: 'pbp2',
                    name: 'Product 2'
                }
            }];

            mockMultiUnitContext.calcPriceByUnit.mockResolvedValue(calcResult);
            isMultipleUnit.mockReturnValue(true);
            mockMultiUnitContext.getFirstDetailObjApiName.mockReturnValue('SalesOrderProductObj');

            // Mock getRowId method
            jest.spyOn(formRenderAfter, 'getRowId').mockImplementation(data => data.rowId);

            const options = {
                sourceAction: 'Convert',
                dataGetter: mockDataGetter,
                dataUpdater: mockDataUpdater,
                masterObjApiName: 'SalesOrderObj',
                objApiName: 'SalesOrderProductObj'
            };

            await formRenderAfter.handleEvent(options);

            expect(mockMultiUnitContext.runPlugin).toHaveBeenCalledWith(
                emitEvent.multiUnit_form_calcPriceParam,
                expect.objectContaining({
                    ...options,
                    param: expect.objectContaining({
                        params: expect.any(Array),
                        masterObjectApiName: 'SalesOrderObj',
                        detailObjectApiName: 'SalesOrderProductObj'
                    }),
                    detailDataList
                })
            );

            expect(mockDataUpdater.updateDetail).toHaveBeenCalledWith(
                'SalesOrderProductObj',
                'row1',
                {
                    base_unit_count__c: 20,
                    conversion_ratio__c: 2,
                    stat_unit_count__c: '20个',
                    places_decimal__c: 2,
                    price_book_product_id__c: 'pbp2',
                    'price_book_product_id__c__r': 'Product 2'
                }
            );
        });

        test('当是映射表单且开启灰度时应该特殊处理映射的单位', async () => {
            const detailDataList = [
                {
                    rowId: 'row1',
                    unit__v: 'unit1'
                }
            ];

            mockDataGetter.getDetail.mockReturnValue(detailDataList);
            mockMultiUnitContext.getFirstDetailObjApiName.mockReturnValue('SalesOrderProductObj');
            global.CRM = {
                util: {
                    isGrayScale: jest.fn().mockReturnValue(true)
                }
            };

            await formRenderAfter.handleEvent({
                formType: 'mapping',
                dataGetter: mockDataGetter,
                dataUpdater: mockDataUpdater,
                objApiName: 'SalesOrderProductObj'
            });

            expect(mockDataUpdater.updateDetail).toHaveBeenCalledWith(
                'SalesOrderProductObj',
                'row1',
                {
                    actual_unit__c: 'unit1'
                }
            );
        });
    });

    describe('buildMultiUnitParams', () => {
        test('应该正确构建多单位参数', () => {
            const detailDataList = [
                {
                    product_id__c: 'prod1',
                    price_book_product_id__c: 'pbp1',
                    actual_unit__c: 'unit1',
                    other_unit__c: 'unit2',
                    quantity__c: 10,
                    is_multiple_unit__c: true
                },
                {
                    product_id__c: 'prod2',
                    actual_unit__c: null,
                    is_multiple_unit__c: true
                },
                {
                    product_id__c: 'prod3',
                    actual_unit__c: 'unit3',
                    is_multiple_unit__c: false
                }
            ];

            isMultipleUnit
                .mockReturnValueOnce(true)
                .mockReturnValueOnce(true)
                .mockReturnValueOnce(false);

            const result = formRenderAfter.buildMultiUnitParams(detailDataList, 'QuoteLinesObj');

            expect(result).toHaveLength(1);
            expect(result[0]).toEqual({
                productId: 'prod1',
                priceBookProductId: 'pbp1',
                unitId: 'unit1',
                otherUnitId: 'unit2',
                count: 10,
                rowId: 'mock-uuid'
            });
        });
    });

    describe('getMultiUnitResult', () => {
        beforeEach(() => {
            // Mock getRowId method
            jest.spyOn(formRenderAfter, 'getRowId').mockImplementation(data => data.rowId || 'mock-uuid');
        });

        test('当没有单位时应该返回undefined', () => {
            const detailData = {
                quantity__c: 10
            };

            const result = formRenderAfter.getMultiUnitResult(detailData, [], {});
            expect(result).toBeUndefined();
        });

        test('当找不到计算结果时应该返回默认值', () => {
            const detailData = {
                rowId: 'row1',
                actual_unit__c: 'unit1',
                quantity__c: 10
            };

            const objectDescribe = {
                fields: {
                    actual_unit__c: {
                        options: [
                            { value: 'unit1', label: '个' }
                        ]
                    }
                }
            };

            const result = formRenderAfter.getMultiUnitResult(detailData, [], objectDescribe);

            expect(result).toEqual({
                conversion_ratio: 1,
                base_unit_count: 10,
                stat_unit_count: '10个'
            });
        });

        test('应该返回匹配的计算结果', () => {
            const detailData = {
                rowId: 'row1',
                actual_unit__c: 'unit1',
                quantity__c: 10
            };

            const calcResults = [
                {
                    rowId: 'row1',
                    conversion_ratio: 2,
                    base_unit_count: 20,
                    stat_unit_count: '20个'
                }
            ];

            const result = formRenderAfter.getMultiUnitResult(detailData, calcResults, {});

            expect(result).toBe(calcResults[0]);
        });
    });
}); 