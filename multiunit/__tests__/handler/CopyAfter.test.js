import {CopyAfter} from '../../src/package/handler/CopyAfter';
import {isMultipleUnit} from '../../src/package/utils';

jest.mock('../../src/package/utils', () => ({
    isMultipleUnit: jest.fn(),
    multiUnitPlacesDecimal: 'multiUnitPlacesDecimal'
}));

describe('CopyAfter', () => {
    let mockMultiUnitContext;
    let mockDataGetter;
    let mockDataUpdater;
    let handler;
    let detailFields;

    beforeEach(() => {
        detailFields = {
            product_id: 'product_id',
            price_book_product_id: 'price_book_product_id',
            price: 'price',
            quantity: 'quantity',
            actual_unit: 'actual_unit',
            other_unit: 'other_unit',
            base_unit_count: 'base_unit_count',
            conversion_ratio: 'conversion_ratio',
            stat_unit_count: 'stat_unit_count',
            other_unit_quantity: 'other_unit_quantity',
            price_book_id: 'price_book_id',
            price_book_price: 'price_book_price',
            is_multiple_unit: 'is_multiple_unit'
        };

        mockMultiUnitContext = {
            getDetailFields: () => detailFields,
            getFirstDetailObjApiName: jest.fn().mockReturnValue('detail_obj')
        };

        mockDataGetter = {
            getData: jest.fn()
        };

        mockDataUpdater = {
            setReadOnly: jest.fn()
        };

        handler = new CopyAfter(mockMultiUnitContext);
        isMultipleUnit.mockImplementation((data, field) => data[field] === true);
    });

    describe('handleEvent', () => {
        it('should handle multiple unit product correctly', () => {
            const options = {
                dataGetter: mockDataGetter,
                dataUpdater: mockDataUpdater,
                dataIndex: ['row1']
            };

            mockDataGetter.getData.mockReturnValue({
                is_multiple_unit: true
            });

            handler.handleEvent(options);

            expect(mockDataUpdater.setReadOnly.mock.calls).toEqual([
                [{
                    objApiName: 'detail_obj',
                    dataIndex: 'row1',
                    fieldName: 'actual_unit',
                    status: false
                }],
                [{
                    objApiName: 'detail_obj',
                    dataIndex: 'row1',
                    fieldName: 'other_unit',
                    status: false
                }]
            ]);
        });

        it('should handle non-multiple unit product correctly', () => {
            const options = {
                dataGetter: mockDataGetter,
                dataUpdater: mockDataUpdater,
                dataIndex: ['row1']
            };

            mockDataGetter.getData.mockReturnValue({
                is_multiple_unit: false
            });

            handler.handleEvent(options);

            expect(mockDataUpdater.setReadOnly.mock.calls).toEqual([
                [{
                    objApiName: 'detail_obj',
                    dataIndex: 'row1',
                    fieldName: 'actual_unit',
                    status: true
                }],
                [{
                    objApiName: 'detail_obj',
                    dataIndex: 'row1',
                    fieldName: 'other_unit',
                    status: true
                }]
            ]);
        });

        it('should handle multiple data indexes', () => {
            const options = {
                dataGetter: mockDataGetter,
                dataUpdater: mockDataUpdater,
                dataIndex: ['row1', 'row2']
            };

            mockDataGetter.getData
                .mockReturnValueOnce({ is_multiple_unit: true })
                .mockReturnValueOnce({ is_multiple_unit: false });

            handler.handleEvent(options);

            expect(mockDataUpdater.setReadOnly.mock.calls).toEqual([
                [{
                    objApiName: 'detail_obj',
                    dataIndex: 'row1',
                    fieldName: 'actual_unit',
                    status: false
                }],
                [{
                    objApiName: 'detail_obj',
                    dataIndex: 'row1',
                    fieldName: 'other_unit',
                    status: false
                }],
                [{
                    objApiName: 'detail_obj',
                    dataIndex: 'row2',
                    fieldName: 'actual_unit',
                    status: true
                }],
                [{
                    objApiName: 'detail_obj',
                    dataIndex: 'row2',
                    fieldName: 'other_unit',
                    status: true
                }]
            ]);
        });
    });
}); 