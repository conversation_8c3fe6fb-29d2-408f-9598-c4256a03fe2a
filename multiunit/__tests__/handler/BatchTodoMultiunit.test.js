import { BatchTodoMultiunit } from '../../src/package/handler/BatchTodoMultiunit';
import { isMultipleUnit } from '../../src/package/utils';

// Mock utils
jest.mock('../../src/package/utils', () => ({
    isMultipleUnit: jest.fn()
}));

describe('BatchTodoMultiunit', () => {
    let mockMultiUnitContext;
    let handler;
    let detailFields;
    let mockPluginParam;

    beforeEach(() => {
        detailFields = {
            product_id: 'product_id',
            price_book_product_id: 'price_book_product_id',
            price: 'price',
            quantity: 'quantity',
            actual_unit: 'actual_unit',
            other_unit: 'other_unit',
            base_unit_count: 'base_unit_count',
            conversion_ratio: 'conversion_ratio',
            stat_unit_count: 'stat_unit_count',
            other_unit_quantity: 'other_unit_quantity',
            is_multiple_unit: 'is_multiple_unit'
        };

        mockMultiUnitContext = {
            getDetailFields: () => detailFields,
            calcPriceByUnit: jest.fn()
        };

        mockPluginParam = {
            dataGetter: {
                getDescribe: jest.fn()
            },
            triggerCalAndUIEvent: jest.fn(),
            objectApiName: 'TestObj'
        };

        handler = new BatchTodoMultiunit(mockMultiUnitContext);
        isMultipleUnit.mockImplementation((data, field) => data[field] === true);
    });

    describe('handleEvent', () => {
        it('should call todoMultiunit with provided options', async () => {
            const options = {
                list: [{id: 1}],
                objectApiName: 'TestObj'
            };

            const spy = jest.spyOn(handler, 'todoMultiunit');
            await handler.handleEvent(options);
            expect(spy).toHaveBeenCalledWith(options);
        });
    });

    describe('todoMultiunit', () => {
        it('should handle non-multiple unit products when setNonMultipleUnitField is true', async () => {
            const list = [{
                rowId: 'row1',
                is_multiple_unit: false,
                actual_unit: 'unit1',
                quantity: 5
            }];

            mockPluginParam.dataGetter.getDescribe.mockReturnValue({
                fields: {
                    actual_unit: {
                        options: [{
                            value: 'unit1',
                            label: '个'
                        }]
                    }
                }
            });

            const result = await handler.todoMultiunit({
                list,
                objectApiName: 'TestObj',
                detailObjectApiName: 'TestDetailObj',
                pluginParam: mockPluginParam,
                setNonMultipleUnitField: true
            });

            expect(result[0]).toEqual(expect.objectContaining({
                base_unit_count: 5,
                conversion_ratio: 1,
                stat_unit_count: '5个',
                other_unit: null,
                other_unit_quantity: null
            }));
        });

        it('should process multiple unit products and call calcPriceByUnit', async () => {
            const list = [{
                rowId: 'row1',
                is_multiple_unit: true,
                product_id: 'prod1',
                price_book_product_id: 'pbp1',
                actual_unit: 'unit1',
                other_unit: 'unit2',
                quantity: 5
            }];

            mockMultiUnitContext.calcPriceByUnit.mockResolvedValue([{
                productId: 'prod1',
                unitId: 'unit1',
                rowId: 'row1',
                price: 100,
                base_unit_count: 5,
                conversion_ratio: 1,
                stat_unit_count: '5个',
                other_unit: 'unit2',
                other_unit_quantity: 2
            }]);

            const result = await handler.todoMultiunit({
                list,
                objectApiName: 'TestObj',
                detailObjectApiName: 'TestDetailObj',
                pluginParam: mockPluginParam
            });

            expect(mockMultiUnitContext.calcPriceByUnit).toHaveBeenCalledWith({
                params: [{
                    productId: 'prod1',
                    priceBookProductId: 'pbp1',
                    unitId: 'unit1',
                    otherUnitId: 'unit2',
                    count: 5,
                    rowId: 'row1'
                }],
                detailObjectApiName: 'TestDetailObj',
                masterObjectApiName: 'TestObj'
            });

            expect(result[0]).toEqual(expect.objectContaining({
                price: 100,
                base_unit_count: 5,
                conversion_ratio: 1,
                stat_unit_count: '5个',
                other_unit: 'unit2',
                other_unit_quantity: 2
            }));
        });

        it('should ignore specified fields in the result', async () => {
            const list = [{
                rowId: 'row1',
                is_multiple_unit: true,
                product_id: 'prod1',
                actual_unit: 'unit1',
                quantity: 5
            }];

            mockMultiUnitContext.calcPriceByUnit.mockResolvedValue([{
                productId: 'prod1',
                unitId: 'unit1',
                rowId: 'row1',
                price: 100,
                base_unit_count: 5,
                conversion_ratio: 1
            }]);

            const result = await handler.todoMultiunit({
                list,
                objectApiName: 'TestObj',
                detailObjectApiName: 'TestDetailObj',
                pluginParam: mockPluginParam,
                ignoreFields: ['price']
            });

            expect(result[0]).not.toHaveProperty('price');
            expect(result[0]).toHaveProperty('base_unit_count', 5);
            expect(result[0]).toHaveProperty('conversion_ratio', 1);
        });

        it('should trigger calculation when noCalculate is false', async () => {
            const list = [{
                rowId: 'row1',
                is_multiple_unit: true,
                product_id: 'prod1',
                actual_unit: 'unit1',
                quantity: 5
            }];

            mockMultiUnitContext.calcPriceByUnit.mockResolvedValue([{
                productId: 'prod1',
                unitId: 'unit1',
                rowId: 'row1',
                price: 100,
                base_unit_count: 5,
                conversion_ratio: 1
            }]);

            await handler.todoMultiunit({
                list,
                objectApiName: 'TestObj',
                detailObjectApiName: 'TestDetailObj',
                pluginParam: mockPluginParam,
                noCalculate: false
            });

            expect(mockPluginParam.triggerCalAndUIEvent).toHaveBeenCalledWith({
                operateType: 'mdEdit',
                dataIndex: ['row1'],
                objApiName: 'TestObj',
                changeFields: ['price', 'base_unit_count', 'conversion_ratio']
            });
        });

        it('should not trigger calculation when noCalculate is true', async () => {
            const list = [{
                rowId: 'row1',
                is_multiple_unit: true,
                product_id: 'prod1',
                actual_unit: 'unit1',
                quantity: 5
            }];

            mockMultiUnitContext.calcPriceByUnit.mockResolvedValue([{
                productId: 'prod1',
                unitId: 'unit1',
                rowId: 'row1',
                price: 100,
                base_unit_count: 5,
                conversion_ratio: 1
            }]);

            await handler.todoMultiunit({
                list,
                objectApiName: 'TestObj',
                detailObjectApiName: 'TestDetailObj',
                pluginParam: mockPluginParam,
                noCalculate: true
            });

            expect(mockPluginParam.triggerCalAndUIEvent).not.toHaveBeenCalled();
        });
    }); 
}); 