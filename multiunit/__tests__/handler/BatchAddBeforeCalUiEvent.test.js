import { BatchAddBeforeCalUiEvent } from '../../src/package/handler/BatchAddBeforeCalUiEvent';

describe('BatchAddBeforeCalUiEvent', () => {
    let batchAddBeforeCalUiEvent;
    let mockMultiUnitContext;

    beforeEach(() => {
        // 创建mock的MultiUnitContext
        mockMultiUnitContext = {
            getDetailFields: jest.fn().mockReturnValue({
                quantity: 'quantity__c',
                price: 'price__c',
                price_book_price: 'price_book_price__c'
            }),
            isOpenPriceBook: jest.fn()
        };

        batchAddBeforeCalUiEvent = new BatchAddBeforeCalUiEvent(mockMultiUnitContext);
    });

    describe('handleEvent', () => {
        test('当没有预设的noCalFields时应该返回基本字段', () => {
            const options = {
                objApiName: 'QuoteLinesObj'
            };
            const pluginExecResult = {};

            mockMultiUnitContext.isOpenPriceBook.mockReturnValue(false);

            const result = batchAddBeforeCalUiEvent.handleEvent(options, pluginExecResult);

            expect(mockMultiUnitContext.getDetailFields).toHaveBeenCalledWith('QuoteLinesObj');
            expect(result).toEqual({
                noCalFields: ['quantity__c', 'price__c']
            });
        });

        test('当有预设的noCalFields时应该合并字段', () => {
            const options = {
                objApiName: 'QuoteLinesObj'
            };
            const pluginExecResult = {
                preData: {
                    noCalFields: ['field1', 'field2']
                }
            };

            mockMultiUnitContext.isOpenPriceBook.mockReturnValue(false);

            const result = batchAddBeforeCalUiEvent.handleEvent(options, pluginExecResult);

            expect(result).toEqual({
                noCalFields: ['field1', 'field2', 'quantity__c', 'price__c']
            });
        });

        test('当价目表开启时应该包含price_book_price字段', () => {
            const options = {
                objApiName: 'QuoteLinesObj'
            };
            const pluginExecResult = {};

            mockMultiUnitContext.isOpenPriceBook.mockReturnValue(true);

            const result = batchAddBeforeCalUiEvent.handleEvent(options, pluginExecResult);

            expect(result).toEqual({
                noCalFields: ['quantity__c', 'price__c', 'price_book_price__c']
            });
        });

        test('当pluginExecResult为null时应该正常工作', () => {
            const options = {
                objApiName: 'QuoteLinesObj'
            };

            mockMultiUnitContext.isOpenPriceBook.mockReturnValue(false);

            const result = batchAddBeforeCalUiEvent.handleEvent(options, null);

            expect(result).toEqual({
                noCalFields: ['quantity__c', 'price__c']
            });
        });
    });
}); 