import { BatchAddAfter } from '../../src/package/handler/BatchAddAfter';
import { MultiUnitContext } from '../../src/package/MultiUnitContext';
import { getRealPriceResult } from '../../src/package/utils';
import { isMultipleUnit, isEmpty } from '../../src/package/utils';
import { emitEvent } from '../../src/package/events';
import mock from '../../testmock/mock';

const { batchAddAfterMock, createTestOptions, createDetailData, createMockHandler } = mock;

jest.mock('../../src/package/MultiUnitContext');
jest.mock('../../src/package/utils', () => ({
    isMultipleUnit: jest.fn(),
    isEmpty: jest.fn(),
    getRealPriceResult: 'getRealPriceResult',
    uuid: jest.fn().mockReturnValue('mock-uuid'),
    multiUnitPlacesDecimal: 'multiUnitPlacesDecimal'
}));

const mockMultiUnitContext = {
    isOpenPriceBook: jest.fn().mockReturnValue(true),
    getLazyLoadOptions: jest.fn().mockResolvedValue([
        { value: 'unit1', label: '个' },
        { value: 'unit2', label: '箱' }
    ]),
    calcPriceByUnit: jest.fn().mockResolvedValue([
        {
            rowId: 'test_row_id',
            price: 100,
            unitId: 'unit1',
            conversion_ratio: 1,
            base_unit_count: 10,
            stat_unit_count: '10个',
            priceBookPrice: 90
        }
    ]),
    runPlugin: jest.fn(),
    batchCalculate: jest.fn().mockResolvedValue({
        SalesOrderDetail: {
            'test_row_id': { quantity: 5 }
        }
    }),
    alert: jest.fn(),
    getDetailFields: jest.fn().mockReturnValue({
        actual_unit: 'actual_unit',
        other_unit: 'other_unit'
    })
};

describe('BatchAddAfter', () => {
    let batchAddAfter;
    let mockContext;
    let mockDataGetter;
    let mockDataUpdater;
    
    beforeEach(() => {
        mockContext = {
            ...mockMultiUnitContext,
            getDetailFields: jest.fn().mockReturnValue({
                actual_unit: 'actual_unit',
                other_unit: 'other_unit',
                quantity: 'quantity',
                price: 'price',
                base_unit_count: 'base_unit_count',
                conversion_ratio: 'conversion_ratio',
                stat_unit_count: 'stat_unit_count',
                price_book_price: 'price_book_price',
                places_decimal: 'places_decimal',
                other_unit_quantity: 'other_unit_quantity'
            })
        };
        batchAddAfter = new BatchAddAfter(mockContext);
        batchAddAfter = createMockHandler(batchAddAfter, { detailFields: batchAddAfterMock.detailFields });

        // Mock dataGetter and dataUpdater
        mockDataGetter = {
            getDescribe: jest.fn().mockReturnValue({
                fields: {
                    actual_unit: {
                        options: [{
                            value: 'unit001',
                            label: '个'
                        }]
                    }
                }
            })
        };

        mockDataUpdater = {
            updateDetail: jest.fn(),
            setReadOnly: jest.fn(),
            setRequired: jest.fn()
        };

        isMultipleUnit.mockImplementation((data, field) => data[field] === true);
        isEmpty.mockImplementation(val => val === undefined || val === null || val === '');
    });

    describe('handleEvent', () => {
        it('should call handleMultiUnitCalc with options', async () => {
            const options = { dataGetter: {}, dataUpdater: {} };
            const spy = jest.spyOn(batchAddAfter, 'handleMultiUnitCalc');
            
            await batchAddAfter.handleEvent(options);
            
            expect(spy).toHaveBeenCalledWith(options);
        });

        it('should not process when target_api_name is not ProductObj or PriceBookProductObj', async () => {
            const options = {
                lookupField: {
                    target_api_name: 'OtherObj'
                }
            };
            await batchAddAfter.handleEvent(options);
            expect(mockDataUpdater.updateDetail).not.toHaveBeenCalled();
        });

        it('should process product lookup data correctly', async () => {
            // 重置模拟
            mockDataUpdater = {
                setReadOnly: jest.fn(),
                setRequired: jest.fn(),
                updateDetail: jest.fn()
            };
            
            const options = {
                dataUpdater: mockDataUpdater,
                objApiName: 'detail_obj',
                lookupDatas: [{
                    object_describe_api_name: 'ProductObj',
                    is_multiple_unit: true
                }],
                newDataIndexs: ['row1']
            };
            
            const batchAddAfter = new BatchAddAfter(mockMultiUnitContext);
            
            // Mock getDetailFields 方法
            batchAddAfter.getDetailFields = jest.fn().mockReturnValue({
                actual_unit: 'actual_unit',
                other_unit: 'other_unit'
            });
            
            // 模拟handleMultiUnitCalc方法，使其调用setFieldReadonly
            batchAddAfter.handleMultiUnitCalc = jest.fn().mockImplementation(async (opt) => {
                batchAddAfter.setFieldReadonly(opt);
                return {};
            });
            
            await batchAddAfter.handleEvent(options);
            
            expect(mockDataUpdater.setReadOnly).toHaveBeenCalledWith({
                objApiName: 'detail_obj',
                dataIndex: 'row1',
                fieldName: ['actual_unit', 'other_unit'],
                status: false
            });
            
            expect(mockDataUpdater.setRequired).toHaveBeenCalledWith({
                objApiName: 'detail_obj',
                dataIndex: 'row1',
                fieldName: ['actual_unit'],
                status: true
            });
        });
    });

    describe('handleMultiUnitCalc', () => {
        it('should handle product lookup data correctly', async () => {
            const mockRowId = 'test_row_id';
            const mockFields = {
                other_unit: { type: 'lookup' },
                quantity: { type: 'number', default_is_expression: true },
                product_id: { type: 'lookup' },
                price: { type: 'number' },
                price_book_price: { type: 'number' },
                price_book_product_id: { type: 'lookup' },
                price_book_discount: { type: 'number' }
            };
            
            const options = {
                dataGetter: {
                    getDescribe: jest.fn().mockReturnValue({
                        fields: mockFields,
                        api_name: 'SalesOrderDetail'
                    }),
                    getMasterData: jest.fn().mockReturnValue({ id: 'master1' }),
                    getDetail: jest.fn().mockReturnValue([
                        { id: 'detail1', rowId: mockRowId }
                    ])
                },
                dataUpdater: {
                    updateDetail: jest.fn(),
                    setReadOnly: jest.fn(),
                    setRequired: jest.fn()
                },
                lookupField: { target_api_name: 'ProductObj' },
                lookupDatas: [{
                    object_describe_api_name: 'ProductObj',
                    _id: 'prod1',
                    unit: 'unit1',
                    unit__v: 'unit1',
                    is_multiple_unit: true,
                    price: 100,
                    _selected_num: undefined
                }],
                newDataIndexs: [mockRowId],
                objApiName: 'SalesOrderDetail',
                masterObjApiName: 'SalesOrder',
                addDatas: [{ rowId: mockRowId, product_id: 'prod1' }]
            };

            await batchAddAfter.handleMultiUnitCalc(options);

            expect(mockContext.calcPriceByUnit).toHaveBeenCalled();
            expect(mockContext.batchCalculate).toHaveBeenCalled();
            expect(options.dataUpdater.updateDetail).toHaveBeenCalledWith(
                'SalesOrderDetail',
                mockRowId,
                expect.any(Object),
                false
            );
        });

        it('should handle batchCalculate error', async () => {
            const mockRowId = 'test_row_id';
            const mockFields = {
                other_unit: { type: 'lookup' },
                quantity: { type: 'number', default_is_expression: true, default_value: 1 },
                product_id: { type: 'lookup' }
            };
            
            const options = {
                dataGetter: {
                    getDescribe: jest.fn().mockReturnValue({
                        fields: mockFields,
                        api_name: 'SalesOrderDetail'
                    }),
                    getMasterData: jest.fn().mockReturnValue({ id: 'master1' }),
                    getDetail: jest.fn().mockReturnValue([
                        { id: 'detail1', rowId: mockRowId }
                    ])
                },
                dataUpdater: {
                    updateDetail: jest.fn()
                },
                lookupField: { target_api_name: 'ProductObj' },
                lookupDatas: [{
                    object_describe_api_name: 'ProductObj',
                    _id: 'prod1',
                    unit: 'unit1',
                    is_multiple_unit: true,
                    _selected_num: undefined
                }],
                newDataIndexs: [mockRowId],
                objApiName: 'SalesOrderDetail',
                masterObjApiName: 'SalesOrder',
                addDatas: [{ rowId: mockRowId, product_id: 'prod1' }]
            };

            const error = new Error('Calculation failed');
            mockContext.batchCalculate.mockRejectedValue(error);

            await batchAddAfter.handleMultiUnitCalc(options);

            expect(mockContext.alert).toHaveBeenCalledWith(error);
            expect(options.lookupDatas[0]._selected_num).toBe(1);
        });

        it('should handle default quantity value when not expression', async () => {
            const mockRowId = 'test_row_id';
            const mockFields = {
                other_unit: { type: 'lookup' },
                quantity: { type: 'number', default_is_expression: false, default_value: 2 },
                product_id: { type: 'lookup' }
            };
            
            const options = {
                dataGetter: {
                    getDescribe: jest.fn().mockReturnValue({
                        fields: mockFields,
                        api_name: 'SalesOrderDetail'
                    }),
                    getMasterData: jest.fn().mockReturnValue({ id: 'master1' }),
                    getDetail: jest.fn().mockReturnValue([])
                },
                dataUpdater: {
                    updateDetail: jest.fn()
                },
                lookupField: { target_api_name: 'ProductObj' },
                lookupDatas: [{
                    object_describe_api_name: 'ProductObj',
                    _id: 'prod1',
                    unit: 'unit1',
                    is_multiple_unit: true,
                    _selected_num: undefined
                }],
                newDataIndexs: [mockRowId],
                objApiName: 'SalesOrderDetail',
                masterObjApiName: 'SalesOrder',
                addDatas: [{ rowId: mockRowId, product_id: 'prod1' }]
            };

            await batchAddAfter.handleMultiUnitCalc(options);

            expect(options.lookupDatas[0]._selected_num).toBe(2);
        });

        it('should update quantity when _selected_num is present', async () => {
            const mockRowId = 'test_row_id';
            const mockFields = {
                other_unit: { type: 'lookup' },
                quantity: { type: 'number' },
                product_id: { type: 'lookup' }
            };
            
            const options = {
                dataGetter: {
                    getDescribe: jest.fn().mockReturnValue({
                        fields: mockFields,
                        api_name: 'SalesOrderDetail'
                    }),
                    getMasterData: jest.fn().mockReturnValue({ id: 'master1' }),
                    getDetail: jest.fn().mockReturnValue([])
                },
                dataUpdater: {
                    updateDetail: jest.fn()
                },
                lookupField: { target_api_name: 'ProductObj' },
                lookupDatas: [{
                    object_describe_api_name: 'ProductObj',
                    _id: 'prod1',
                    unit: 'unit1',
                    is_multiple_unit: true,
                    _selected_num: 3
                }],
                newDataIndexs: [mockRowId],
                objApiName: 'SalesOrderDetail',
                masterObjApiName: 'SalesOrder',
                addDatas: [{ rowId: mockRowId, product_id: 'prod1' }]
            };

            await batchAddAfter.handleMultiUnitCalc(options);

            expect(options.dataUpdater.updateDetail).toHaveBeenCalledWith(
                'SalesOrderDetail',
                mockRowId,
                expect.objectContaining({
                    quantity: 3
                }),
                false
            );
        });

        it('should handle PriceBookProduct lookup data correctly', async () => {
            const mockRowId = 'test_row_id';
            const options = {
                dataGetter: {
                    getDescribe: jest.fn().mockReturnValue({
                        fields: {
                            other_unit: { type: 'lookup' },
                            quantity: { type: 'number' }
                        }
                    }),
                    getMasterData: jest.fn().mockReturnValue({ id: 'master1' }),
                    getDetail: jest.fn().mockReturnValue([
                        { id: 'detail1', rowId: mockRowId }
                    ])
                },
                dataUpdater: {
                    updateDetail: jest.fn()
                },
                lookupField: { target_api_name: 'PriceBookProductObj' },
                lookupDatas: [{
                    object_describe_api_name: 'PriceBookProductObj',
                    _id: 'pbp1',
                    unit: 'unit1',
                    product_id__ro: {
                        unit__v: 'unit2'
                    },
                    is_multiple_unit: true,
                    price: 100
                }],
                newDataIndexs: [mockRowId],
                objApiName: 'SalesOrderDetail',
                addDatas: [{ rowId: mockRowId, product_id: 'prod1' }]
            };

            await batchAddAfter.handleMultiUnitCalc(options);

            expect(options.lookupDatas[0].unit__v).toBe('unit2');
        });

        it('should update quantity when _selected_num exists but updateData[quantity] is empty', async () => {
            const mockRowId = 'test_row_id';
            const mockFields = {
                other_unit: { type: 'lookup' },
                quantity: { type: 'number' },
                product_id: { type: 'lookup' }
            };
            
            const options = {
                dataGetter: {
                    getDescribe: jest.fn().mockReturnValue({
                        fields: mockFields,
                        api_name: 'SalesOrderDetail'
                    }),
                    getMasterData: jest.fn().mockReturnValue({ id: 'master1' }),
                    getDetail: jest.fn().mockReturnValue([])
                },
                dataUpdater: {
                    updateDetail: jest.fn()
                },
                lookupField: { target_api_name: 'ProductObj' },
                lookupDatas: [{
                    object_describe_api_name: 'ProductObj',
                    _id: 'prod1',
                    unit: 'unit1',
                    is_multiple_unit: true,
                    _selected_num: 5,
                    price: 100
                }],
                newDataIndexs: [mockRowId],
                objApiName: 'SalesOrderDetail',
                masterObjApiName: 'SalesOrder',
                addDatas: [{ rowId: mockRowId, product_id: 'prod1' }]
            };

            // Mock getDetailFields to return field mappings
            batchAddAfter.getDetailFields = jest.fn().mockReturnValue({
                quantity: 'quantity',
                product_id: 'product_id'
            });

            // Mock toMultiUnitData to return empty quantity
            const mockToMultiUnitData = jest.spyOn(batchAddAfter, 'toMultiUnitData');
            mockToMultiUnitData.mockReturnValue({});

            // Mock calcPriceByUnit to return empty result
            mockContext.calcPriceByUnit.mockResolvedValue([]);

            await batchAddAfter.handleMultiUnitCalc(options);

            // 验证 updateDetail 被调用，且 quantity 字段被正确设置
            const updateDetailCalls = options.dataUpdater.updateDetail.mock.calls;
            expect(updateDetailCalls.length).toBe(1);
            const [objApiName, dataIndex, updateData, flag] = updateDetailCalls[0];
            expect(objApiName).toBe('SalesOrderDetail');
            expect(dataIndex).toBe(mockRowId);
            expect(updateData.quantity).toBe(5);
            expect(flag).toBe(false);

            // 清理 mock
            mockToMultiUnitData.mockRestore();
        });
    });

    describe('fillOtherUnit', () => {
        it('should fill other unit for multiple unit products', async () => {
            const otherUnitField = { type: 'lookup' };
            const selectedList = [{
                object_describe_api_name: 'ProductObj',
                _id: 'prod1',
                unit: 'unit1',
                is_multiple_unit: true
            }];

            await batchAddAfter.fillOtherUnit(otherUnitField, selectedList, 'SalesOrderDetail');

            expect(mockContext.getLazyLoadOptions).toHaveBeenCalledWith('prod1', 'SalesOrderDetail');
            expect(selectedList[0].key_other_unit_id).toBe('unit2');
        });

        it('should return early if otherUnitField is not provided', async () => {
            mockContext.getLazyLoadOptions.mockReset();
            let selectedList = [{
                object_describe_api_name: 'ProductObj',
                is_multiple_unit: false,
                _id: 'prod1'
            }];
            await batchAddAfter.fillOtherUnit(null, selectedList, 'SalesOrderDetail');

            expect(mockContext.getLazyLoadOptions).not.toHaveBeenCalled();
        });
    });

    describe('buildParams', () => {
        it('should build params for multiple unit products', () => {
            const lookupDataList = [{
                _id: 'prod1',
                object_describe_api_name: 'ProductObj',
                unit: 'unit1',
                is_multiple_unit: true,
                _selected_num: 5,
                [getRealPriceResult]: {
                    _id: 'pbp1',
                    object_describe_api_name: 'PriceBookProductObj'
                }
            }];

            const result = batchAddAfter.buildParams(lookupDataList);

            expect(result).toEqual([{
                productId: 'prod1',
                priceBookProductId: 'pbp1',
                unitId: 'unit1',
                count: 5,
                otherUnitId: undefined,
                rowId: expect.any(String)
            }]);
        });
    });

    describe('getMultiUnitResult', () => {
        it('should get multi unit result when no calculation results match', () => {
            const selected = {
                unit: 'unit1',
                object_describe_api_name: 'ProductObj',
                price: 100,
                _selected_num: 5,
                is_multiple_unit: true
            };
            const objectDescribe = {
                api_name: 'SalesOrderDetail',
                fields: {
                    actual_unit: {
                        options: [{ value: 'unit1', label: '个' }]
                    }
                }
            };

            const result = batchAddAfter.getMultiUnitResult(selected, [], objectDescribe);

            expect(result).toEqual({
                price: 100,
                unitId: 'unit1',
                conversion_ratio: 1,
                base_unit_count: 5,
                stat_unit_count: '5',
                priceBookPrice: undefined,
                count: 5
            });
        });

        it('should handle PriceBookProduct with getRealPriceResult', () => {
            const selected = {
                unit: 'unit1',
                object_describe_api_name: 'PriceBookProductObj',
                pricebook_price: 90,
                pricebook_sellingprice: 100,
                _selected_num: 5,
                is_multiple_unit: true,
                [getRealPriceResult]: {
                    pricebook_price: 85,
                    selling_price: 95
                }
            };
            const objectDescribe = {
                api_name: 'SalesOrderDetail',
                fields: {
                    actual_unit: {
                        options: [{ value: 'unit1', label: '个' }]
                    }
                }
            };

            const result = batchAddAfter.getMultiUnitResult(selected, [], objectDescribe);

            expect(result.priceBookPrice).toBe(85);
            expect(result.price).toBe(95);
        });
    });

    describe('filterPrice', () => {
        it('should filter price calculation fields correctly', () => {
            const objApiName = 'SalesOrderDetail';
            const param = {
                dataGetter: {
                    getDescribe: jest.fn().mockReturnValue({
                        fields: {
                            price_book_price: {
                                default_value: '$price_book_product_id__r.pricebook_sellingprice$*$price_book_product_id__r.discount$'
                            }
                        }
                    })
                },
                filterFields: {},
                addDatas: [{ rowId: 'row1' }],
                lookupDatas: [{ is_multiple_unit: true }],
                parseParam: jest.fn().mockImplementation(p => ({
                    ...p,
                    excludedDetailCalculateFields: {
                        [objApiName]: {
                            row1: [{ fieldName: 'price_book_price', order: 1 }]
                        }
                    }
                }))
            };

            batchAddAfter.filterPrice(objApiName, param);

            expect(param.filterFields[objApiName]).toBeDefined();
            expect(param.parseParam).toBeDefined();
            const result = param.parseParam({});
            expect(result.excludedDetailCalculateFields).toBeDefined();
            expect(result.excludedDetailCalculateFields[objApiName]).toBeDefined();
        });

        it('should handle non-pricebook formula', () => {
            const objApiName = 'SalesOrderDetail';
            const param = {
                dataGetter: {
                    getDescribe: jest.fn().mockReturnValue({
                        fields: {
                            price_book_price: {
                                default_value: '$product_price$*$price_book_discount$'
                            }
                        }
                    })
                },
                filterFields: {},
                addDatas: [{ rowId: 'row1' }],
                lookupDatas: [{ is_multiple_unit: true }]
            };

            batchAddAfter.filterPrice(objApiName, param);

            expect(param.filterFields[objApiName]).toBeDefined();
        });

        it('should merge calculate fields correctly', () => {
            const objApiName = 'SalesOrderDetail';
            const param = {
                dataGetter: {
                    getDescribe: jest.fn().mockReturnValue({
                        fields: {
                            price_book_price: {
                                default_value: '$price_book_product_id__r.pricebook_sellingprice$*$price_book_product_id__r.discount$'
                            }
                        }
                    })
                },
                filterFields: {},
                addDatas: [
                    { rowId: 'row1' },
                    { rowId: 'row2' }
                ],
                lookupDatas: [
                    { is_multiple_unit: true },
                    { is_multiple_unit: true }
                ]
            };

            batchAddAfter.filterPrice(objApiName, param);

            const result = param.parseParam({});
            expect(result.excludedDetailCalculateFields[objApiName].row1).toBeDefined();
            expect(result.excludedDetailCalculateFields[objApiName].row2).toBeDefined();
        });
    });

    describe('setFieldReadonly', () => {
        beforeEach(() => {
            // 重置模拟
            mockDataUpdater = {
                setReadOnly: jest.fn(),
                setRequired: jest.fn()
            };
            
            // Mock isMultipleUnit 函数
            isMultipleUnit.mockImplementation((data, field) => data.is_multiple_unit === true);
        });
        
        it('should set fields readonly based on multiple unit status', () => {
            const options = {
                dataUpdater: mockDataUpdater,
                objApiName: 'SalesOrderDetail',
                lookupDatas: [{ is_multiple_unit: true }],
                newDataIndexs: ['row1']
            };
            
            const batchAddAfter = new BatchAddAfter(mockMultiUnitContext);
            
            // Mock getDetailFields 方法
            batchAddAfter.getDetailFields = jest.fn().mockReturnValue({
                actual_unit: 'actual_unit',
                other_unit: 'other_unit'
            });
            
            batchAddAfter.setFieldReadonly(options);
            
            expect(mockDataUpdater.setReadOnly).toHaveBeenCalledWith({
                objApiName: 'SalesOrderDetail',
                dataIndex: 'row1',
                fieldName: ['actual_unit', 'other_unit'],
                status: false
            });
            
            expect(mockDataUpdater.setRequired).toHaveBeenCalledWith({
                objApiName: 'SalesOrderDetail',
                dataIndex: 'row1',
                fieldName: ['actual_unit'],
                status: true
            });
        });

        it('should handle non-multiple unit products', () => {
            const options = {
                dataUpdater: {
                    setReadOnly: jest.fn(),
                    setRequired: jest.fn()
                },
                lookupDatas: [{ is_multiple_unit: false }],
                newDataIndexs: ['row1'],
                objApiName: 'SalesOrderDetail'
            };

            const batchAddAfter = new BatchAddAfter(mockMultiUnitContext);
            
            // Mock getDetailFields 方法
            batchAddAfter.getDetailFields = jest.fn().mockReturnValue({
                actual_unit: 'actual_unit',
                other_unit: 'other_unit'
            });
            
            batchAddAfter.setFieldReadonly(options);

            expect(options.dataUpdater.setReadOnly).toHaveBeenCalledWith({
                objApiName: 'SalesOrderDetail',
                dataIndex: 'row1',
                fieldName: ['actual_unit', 'other_unit'],
                status: true
            });
            
            // 非多单位产品不应该设置必填
            expect(options.dataUpdater.setRequired).not.toHaveBeenCalled();
        });
    });
}); 