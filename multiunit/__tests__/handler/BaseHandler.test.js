import { BaseHandler } from '../../src/package/handler/BaseHandler';
import { MultiUnitContext } from '../../src/package/MultiUnitContext';
import { multiUnitPlacesDecimal, rowId, isMultipleUnit, uuid } from '../../src/package/utils';
import mock from '../../testmock/mock';

const { baseHandlerMock, createTestOptions, createDetailData, createMockHandler } = mock;

jest.mock('../../src/package/MultiUnitContext');

// Mock utils
jest.mock('../../src/package/utils', () => ({
    isMultipleUnit: jest.fn(),
    uuid: jest.fn(),
    multiUnitPlacesDecimal: 'places_decimal__c',
    rowId: 'rowId'
}));

describe('BaseHandler', () => {
    let baseHandler;
    let mockMultiUnitContext;
    let mockPluginService;
    let mockDataUpdater;

    beforeEach(() => {
        mockDataUpdater = {
            setReadOnly: jest.fn()
        };

        mockPluginService = {
            api: {
                i18n: jest.fn(msg => msg),
                request: jest.fn()
            }
        };

        mockMultiUnitContext = new MultiUnitContext(mockPluginService, {});
        mockMultiUnitContext.getMasterFields = jest.fn().mockReturnValue({
            form_account_id: 'account_id',
            form_partner_id: 'partner_id'
        });
        mockMultiUnitContext.getDetailFields = jest.fn().mockReturnValue({
            price: 'product_price',
            quantity: 'quantity',
            actual_unit: 'actual_unit',
            conversion_ratio: 'conversion_ratio',
            base_unit_count: 'base_unit_count',
            stat_unit_count: 'stat_unit_count',
            other_unit: 'other_unit',
            other_unit_quantity: 'other_unit_quantity',
            price_book_product_id: 'price_book_product_id',
            price_book_price: 'price_book_price',
            price_book_id: 'price_book_id',
            places_decimal: 'places_decimal'
        });
        mockMultiUnitContext.pluginService = mockPluginService;

        baseHandler = new BaseHandler(mockMultiUnitContext);
        baseHandler = createMockHandler(baseHandler, { detailFields: baseHandlerMock.detailFields });
        baseHandler.dataUpdater = mockDataUpdater;
    });

    test('should initialize correctly', () => {
        expect(baseHandler).toBeDefined();
        expect(baseHandler.multiUnitContext).toBe(mockMultiUnitContext);
    });

    test('should have empty handleEvent method', () => {
        expect(baseHandler.handleEvent()).toBeUndefined();
    });

    describe('toMultiUnitData', () => {
        test('should convert multi unit result to data object', () => {
            const multiUnitResult = {
                price: 100,
                count: 5,
                unitId: 'unit1',
                base_unit_count: 10,
                conversion_ratio: 2,
                stat_unit_count: 20,
                other_unit: 'otherUnit1',
                other_unit_quantity: 15,
                places_decimal: 2,
                priceBookPrice: 90
            };

            const result = baseHandler.toMultiUnitData(multiUnitResult, 'testObj');

            expect(result).toEqual({
                price: 100,
                quantity: 5,
                other_unit: 'otherUnit1',
                price_book_price: 90,
                undefined: 15,
                places_decimal__c: undefined
            });
        });

        test('should handle price book product changes', () => {
            const multiUnitResult = {
                needChangePriceBookProduct: true,
                changePriceBookProduct: {
                    id: 'pb1',
                    name: 'Product 1',
                    containerDocument: {
                        pricebook_id: 'book1',
                        pricebook_id__r: 'Book 1'
                    }
                }
            };

            const result = baseHandler.toMultiUnitData(multiUnitResult, 'testObj');

            expect(result).toEqual({
                price_book_product_id: 'pb1',
                'price_book_product_id__r': 'Product 1',
                undefined: 'book1',
                'undefined__r': 'Book 1',
                other_unit: undefined,
                places_decimal__c: undefined,
                price: undefined,
                price_book_price: undefined
            });
        });

        test('should handle null input', () => {
            const result = baseHandler.toMultiUnitData(null, 'testObj');
            expect(result).toEqual({
                price: undefined,
                price_book_price: undefined,
                other_unit: undefined,
                undefined: undefined,
                places_decimal__c: undefined
            });
        });
    });

    describe('field getters', () => {
        test('should get master fields', () => {
            const fields = baseHandler.getMasterFields();
            expect(fields).toEqual({
                form_account_id: 'account_id',
                form_partner_id: 'partner_id'
            });
        });

        test('should get detail fields', () => {
            const fields = baseHandler.getDetailFields('testObj');
            expect(fields).toEqual(baseHandlerMock.detailFields);
        });

        test('should handle null returns from context', () => {
            mockMultiUnitContext.getMasterFields.mockReturnValue(null);
            mockMultiUnitContext.getDetailFields.mockReturnValue(null);

            expect(baseHandler.getMasterFields()).toEqual({});
            expect(baseHandler.getDetailFields('testObj')).toEqual(baseHandlerMock.detailFields);
        });
    });

    describe('getDataIndex', () => {
        test('should get row id or data index', () => {
            expect(baseHandler.getDataIndex({ rowId: 'row1' })).toBe('row1');
            expect(baseHandler.getDataIndex({ dataIndex: 'index1' })).toBe('index1');
            expect(baseHandler.getDataIndex({ rowId: 'row1', dataIndex: 'index1' })).toBe('row1');
            expect(baseHandler.getDataIndex(null)).toBeUndefined();
        });
    });

    describe('i18n', () => {
        test('should translate message', () => {
            const message = 'test.message';
            baseHandler.i18n(message);
            expect(mockPluginService.api.i18n).toHaveBeenCalledWith(message);
        });
    });

    describe('setFieldReadonly', () => {
        let mockFields;
        
        beforeEach(() => {
            mockFields = {
                actual_unit: 'actual_unit',
                other_unit: 'other_unit'
            };
            mockDataUpdater = {
                setReadOnly: jest.fn(),
                setRequired: jest.fn()
            };
        });
        
        it('should set fields readonly based on is_multiple_unit', () => {
            baseHandler.setFieldReadonly = function({dataUpdater, objApiName, dataIndex, isMultipleUnit}) {
                let {actual_unit, other_unit} = mockFields;
                let fields = [actual_unit, other_unit];
                
                dataUpdater && dataUpdater.setReadOnly && dataUpdater.setReadOnly({
                    objApiName: objApiName,
                    dataIndex: dataIndex,
                    fieldName: fields,
                    status: !isMultipleUnit
                });
                
                if(isMultipleUnit){
                    dataUpdater && dataUpdater.setRequired && dataUpdater.setRequired({
                        objApiName: objApiName,
                        dataIndex: dataIndex,
                        fieldName: [actual_unit],
                        status: true
                    });
                }
            };
            
            const options = {
                dataUpdater: mockDataUpdater,
                dataIndex: 'row1',
                objApiName: 'testObj',
                isMultipleUnit: false
            };
            
            baseHandler.setFieldReadonly(options);
            
            expect(mockDataUpdater.setReadOnly).toHaveBeenCalledWith({
                objApiName: 'testObj',
                dataIndex: 'row1',
                fieldName: ['actual_unit', 'other_unit'],
                status: true
            });
            
            const options2 = {
                dataUpdater: mockDataUpdater,
                dataIndex: 'row2',
                objApiName: 'testObj',
                isMultipleUnit: true
            };
            
            baseHandler.setFieldReadonly(options2);
            
            expect(mockDataUpdater.setReadOnly).toHaveBeenCalledWith({
                objApiName: 'testObj',
                dataIndex: 'row2',
                fieldName: ['actual_unit', 'other_unit'],
                status: false
            });
            
            expect(mockDataUpdater.setRequired).toHaveBeenCalledWith({
                objApiName: 'testObj',
                dataIndex: 'row2',
                fieldName: ['actual_unit'],
                status: true
            });
        });

        test('should handle empty input', () => {
            baseHandler.setFieldReadonly();
            baseHandler.setFieldReadonly({});
            baseHandler.setFieldReadonly({ list: [] });
            // No errors should be thrown
        });
    });

    describe('getRowId', () => {
        beforeEach(() => {
            uuid.mockReturnValue('generated-uuid');
        });

        test('should get or generate row id', () => {
            const existingData = { [rowId]: 'existing-id' };
            expect(baseHandler.getRowId(existingData)).toBe('existing-id');
            expect(existingData[rowId]).toBe('existing-id');

            const newData = {};
            const newId = baseHandler.getRowId(newData);
            expect(newId).toBe('generated-uuid');
            expect(newData[rowId]).toBe('generated-uuid');

            const regenerateData = { [rowId]: 'old-id' };
            const regeneratedId = baseHandler.getRowId(regenerateData, true);
            expect(regeneratedId).toBe('generated-uuid');
            expect(regenerateData[rowId]).toBe('generated-uuid');
        });
    });
}); 