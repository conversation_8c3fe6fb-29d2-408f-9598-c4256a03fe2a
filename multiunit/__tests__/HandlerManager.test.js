import { HandlerManager } from '../src/package/HandlerManager';
import { MultiUnitContext } from '../src/package/MultiUnitContext';
import { events } from '../src/package/events';
import { FormRenderAfter } from '../src/package/handler/FormRenderAfter';
import { BatchAddAfter } from '../src/package/handler/BatchAddAfter';
import { FieldChangeAfter } from '../src/package/handler/FieldChangeAfter';
import { FieldChangeBefore } from '../src/package/handler/FieldChangeBefore';
import { BatchTodoMultiunit } from '../src/package/handler/BatchTodoMultiunit';
import { BatchAddBeforeCalUiEvent } from '../src/package/handler/BatchAddBeforeCalUiEvent';
import { PriceService } from '../src/package/handler/PriceService';
import { <PERSON><PERSON> } from '../src/package/handler/Bom';
import { CopyAfter } from '../src/package/handler/CopyAfter';
import { MdTileBefore } from '../src/package/handler/MdTileBefore';
import { BaseHandler } from '../src/package/handler/BaseHandler';

jest.mock('../src/package/handler/FormRenderAfter');
jest.mock('../src/package/handler/BatchAddAfter');
jest.mock('../src/package/handler/FieldChangeAfter');
jest.mock('../src/package/handler/FieldChangeBefore');
jest.mock('../src/package/handler/BatchTodoMultiunit');
jest.mock('../src/package/handler/BatchAddBeforeCalUiEvent');
jest.mock('../src/package/handler/PriceService');
jest.mock('../src/package/handler/Bom');
jest.mock('../src/package/handler/CopyAfter');
jest.mock('../src/package/handler/MdTileBefore');
jest.mock('../src/package/handler/BaseHandler');

describe('HandlerManager', () => {
    let handlerManager;
    let mockMultiUnitContext;
    let mockOptions;
    let mockPluginExecResult;

    beforeEach(() => {
        // 重置所有模拟的实现
        jest.clearAllMocks();

        mockMultiUnitContext = new MultiUnitContext({}, {});
        mockOptions = { option: 'test' };
        mockPluginExecResult = { result: 'test' };
        handlerManager = new HandlerManager(mockMultiUnitContext);

        // 为所有处理器类添加模拟的handleEvent方法
        const mockHandleEvent = jest.fn().mockResolvedValue({ success: true });
        [
            FormRenderAfter,
            BatchAddAfter,
            FieldChangeAfter,
            FieldChangeBefore,
            BatchTodoMultiunit,
            BatchAddBeforeCalUiEvent,
            PriceService,
            Bom,
            CopyAfter,
            MdTileBefore,
            BaseHandler
        ].forEach(HandlerClass => {
            HandlerClass.prototype.handleEvent = mockHandleEvent;
        });
    });

    test('should initialize correctly', () => {
        expect(handlerManager).toBeDefined();
        expect(handlerManager.handlers).toEqual({});
        expect(handlerManager.multiUnitContext).toBe(mockMultiUnitContext);
    });

    test('should handle form render after event', async () => {
        const result = await handlerManager.handleEvent(events.form_render_after, mockPluginExecResult, mockOptions);
        expect(result).toEqual({ success: true });
        expect(FormRenderAfter).toHaveBeenCalledWith(mockMultiUnitContext);
    });

    test('should handle batch add after event', async () => {
        const result = await handlerManager.handleEvent(events.md_batchAdd_after, mockPluginExecResult, mockOptions);
        expect(result).toEqual({ success: true });
        expect(BatchAddAfter).toHaveBeenCalledWith(mockMultiUnitContext);
    });

    test('should handle field change after event', async () => {
        const result = await handlerManager.handleEvent(events.field_change_after, mockPluginExecResult, mockOptions);
        expect(result).toEqual({ success: true });
        expect(FieldChangeAfter).toHaveBeenCalledWith(mockMultiUnitContext);
    });

    test('should handle field change before event', async () => {
        const result = await handlerManager.handleEvent(events.field_change_before, mockPluginExecResult, mockOptions);
        expect(result).toEqual({ success: true });
        expect(FieldChangeBefore).toHaveBeenCalledWith(mockMultiUnitContext);
    });

    test('should handle batch todo multiunit event', async () => {
        const result = await handlerManager.handleEvent(events.multiunit_batchTodoMultiunit, mockPluginExecResult, mockOptions);
        expect(result).toEqual({ success: true });
        expect(BatchTodoMultiunit).toHaveBeenCalledWith(mockMultiUnitContext);
    });

    test('should handle price service events', async () => {
        const priceServiceEvents = [
            events.priceService_batchAdd_getPriceParam,
            events.priceService_batchAdd_matchGetPriceResult,
            events.priceService_form_getPriceParam,
            events.priceService_form_matchGetPriceResult,
            events.priceService_batchAddAfter_parseData
        ];

        for (const event of priceServiceEvents) {
            const result = await handlerManager.handleEvent(event, mockPluginExecResult, mockOptions);
            expect(result).toEqual({ success: true });
            expect(PriceService).toHaveBeenCalledWith(mockMultiUnitContext);
        }
    });

    test('should handle bom events', async () => {
        const bomEvents = [
            events.bom_md_edit_after,
            events.bom_md_batchAdd_end
        ];

        for (const event of bomEvents) {
            const result = await handlerManager.handleEvent(event, mockPluginExecResult, mockOptions);
            expect(result).toEqual({ success: true });
            expect(Bom).toHaveBeenCalledWith(mockMultiUnitContext);
        }
    });

    test('should handle copy after event', async () => {
        const result = await handlerManager.handleEvent(events.md_copy_after, mockPluginExecResult, mockOptions);
        expect(result).toEqual({ success: true });
        expect(CopyAfter).toHaveBeenCalledWith(mockMultiUnitContext);
    });

    test('should handle md tile before event', async () => {
        const result = await handlerManager.handleEvent(events.md_tile_before, mockPluginExecResult, mockOptions);
        expect(result).toEqual({ success: true });
        expect(MdTileBefore).toHaveBeenCalledWith(mockMultiUnitContext);
    });

    test('should handle set field readonly event', async () => {
        const result = await handlerManager.handleEvent(events.set_field_readonly, mockPluginExecResult, mockOptions);
        expect(BaseHandler).toHaveBeenCalledWith(mockMultiUnitContext);
        const mockBaseHandler = BaseHandler.mock.instances[0];
        expect(mockBaseHandler.setFieldReadonly).toHaveBeenCalledWith(mockOptions);
    });

    test('should reuse existing handler instance', async () => {
        // 调用两次相同的事件
        await handlerManager.handleEvent(events.form_render_after, mockPluginExecResult, mockOptions);
        await handlerManager.handleEvent(events.form_render_after, mockPluginExecResult, mockOptions);

        // FormRenderAfter构造函数应该只被调用一次
        expect(FormRenderAfter).toHaveBeenCalledTimes(1);
    });

    test('should return undefined for unknown event', async () => {
        const result = await handlerManager.handleEvent('unknown.event', mockPluginExecResult, mockOptions);
        expect(result).toBeUndefined();
    });
}); 