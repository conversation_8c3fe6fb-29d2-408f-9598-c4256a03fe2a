import { MultiUnitApi } from '../src/package/MultiUnitApi';

describe('MultiUnitApi', () => {
    let multiUnitApi;
    let mockHttp;

    beforeEach(() => {
        mockHttp = jest.fn();
        multiUnitApi = new MultiUnitApi(mockHttp);
    });

    describe('calc', () => {
        const mockCalcParams = {
            params: { param1: 'value1' },
            mcCurrency: 'USD',
            detailObjectApiName: 'DetailObj',
            masterObjectApiName: 'MasterObj'
        };

        test('should call calc API correctly', async () => {
            const mockResponse = {
                Value: {
                    caclResult: { calculated: true }
                },
                Result: {
                    FailureCode: 0,
                    StatusCode: 0
                }
            };

            mockHttp.mockResolvedValue(mockResponse);

            const result = await multiUnitApi.calc(mockCalcParams);

            expect(mockHttp).toHaveBeenCalledWith({
                url: 'FHH/EM1ANCRM/API/v1/object/mutipleUnit/service/calc',
                data: {
                    params: mockCalcParams.params,
                    mcCurrency: mockCalcParams.mcCurrency,
                    objectApiName: mockCalcParams.masterObjectApiName,
                    detailObjectApiName: mockCalcParams.detailObjectApiName,
                }
            });
            expect(result).toEqual({ calculated: true });
        });
    });

    describe('getOptions', () => {
        test('should call getOptions API correctly', async () => {
            const mockResponse = {
                Value: {
                    optionList: ['option1', 'option2']
                },
                Result: {
                    FailureCode: 0,
                    StatusCode: 0
                }
            };

            mockHttp.mockResolvedValue(mockResponse);

            const result = await multiUnitApi.getOptions('product1', 'sourceObj');

            expect(mockHttp).toHaveBeenCalledWith({
                url: 'FHH/EM1ANCRM/API/v1/object/lazyLoadOptions/service/getLazyLoadOptions',
                data: {
                    dataId: 'product1',
                    dataObjApiName: 'ProductObj',
                    targetObjApiName: 'UnitInfoObj',
                    sourceObjApiName: 'sourceObj',
                    extend_info: undefined
                }
            });
            expect(result).toEqual(['option1', 'option2']);
        });
    });

    describe('batchCalculate', () => {
        const mockBatchCalcParams = {
            masterObjectApiName: 'MasterObj',
            masterData: { data: 'master' },
            detailDataMap: { detail: 'data' },
            modifiedObjectApiName: 'ModifiedObj',
            modifiedDataIndexList: [1, 2],
            calculateFields: ['field1', 'field2']
        };

        test('should call batchCalculate API correctly', async () => {
            const mockResponse = {
                Value: {
                    calculateResult: { success: true }
                },
                Result: {
                    FailureCode: 0,
                    StatusCode: 0
                }
            };

            mockHttp.mockResolvedValue(mockResponse);

            const result = await multiUnitApi.batchCalculate(mockBatchCalcParams);

            expect(mockHttp).toHaveBeenCalledWith({
                url: 'FHH/EM1HNCRM/API/v1/object/calculate/service/batchCalculate',
                data: mockBatchCalcParams
            });
            expect(result).toEqual({ success: true });
        });
    });

    describe('error handling', () => {
        test('should handle string response as error', async () => {
            mockHttp.mockResolvedValue('Error message');

            await expect(multiUnitApi.calc({})).rejects.toEqual('Error message');
        });

        test('should handle Error object in response', async () => {
            mockHttp.mockResolvedValue({
                Error: {
                    Message: 'API Error'
                }
            });

            await expect(multiUnitApi.calc({})).rejects.toEqual('API Error');
        });

        test('should handle FailureCode in response', async () => {
            mockHttp.mockResolvedValue({
                Result: {
                    FailureCode: 1,
                    FailureMessage: 'Operation failed'
                }
            });

            await expect(multiUnitApi.calc({})).rejects.toEqual('Operation failed');
        });

        test('should handle StatusCode in response', async () => {
            mockHttp.mockResolvedValue({
                Result: {
                    StatusCode: 1
                }
            });

            await expect(multiUnitApi.calc({})).rejects.toEqual('未知异常');
        });

        test('should handle missing http client', async () => {
            const apiWithoutHttp = new MultiUnitApi(null);
            await expect(apiWithoutHttp.calc({})).rejects.toEqual('MultiUnitApi：http can not be null');
        });

        test('should handle http request error', async () => {
            mockHttp.mockRejectedValue(new Error('Network error'));
            await expect(multiUnitApi.calc({})).rejects.toThrow('Network error');
        });
    });
}); 