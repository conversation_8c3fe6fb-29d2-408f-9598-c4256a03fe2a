import {
    isMultipleUnit,
    isObject,
    isArray,
    isEmpty,
    uuid,
    multiUnitPlacesDecimal,
    getRealPriceResult,
    rowId
} from '../src/package/utils';

describe('Utils', () => {
    describe('isMultipleUnit', () => {
        test('should handle null or undefined input', () => {
            expect(isMultipleUnit(null, 'field')).toBe(false);
            expect(isMultipleUnit(undefined, 'field')).toBe(false);
            expect(isMultipleUnit({}, null)).toBe(false);
            expect(isMultipleUnit({}, undefined)).toBe(false);
        });

        test('should handle boolean values', () => {
            expect(isMultipleUnit({ field: true }, 'field')).toBe(true);
            expect(isMultipleUnit({ field: false }, 'field')).toBe(false);
        });

        test('should handle string values', () => {
            expect(isMultipleUnit({ field: '是' }, 'field')).toBe(true);
            expect(isMultipleUnit({ field: 'true' }, 'field')).toBe(true);
            expect(isMultipleUnit({ field: '否' }, 'field')).toBe(false);
            expect(isMultipleUnit({ field: 'false' }, 'field')).toBe(false);
        });

        test('should handle __v suffix', () => {
            expect(isMultipleUnit({ field: 'false', 'field__v': true }, 'field')).toBe(true);
            expect(isMultipleUnit({ field: 'true', 'field__v': false }, 'field')).toBe(false);
        });

        test('should handle product_id__ro', () => {
            const data = {
                product_id__ro: {
                    field: true
                }
            };
            expect(isMultipleUnit(data, 'field')).toBe(true);
        });
    });

    describe('isObject', () => {
        test('should identify objects correctly', () => {
            expect(isObject({})).toBe(true);
            expect(isObject({ a: 1 })).toBe(true);
            expect(isObject([])).toBe(true);
            expect(isObject(null)).toBe(false);
            expect(isObject(undefined)).toBe(false);
            expect(isObject('')).toBe(false);
            expect(isObject(123)).toBe(false);
            expect(isObject(true)).toBe(false);
        });
    });

    describe('isArray', () => {
        test('should identify arrays correctly', () => {
            expect(isArray([])).toBe(true);
            expect(isArray([1, 2, 3])).toBe(true);
            expect(isArray({})).toBe(false);
            expect(isArray(null)).toBe(false);
            expect(isArray(undefined)).toBe(false);
            expect(isArray('')).toBe(false);
            expect(isArray(123)).toBe(false);
        });
    });

    describe('isEmpty', () => {
        test('should check empty values correctly', () => {
            // Objects
            expect(isEmpty({})).toBe(true);
            expect(isEmpty({ a: 1 })).toBe(false);

            // Arrays
            expect(isEmpty([])).toBe(true);
            expect(isEmpty([1, 2])).toBe(false);

            // Strings
            expect(isEmpty('')).toBe(true);
            expect(isEmpty('text')).toBe(false);

            // Numbers
            expect(isEmpty(0)).toBe(false);
            expect(isEmpty(1)).toBe(false);

            // Booleans
            expect(isEmpty(false)).toBe(false);
            expect(isEmpty(true)).toBe(false);

            // Null/Undefined
            expect(isEmpty(null)).toBe(true);
            expect(isEmpty(undefined)).toBe(true);
        });
    });

    describe('uuid', () => {
        test('should generate valid uuid string', () => {
            const id = uuid();
            expect(typeof id).toBe('string');
            expect(id.length).toBe(32); // 32 characters without hyphens
            expect(id).toMatch(/^[0-9a-f]{32}$/); // should be hex digits
        });

        test('should generate unique values', () => {
            const ids = new Set();
            for (let i = 0; i < 1000; i++) {
                ids.add(uuid());
            }
            expect(ids.size).toBe(1000); // All IDs should be unique
        });
    });

    describe('constants', () => {
        test('should export correct constant values', () => {
            expect(multiUnitPlacesDecimal).toBe('multi_unit_places_decimal');
            expect(getRealPriceResult).toBe('get_real_price_result');
            expect(rowId).toBe('key_row_id');
        });
    });
}); 