import {each, isEmpty, uniq} from "../../pluginbase-ava/package/pluginutils";

class AdvancedFormulaCache {

    constructor() {
        this.detailFormulaMap = {};// {productId: {triggerField: []}}
        this.masterFormulaMap = {};// {productId: {triggerField: []}}
    }

    add(isMaster, key, formulaObj) {
        if (!isEmpty(key) && !isEmpty(formulaObj)) {
            if (isMaster) {
                this.masterFormulaMap[key] = formulaObj;
            } else {
                this.detailFormulaMap[key] = formulaObj;
            }
        }
    }

    /**
     * 获取主对象触发高级公式的字段
     * @return {*}
     */
    getMasterTriggerFields() {
        let fields = [];
        let formulaMap = this.masterFormulaMap || {};
        each(formulaMap, (result, key) => {
            fields.push(...Object.keys(result));
        });
        return uniq(fields);
    }

    /**
     * 获取该产品或bom触发高级公式的字段
     * @param key
     * @return {*}
     */
    getDetailTriggerFields(key) {
        let triggerObj = !isEmpty(key) && (this.detailFormulaMap[key]);
        return isEmpty(triggerObj) ? [] : Object.keys(triggerObj);
    }

    /**
     * 获取该产品或bom触发高级公式的字段
     * @param keys
     * @return {*}
     */
    batchGetDetailTriggerFields(keys) {
        let fields = [];
        keys && keys.length && keys.forEach(key => {
            let triggerFields = this.getDetailTriggerFields(key);
            if (triggerFields && triggerFields.length) {
                fields.push(...triggerFields);
            }
        })
        return uniq(fields);
    }

    getMasterFieldChangedCalcFields(changedFields) {
        let calcFields = {}
        let formulaMap = this.masterFormulaMap || {};
        changedFields && changedFields.length && changedFields.forEach(fieldName => {
            each(formulaMap, (result, key) => {
                each(result, (triggerResultFields, triggerField) => {
                    if (triggerField === fieldName && triggerResultFields && triggerResultFields.length) {
                        (calcFields[key] || (calcFields[key] = [])).push(...triggerResultFields);
                    }
                })
            });
        })
        return calcFields;
    }

    getDetailFieldChangedCalcFields(keys, changedFields) {
        let calFields = [];
        keys && keys.length && changedFields && changedFields.length && keys.forEach(key => {
            let result = this.detailFormulaMap[key];
            changedFields.forEach(fieldName => {
                let calcFields = result && result[fieldName];
                if (calcFields && calcFields.length) {
                    calFields.push(...calcFields);
                }
            })
        })
        return uniq(calFields);
    }

    /**
     * 获取该产品或bom触发高级公式后更新的字段
     * @param key
     * @param includeMaster
     * @return {*}
     */
    getTriggerResultFields(key, includeMaster = true) {
        let resultFields = [];
        let masterTriggerObj = !isEmpty(key) && (this.masterFormulaMap[key]);
        each(masterTriggerObj, (triggerResultFields, triggerField) => {
            if (triggerResultFields && triggerResultFields.length) {
                resultFields.push(...triggerResultFields);
            }
        })
        let detailTriggerObj = !isEmpty(key) && (this.detailFormulaMap[key]);
        each(detailTriggerObj, (triggerResultFields, triggerField) => {
            if (triggerResultFields && triggerResultFields.length) {
                resultFields.push(...triggerResultFields);
            }
        });
        return uniq(resultFields);
    }

    /**
     * 批量获取该产品或bom触发高级公式后更新的字段
     * @param keys
     * @param includeMaster
     * @return {*}
     */
    batchGetTriggerResultFields(keys, includeMaster = true) {
        let calFields = [];
        keys && keys.length && keys.forEach(key => {
            let fields = this.getTriggerResultFields(key, includeMaster);
            fields && fields.length && (calFields.push(...fields));
        })
        return uniq(calFields);
    }

    async requestAdvancedFormulaList(masterObjApiName, detailObjApiName, detailDataList, requester, detailFieldMap) {
        function newFilter(field_name, operator, field_values) {
            return {field_name, operator, field_values}
        }

        if (!detailDataList || !detailDataList.length) {
            return;
        }
        let productIds = [];
        let bomIds = [];
        let {bom_id = 'bom_id', product_id = 'product_id'} = detailFieldMap || {};
        detailDataList.forEach(it => {
            let {[bom_id]: bomId, [product_id]: productId} = it;
            // (!isEmpty(product_id)) && (productIds.push(product_id));
            // (!isEmpty(bom_id)) && (bomIds.push(bom_id));
            if (!isEmpty(bomId)) {
                bomIds.push(bomId);
            } else if (!isEmpty(productId)) {
                productIds.push(productId);
            }
        })
        let wheres = [];
        let objectFilter = newFilter('ref_object_api_name', 'EQ', [detailObjApiName]);
        productIds.length && (wheres.push({
            connector: "OR",
            filters: [objectFilter, newFilter('product_id', 'IN', productIds)]
        }));
        bomIds.length && (wheres.push({
            connector: "OR",
            filters: [objectFilter, newFilter('bom_id', 'IN', bomIds)]
        }));
        let result = await requester({
            apiName: detailObjApiName,
            queryInfo: JSON.stringify({wheres})
        });
        if (result) {
            each(result, (value, key) => {
                let {[masterObjApiName]: masterFields, [detailObjApiName]: detailFields} = value || {}
                let masterFormula = this.getFormula(masterFields, detailObjApiName);
                let detailFormula = this.getFormula(detailFields, detailObjApiName);
                this.add(true, key, masterFormula);
                this.add(false, key, detailFormula);
            });
        }
    }

    getFormula(objFields, detailObjApiName) {
        let formulaObj = {};
        each(objFields, (triggerResult, triggerField) => {
            let {calculateFields} = triggerResult || {};
            let detailCalculateFields = calculateFields && calculateFields[detailObjApiName];
            if (detailCalculateFields && detailCalculateFields.length) {
                formulaObj[triggerField] = detailCalculateFields;
            }
        });
        return formulaObj;
    }
}

export default new AdvancedFormulaCache();