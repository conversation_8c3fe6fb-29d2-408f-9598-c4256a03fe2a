import {cloneDeep, isEmpty, each} from "../../pluginbase-ava/package/pluginutils";

export class AttributePicker {

    constructor(onDataChangedListener) {
        this.release();
        this.onDataChangedListener = onDataChangedListener;
    }

    pick(attributeMap, notifyDataChanged = true) {
        if (!isEmpty(attributeMap)) {
            Object.assign(this.attributeMap, attributeMap);
            each(this.attributeMap, (value, key) => {
                if (!value || !value.length) {
                    delete this.attributeMap[key];
                }
            })
            notifyDataChanged && this.onDataChangedListener && this.onDataChangedListener();
        }
    }

    unpick(uniqueId, notifyDataChanged = true) {
        if (isEmpty(uniqueId)) {
            return;
        }
        delete this.attributeMap[uniqueId];
        notifyDataChanged && this.onDataChangedListener && this.onDataChangedListener();
    }

    getAttributeMap() {
        return cloneDeep(this.attributeMap);
    }

    release(notifyDataChanged = true) {
        this.attributeMap = {};
        notifyDataChanged && this.onDataChangedListener && this.onDataChangedListener();
    }
}