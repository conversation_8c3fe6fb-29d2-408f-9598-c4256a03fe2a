import {each, isEmpty, uniq, uuid} from "../../pluginbase-ava/package/pluginutils";
import advancedFormulaCache from "./advancedFormulaCache";
import commonLogic from "./commonLogic";

export const TriggerType = Object.freeze({
    ADD: 'add',//新建
    EDIT_MASTER: 'edit_master',//修改主对象字段
    EDIT_DETAIL: 'edit_detail',//修改从对象字段
    RECONFIGURATION: 'reconfiguration',//二次配置
});

export class QuoterCalculate {

    constructor(context) {
        let {fieldMapping, pluginApi, requestApi} = context || {};
        this.fieldMapping = fieldMapping;
        this.pluginApi = pluginApi;
        this.requestApi = requestApi;
    }

    async formRenderEnd(pluginExecResult, options) {
        let {dataGetter} = options;
        let objApiName = this.fieldMapping.getFirstDetailObjApiName();
        let detailDataList = dataGetter.getDetail(objApiName);
        await this.handleAddNewData(options, detailDataList);
    }

    async masterFieldEditEnd(options) {
        let {fieldName, dataGetter} = options;
        let objApiName = this.fieldMapping.getFirstDetailObjApiName();
        let detailDatas = dataGetter.getDetail(objApiName);
        let detailCalFields = this.getDetailCalcFieldsByMasterChanged([fieldName], detailDatas);
        if (!isEmpty(detailCalFields)) {
            return await this.quoteBatchCalculate(options, detailCalFields, TriggerType.EDIT_MASTER, {changeFieldApiName: fieldName});
        }
    }

    async detailFieldEditEnd(options) {
        let {fieldName, dataIndex, dataGetter} = options;
        let objApiName = this.fieldMapping.getFirstDetailObjApiName();
        let {quantity} = this.fieldMapping.getDetailFields(objApiName);
        let updateDetailsFields = {};
        (updateDetailsFields[dataIndex] || (updateDetailsFields[dataIndex] = [])).push(fieldName);
        //如果修改的是包的数量，子件也要参数高级公式计算
        if (fieldName === quantity) {
            let subDetails = this.pluginApi.runPluginSync('bom.getSubDetailsFromPkg.sync', Object.assign(options, {
                objApiName,
                dataIndex
            }));
            subDetails && subDetails.length && subDetails.forEach(it => {
                let dataIndex = it.dataIndex;
                (updateDetailsFields[dataIndex] || (updateDetailsFields[dataIndex] = [])).push(fieldName);
            })
        }
        let detailDatas = dataGetter.getDetail(objApiName);
        let detailCalFields = this.getDetailCalcFieldsByDetailChanged(updateDetailsFields, detailDatas);
        if (!isEmpty(detailCalFields)) {
            return await this.quoteBatchCalculate(options, detailCalFields, TriggerType.EDIT_DETAIL, {
                changeFieldApiName: fieldName,
                changeDataIndex: dataIndex
            });
        }
    }

    async mdBatchAddEnd(options) {
        let {newDatas} = options;
        await this.handleAddNewData(options, newDatas);
        let detailCalFields = this.getDetailCalcFieldsByAddNew(newDatas);
        if (!isEmpty(detailCalFields)) {
            let addNewDataIndexs = newDatas && newDatas.length && newDatas.map(it => it.dataIndex) || [];
            return await this.quoteBatchCalculate(options, detailCalFields, TriggerType.ADD, {addNewDataIndexs});
        }
    }

    async mdDelEnd(options) {
        //do nothing
    }

    async bomReconfigurationEnd(options, detailDataList) {
        if (!detailDataList || !detailDataList.length) {
            return;
        }
        let {objApiName, bomChangedInfo, dataGetter} = options;
        objApiName = objApiName || this.fieldMapping.getFirstDetailObjApiName();
        let {updateDetailData, addNewDataIndexList} = bomChangedInfo || {};
        let detailDatas = dataGetter.getDetail(objApiName);
        let addNewDatas = addNewDataIndexList && addNewDataIndexList.length && addNewDataIndexList.map(dataIndex => {
            return detailDatas && detailDatas.find(it => it.dataIndex === dataIndex);
        });
        await this.handleAddNewData(options, addNewDatas);
        let updateDetailsFields = {};
        (!isEmpty(updateDetailData)) && each(updateDetailData, (updateData, dataIndex) => {
            updateDetailsFields[dataIndex] = isEmpty(updateData) ? [] : Object.keys(updateData);
        })
        let byDetailChanged = this.getDetailCalcFieldsByDetailChanged(updateDetailsFields, detailDatas);
        let byAddNew = this.getDetailCalcFieldsByAddNew(addNewDatas);
        let detailCalFields = {};
        detailCalFields = this.mergeCalcFields(detailCalFields, byDetailChanged);
        detailCalFields = this.mergeCalcFields(detailCalFields, byAddNew);
        if (!isEmpty(detailCalFields)) {
            return await this.quoteBatchCalculate(options, detailCalFields, TriggerType.RECONFIGURATION, {});
        }
    }

    quoteBatchCalculate(options, detailCalFields, triggerType, param) {
        let {dataGetter, dataUpdater, formApis} = options;
        let masterData = dataGetter.getMasterData();
        let objApiName = this.fieldMapping.getFirstDetailObjApiName();
        let detailDataMap = {};
        let detailDatas = dataGetter.getDetail(objApiName);
        detailDatas && detailDatas.forEach(it => {
            let {dataIndex} = it;
            if (isEmpty(dataIndex)) {
                dataIndex = formApis.createNewDataIndex();
                it.dataIndex = dataIndex;
            }
            detailDataMap[dataIndex] = Object.assign({}, it);
        })
        let fieldMapping = this.fieldMapping.getDetailFields(objApiName);
        let quoterParam = {
            detailCalculateFieldApiNames: {
                [objApiName]: detailCalFields
            },
            detailDataMap,
            detailObjectApiName: objApiName,
            fieldsMap: fieldMapping,
            masterData: Object.assign({}, masterData),
            modifiedObjectApiName: objApiName,
            modifiedDataIndexList: Object.keys(detailCalFields || {})
        }
        this.pluginApi.runPlugin('quoter.execute.before', Object.assign({}, options, {objApiName, quoterParam}));
        let pageId = dataGetter.getPageId();
        let token = 'quoter_' + uuid();
        this.pluginApi.showSingletonLoading(token, {}, pageId);
        return this.requestApi.quoteBatchCalculate(quoterParam).then(async result => {
            let filterDataIndexList = await this.pluginApi.runPlugin('quoter.execute.after', Object.assign({}, options, {
                objApiName,
                quoterResult: result,
                param
            }));
            let changeFields = [];//修改的字段
            let modifiedIndexList = [];//修改的明细行
            let calcFilterFields = {};//报价器执行后触发计算，不计算的字段
            result && result.length && result.forEach(it => {
                let {data_index} = it;
                if (filterDataIndexList && filterDataIndexList.length && filterDataIndexList.includes(data_index)) {
                    return;
                }
                calcFilterFields[data_index] = Object.keys(it);
                dataUpdater.updateDetail(objApiName, data_index, it);
                modifiedIndexList.push(data_index);
                changeFields.push(...Object.keys(it));
            });
            await formApis.triggerCalAndUIEvent({
                objApiName: objApiName,
                modifiedDataIndexs: modifiedIndexList,
                changeFields: uniq(changeFields),
                beforeCalPost(p) {
                    if (p && p.data && !isEmpty(calcFilterFields)) {
                        let excludedDetailCalculateFields = {};
                        each(calcFilterFields, (value, key) => {
                            if (value && value.length) {
                                excludedDetailCalculateFields[key] = value.map(it => {
                                    return {fieldName: it, order: 1}
                                })
                            }
                        })
                        p.data.excludedDetailCalculateFields = {[objApiName]: excludedDetailCalculateFields};
                    }
                }
            });
            await this.pluginApi.runPlugin('quoter.execute.end', Object.assign({}, options, {
                objApiName,
                quoterResult: result,
                param
            }));
            return this.triggerUIEvent(options, Object.assign({}, param, {
                triggerType
            }));
        }).then(() => {
            this.pluginApi.hideSingletonLoading(token, pageId);
        }).catch(err => {
            this.pluginApi.hideSingletonLoading(token, pageId);
            this.pluginApi.showToast(err);
        });
    }

    async handleAddNewData(options, addNewDatas) {
        if (addNewDatas && addNewDatas.length) {
            let {masterObjApiName, dataUpdater} = options;
            let objApiName = this.fieldMapping.getFirstDetailObjApiName();
            let fieldMapping = this.fieldMapping.getDetailFields(objApiName);
            //获取配置高级公式的字段
            await this.queryAdvancedFormulaList(masterObjApiName, objApiName, addNewDatas);
            //设置高级公式计算结果的字段只读
            commonLogic.setFieldReadonly(dataUpdater, addNewDatas, fieldMapping);
        }
    }

    queryAdvancedFormulaList(masterObjApiName, objApiName, detailDataList) {
        objApiName = objApiName || this.fieldMapping.getFirstDetailObjApiName();
        let requester = this.requestApi.queryAdvancedFormulaList.bind(this.requestApi);
        let detailFieldMap = this.fieldMapping.getDetailFields(objApiName);
        return advancedFormulaCache.requestAdvancedFormulaList(masterObjApiName, objApiName, detailDataList, requester, detailFieldMap)
            .catch(err => {
                this.pluginApi.showToast(err);
            });
    }

    getDetailCalcFieldsByMasterChanged(updateMasterFields, detailDatas) {
        if (!updateMasterFields || !updateMasterFields.length || !detailDatas || !detailDatas.length) {
            return;
        }
        let triggerFields = advancedFormulaCache.getMasterTriggerFields();
        let isTrigger = triggerFields && triggerFields.length && triggerFields.some(it => {
            return updateMasterFields.includes(it);
        });
        let calcFields = isTrigger ? advancedFormulaCache.getMasterFieldChangedCalcFields(updateMasterFields) : null;
        if (isEmpty(calcFields)) {
            return;
        }
        let detailCalFields = {};
        let objApiName = this.fieldMapping.getFirstDetailObjApiName();
        let fieldMapping = this.fieldMapping.getDetailFields(objApiName);
        let {product_id, bom_id} = fieldMapping;
        detailDatas.forEach(it => {
            let {dataIndex, [product_id]: productId, [bom_id]: bomId} = it;
            let productCalcFields = productId ? calcFields[productId] : null;
            let bomCalcFields = bomId ? calcFields[bomId] : null;
            let allCalcFields = [...(productCalcFields || []), ...(bomCalcFields || [])];
            (allCalcFields && allCalcFields.length) && (detailCalFields[dataIndex] = allCalcFields)
        });
        return detailCalFields;
    }

    getDetailCalcFieldsByDetailChanged(updateDetailsFields, detailDatas) {
        if (isEmpty(updateDetailsFields) || !detailDatas || !detailDatas.length) {
            return;
        }
        let detailCalFields = {};
        let objApiName = this.fieldMapping.getFirstDetailObjApiName();
        let fieldMapping = this.fieldMapping.getDetailFields(objApiName);
        let {product_id, bom_id} = fieldMapping;
        each(updateDetailsFields, (changedFields, dataIndex) => {
            let objectData = detailDatas.find(it => it.dataIndex === dataIndex);
            if (objectData) {
                let {[product_id]: productId, [bom_id]: bomId} = objectData
                let triggerFields = advancedFormulaCache.batchGetDetailTriggerFields([productId, bomId]);
                let isTrigger = triggerFields && triggerFields.length && triggerFields.some(it => {
                    return changedFields.includes(it);
                });
                if (isTrigger) {
                    let calcFields = advancedFormulaCache.getDetailFieldChangedCalcFields([productId, bomId], changedFields);
                    (calcFields && calcFields.length) && (detailCalFields[dataIndex] = calcFields);
                }
            }
        });
        return detailCalFields;
    }

    getDetailCalcFieldsByAddNew(addNewDatas) {
        let detailCalFields = {};
        let objApiName = this.fieldMapping.getFirstDetailObjApiName();
        let fieldMapping = this.fieldMapping.getDetailFields(objApiName);
        let {product_id, bom_id} = fieldMapping;
        addNewDatas && addNewDatas.length && addNewDatas.forEach(it => {
            let {dataIndex, [product_id]: productId, [bom_id]: bomId} = it;
            let calcFields = advancedFormulaCache.batchGetTriggerResultFields([productId, bomId]);
            (calcFields && calcFields.length) && (detailCalFields[dataIndex] = calcFields);
        });
        return detailCalFields;
    }

    mergeCalcFields(source, toSource) {
        source = source || {};
        (!isEmpty(toSource)) && each(toSource, (value, key) => {
            if (value && value.length) {
                let preValue = source[key];
                if (!preValue) {
                    preValue = [];
                    source[key] = preValue;
                }
                preValue.push(...value);
            }
        });
        return source;
    }

    async triggerUIEvent(options, params) {
        if (isEmpty(options) || isEmpty(params)) {
            return;
        }
        let isTriggerUIEvent = false;
        let {triggerType, addNewDataIndexs, changeDataIndex, changeFieldApiName} = params;
        let {masterObjApiName, formApis} = options;
        let objApiName = this.fieldMapping.getFirstDetailObjApiName();
        let isMasterType = [TriggerType.EDIT_MASTER].includes(triggerType);
        let objectApiName = isMasterType ? masterObjApiName : objApiName;
        let param = {objApiName: objectApiName, disableCal: true};
        if (triggerType === TriggerType.ADD) {
            isTriggerUIEvent = true;
            Object.assign(param, {newDataIndexs: addNewDataIndexs});//新增明细UI事件
        } else if ([TriggerType.EDIT_MASTER, TriggerType.EDIT_DETAIL].includes(triggerType)) {
            isTriggerUIEvent = true;
            let isModifyDetail = [TriggerType.EDIT_DETAIL].includes(triggerType)
            Object.assign(param, {//字段变更UI事件
                changeFields: [changeFieldApiName],
                triggerUiField: changeFieldApiName,
                uiChangedFields: [changeFieldApiName]
            }, isModifyDetail && {
                modifiedDataIndexs: [changeDataIndex],
            });
        }
        if (isTriggerUIEvent) {
            return formApis.triggerCalAndUIEvent(param);
        }
    }
}