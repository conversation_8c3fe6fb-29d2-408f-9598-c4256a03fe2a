import {QuoterCalculate} from "./QuoterCalculate";

export class MdBatchAdd {

    constructor(context) {
        let {fieldMapping, pluginApi, requestApi} = context || {};
        this.fieldMapping = fieldMapping;
        this.pluginApi = pluginApi;
        this.requestApi = requestApi;
        this.quoterCalculate = new QuoterCalculate(context);
    }

    mdBatchAddAfter(pluginExecResult, options) {
        let {objApiName, dataGetter, dataUpdater, addOpt} = options;
        if (addOpt && addOpt.isQuoterGenerateDetails) {
            let detailDataList = dataGetter.getDetail(objApiName);
            let dataIndexList = detailDataList && detailDataList.length && detailDataList.map(it => it.dataIndex) || [];
            dataUpdater.del(objApiName, dataIndexList);
        }
    }

    async mdBatchAddEnd(pluginExecResult, options) {
        await this.quoterCalculate.mdBatchAddEnd(options);
    }

    async mdCloneEnd(pluginExecResult, options) {
        await this.quoterCalculate.mdBatchAddEnd(options);
    }

    async mdDelEnd(pluginExecResult, options) {
        await this.quoterCalculate.mdDelEnd(options);
    }
}