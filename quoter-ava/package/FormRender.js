import {QuoterPropBuilder} from "./QuoterPropBuilder";
import {QuoterCalculate} from "./QuoterCalculate";
import {i18n, isEmpty} from "../../pluginbase-ava/package/pluginutils";
import fsapi from "fs-hera-api";

export class FormRender {

    constructor(context) {
        let {fieldMapping, pluginApi, pluginDescribe, requestApi} = context;
        this.fieldMapping = fieldMapping;
        this.pluginApi = pluginApi;
        this.pluginDescribe = pluginDescribe;
        this.requestApi = requestApi;
        this.quoterPropBuilder = new QuoterPropBuilder(context);
        this.quoterCalculate = new QuoterCalculate(context);
    }

    formRenderBefore(pluginExecResult, options) {
        let preResult = pluginExecResult && pluginExecResult.preData;
        let {quoter_json} = this.fieldMapping.getMasterFields();
        return Object.assign({}, preResult, {
            master_field_components: {
                [quoter_json]: {
                    resource: "objformplugin-quoter/package/quoter",
                    prop: {
                        pluginInfo: this.pluginDescribe,
                    }
                }
            }
        });
    }

    async formRenderEnd(pluginExecResult, options) {
        let {dataGetter, dataUpdater} = options;
        let masterData = dataGetter.getMasterData();
        let {attribute_constraint_id, quoter_json, quoter_label} = this.fieldMapping.getMasterFields();
        let {[attribute_constraint_id]: attributeConstraintId, [quoter_json]: quoterJson} = masterData || {};
        let quoterJsonObj = isEmpty(quoterJson) ? null : JSON.parse(quoterJson);
        let {selectedData, version} = quoterJsonObj || {};
        let quoterData = await this.quoterPropBuilder.queryAttributeConstraintData(attributeConstraintId, version, dataGetter);
        let changed = quoterData && quoterData.changed;
        if (changed) {
            fsapi.util.showToast(i18n('ava.object_form.quoter.constraint_changed_tip')/*该约束规则已更新, 历史配置数据已清空！*/);
        }
        let updateData = Object.assign({[quoter_json]: JSON.stringify(Object.assign(quoterData, (!changed) && {selectedData}))}, changed && {[quoter_label]: null});
        dataUpdater.updateMaster(updateData);
        await this.quoterCalculate.formRenderEnd(pluginExecResult, options);
    }

    formSubmitBefore(pluginExecResult, options) {
        let {object_data, dataUpdater} = options || {};
        let quoterValueChanged = object_data && object_data['quoter_value_changed'];
        return quoterValueChanged ? this.pluginApi.confirmPromise(null, i18n('ava.object_form.quoter.constraint_changed_submit_tip')/*规则有改动,是否继续提交？*/)
            .then(confirm => {
                dataUpdater.updateMaster({'quoter_value_changed': false});
                return {consumed: !confirm}
            }) : undefined;
    }
}