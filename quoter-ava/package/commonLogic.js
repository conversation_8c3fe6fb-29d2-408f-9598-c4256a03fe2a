import advancedFormulaCache from "./advancedFormulaCache";

class CommonLogic {

    setFieldReadonly(dataUpdater, detailDataList, fieldMapping) {
        let {product_id, bom_id} = fieldMapping;
        detailDataList && detailDataList.length && detailDataList.forEach(it => {
            let {[product_id]: productId, [bom_id]: bomId, object_describe_api_name} = it;
            let triggerResultFields = advancedFormulaCache.batchGetTriggerResultFields([productId, bomId]);
            if (triggerResultFields && triggerResultFields.length) {
                dataUpdater && dataUpdater.setReadOnly({
                    objApiName: object_describe_api_name,
                    dataIndex: it.dataIndex,
                    fieldName: triggerResultFields,
                    biz: 'quoter',
                    priority: 11
                });
            }
        });
    }
}

export default new CommonLogic();