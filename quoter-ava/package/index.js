import log from "../../pluginbase-ava/package/log";
import BizStateConfig from "../../pluginbase-ava/package/BizStateConfig";
import PluginApi from "../../pluginbase-ava/package/PluginApi";
import FieldMapping from "../../pluginbase-ava/package/FieldMapping";
import {FormRender} from "./FormRender";
import {MasterFieldEdit} from "./MasterFieldEdit";
import {DetailFieldEdit} from "./DetailFieldEdit";
import {GenerateDetails} from "./GenerateDetails";
import {MdBatchAdd} from './MdBatchAdd';
import {QueryBomPrice} from './QueryBomPrice'
import QuoterApi from "./QuoterApi";
import {defFieldMapping} from "./utils";
import {isEmpty} from "../../pluginbase-ava/package/pluginutils";

export default class Quoter {

    constructor(pluginService, pluginParam) {
        let {bizStateConfig, params, describe} = pluginParam || {};
        log.tickPluginUsed(describe);
        this.bizStateConfig = new BizStateConfig(bizStateConfig, pluginService.api.getPlugins());
        let context = {
            fieldMapping: new FieldMapping(params, defFieldMapping),
            bizStateConfig: this.bizStateConfig,
            pluginApi: new PluginApi(pluginService),
            requestApi: new QuoterApi(pluginService.api.request),
            pluginDescribe: describe
        }
        this.formRender = new FormRender(context);
        this.masterFieldEdit = new MasterFieldEdit(context);
        this.detailFieldEdit = new DetailFieldEdit(context);
        this.generateDetails = new GenerateDetails(context);
        this.mdBatchAdd = new MdBatchAdd(context);
        this.queryBomPrice = new QueryBomPrice(context);
    }

    formRenderBefore(pluginExecResult, options) {
        return this.formRender.formRenderBefore(pluginExecResult, options);
    }

    formRenderEnd(pluginExecResult, options) {
        return this.formRender.formRenderEnd(pluginExecResult, options);
    }

    formSubmitBefore(pluginExecResult, options) {
        return this.formRender.formSubmitBefore(pluginExecResult, options);
    }

    fieldEditAfter(pluginExecResult, options) {
        let {masterObjApiName, objApiName} = options;
        if (objApiName === masterObjApiName) {
            return this.masterFieldEdit.fieldEditAfter(pluginExecResult, options);
        }
    }

    fieldEditEnd(pluginExecResult, options) {
        let {masterObjApiName, objApiName} = options;
        if (objApiName === masterObjApiName) {
            return this.masterFieldEdit.fieldEditEnd(pluginExecResult, options);
        } else {
            return this.detailFieldEdit.fieldEditEnd(pluginExecResult, options);
        }
    }

    mdBatchAddAfter(pluginExecResult, options) {
        return this.mdBatchAdd.mdBatchAddAfter(pluginExecResult, options);
    }

    mdBatchAddEnd(pluginExecResult, options) {
        return this.mdBatchAdd.mdBatchAddEnd(pluginExecResult, options);
    }

    mdCloneEnd(pluginExecResult, options) {
        return this.mdBatchAdd.mdCloneEnd(pluginExecResult, options);
    }

    mdDelEnd(pluginExecResult, options) {
        return this.mdBatchAdd.mdDelEnd(pluginExecResult, options);
    }

    generateDetailsBefore(pluginExecResult, options) {
        return this.generateDetails.generateDetails(pluginExecResult, options);
    }

    bomQueryBomPriceParseParamsBeforeSync(pluginExecResult, options) {
        return this.queryBomPrice.bomQueryBomPriceParseParamsBeforeSync(pluginExecResult, options);
    }

    bomReconfigurationEnd(pluginExecResult, options) {
        return this.queryBomPrice.bomReconfigurationEnd(pluginExecResult, options)
    }

    updateForcePriority(pluginExecResult, options) {
        let {forcePriority} = options || {};
        if (!isEmpty(forcePriority)) {
            let result = forcePriority ? 1 : 0;
            this.bizStateConfig && this.bizStateConfig.updateBizStateConfig('enforce_priority', result);
        }
    }

    apply() {
        return [
            {
                event: "form.render.before",
                functional: this.formRenderBefore.bind(this)
            },
            {
                event: 'form.render.end',
                functional: this.formRenderEnd.bind(this)
            },
            {
                event: 'form.submit.before',
                functional: this.formSubmitBefore.bind(this)
            },
            {
                event: 'field.edit.after',
                functional: this.fieldEditAfter.bind(this)
            },
            {
                event: 'field.edit.end',
                functional: this.fieldEditEnd.bind(this)
            },
            {
                event: "md.batchAdd.after",
                functional: this.mdBatchAddAfter.bind(this)
            },
            {
                event: "md.batchAdd.end",
                functional: this.mdBatchAddEnd.bind(this)
            },
            {
                event: "md.clone.end",
                functional: this.mdCloneEnd.bind(this)
            },
            {
                event: "md.del.end",
                functional: this.mdDelEnd.bind(this)
            },
            {
                event: 'quoter.generateDetails.before',
                functional: this.generateDetailsBefore.bind(this)
            },
            {
                event: 'bom.queryBomPrice.parseParams.before',
                functional: this.bomQueryBomPriceParseParamsBeforeSync.bind(this)
            },
            {
                event: 'bom.reconfiguration.end',
                functional: this.bomReconfigurationEnd.bind(this)
            },
            {
                event: "dht.priceBookPriority.update.before",
                functional: this.updateForcePriority.bind(this)
            }
        ]
    }
}
