const info = {
    'ava.object_form.quoter.must_attribute_change_tip': '该属性为必选，不可更改',
    'ava.object_form.quoter.single_must_attribute_change_tip': '该属性为单选且必选，不可更改',
    "ava.object_form.pleaseinput":"请输入",
    'ava.object_form.multi_select': '(多选)',
    'ava.object_form.quoter.selected_attribute_constraint_first': '请先选择属性级联约束',
    'ava.object_form.pleaseselect': '请选择',
    'ava.object_form.please_fill_in': '请填写',
    'ava.object_form.quoter.must_attribute_not_filled': '有必填的属性未填写',
    'ava.object_form.quoter.generate_details': '生成明细',
    'ava.object_form.quoter.constraint_changed_tip': '该约束规则已更新, 历史配置数据已清空！',
    'ava.object_form.quoter.constraint_changed_submit_tip': '规则有改动,是否继续提交？'
}

//固定格式，fssub依赖 -start
module.exports = {
    info
}
//固定格式，fssub依赖 -end