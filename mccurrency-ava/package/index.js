import FieldMapping from "../../pluginbase-ava/package/FieldMapping";
import PluginApi from "../../pluginbase-ava/package/PluginApi";
import {isEmpty, request} from "../../pluginbase-ava/package/pluginutils";
import log from "../../pluginbase-ava/package/log";
import BizStateConfig from "../../pluginbase-ava/package/BizStateConfig";
import perfTick from "../../objformmain/libs/perfTick";

export default class McCurrency {

    constructor(pluginService, pluginParam) {
        let {params, describe, bizStateConfig} = pluginParam || {};
        log.tickPluginUsed(describe);
        this.fieldMapping = new FieldMapping(params, {
            masterFields: {
                form_mc_currency: 'mc_currency',//币种
            },
            detailFields: {
                mc_currency: 'mc_currency',//币种
            }
        });
        this.request = pluginService.api.request;
        this.pluginApi = new PluginApi(pluginService);
        this.bizStateConfig = new BizStateConfig(bizStateConfig, pluginService.api.getPlugins());
    }

    async formFetchDescribeLayoutBefore(pluginExecResult, options) {
        let {dataGetter, masterObjApiName} = options || {};
        let pageId = dataGetter.getPageId();
        if (!masterObjApiName) {
            let masterData = dataGetter.getMasterData();
            masterObjApiName = masterData && masterData.object_describe_api_name || 'UnknownObj';
        }
        let preResult = pluginExecResult && pluginExecResult.preData;
        let prePromiseAllWithFetch = preResult && preResult.promiseAllWithFetch;
        return Object.assign({}, preResult, {
            promiseAllWithFetch: () => {
                let promiseList = [];
                if (prePromiseAllWithFetch) {
                    promiseList.push(Promise.resolve(prePromiseAllWithFetch()));
                }
                let requestPromise = this.requestMultiCurrencyStatus(pageId, masterObjApiName);
                promiseList.push(requestPromise);
                return Promise.all(promiseList);
            }
        })
    }

    fieldEditEnd(pluginExecResult, options) {
        let {masterObjApiName, fieldName, changeData, dataGetter, formApis} = options;
        let {form_mc_currency} = this.fieldMapping.getMasterFields();
        if (fieldName === form_mc_currency) {
            if (isEmpty(changeData)) {
                return;
            }
            let mcCurrencyChanged = !(typeof changeData[fieldName] === 'undefined');
            let objApiName = this.fieldMapping.getFirstDetailObjApiName();
            let detailDataList = dataGetter && dataGetter.getDetail(objApiName);
            if (mcCurrencyChanged && detailDataList && detailDataList.length) {//有从对象数据走取价
                return this.pluginApi.runPlugin('price-service.triggerBatchCalc', Object.assign({}, options, {
                    triggerType: 'masterFieldEdit'
                })).then(() => {
                    //取价后触发一次UI事件
                    return formApis.triggerCalAndUIEvent({
                        objApiName: masterObjApiName,
                        disableCal: true,//不触发计算
                        changeFields: [fieldName],
                        triggerUiField: fieldName,
                        uiChangedFields: [fieldName],
                    })
                });
            }
        }
    }

    mdBatchAddSelectSkuConfig(pluginExecResult, options) {
        let mcCurrency = this.getMcCurrency(options);
        let preData = pluginExecResult && pluginExecResult.preData || {};
        return Object.assign({}, preData, {
            selectedMcCurrency: mcCurrency,//选择的币种
        });
    }

    priceServiceBatchAddIsTriggerGetPrice(pluginExecResult, options) {
        let preData = pluginExecResult && pluginExecResult.preData || {};
        let openMultiCurrency = this.bizStateConfig.isOpenMultiCurrency();
        return Object.assign({}, preData, openMultiCurrency && {
            mccurrency: true,//开启多币种，选sku回来后需要触发取价
        });
    }

    priceServiceGetPriceParam(pluginExecResult, options) {
        let mcCurrency = this.getMcCurrency(options);
        let {params} = options;
        Object.assign(params, {
            mcCurrency
        });
    }

    multiUnitCalcPriceBeforeSync(pluginExecResult, options) {
        let mcCurrency = this.getMcCurrency(options);
        let {param} = options;
        Object.assign(param, {
            mcCurrency
        });
    }

    isOpenMultiCurrency(options) {
        return this.bizStateConfig.isOpenMultiCurrency();
    }

    getMcCurrency(options) {
        let {form_mc_currency} = this.fieldMapping.getMasterFields();
        let masterData = options && options.dataGetter && options.dataGetter.getMasterData && options.dataGetter.getMasterData();
        let {[form_mc_currency]: mc_currency} = masterData || {};
        return mc_currency;
    }

    requestMultiCurrencyStatus(pageId, masterObjApiName) {
        let ticker = perfTick.startTicker({operationId: 'requestMultiCurrencyStatus'});
        return request(this.request, {
            url: `FHE/EM1ANCRM/API/v1/object/currency/service/multi_currency_status`,
            data: {},
            cacheRule: {type: "app"}
        }).then(res => {
            let status = res ? res.status : 0;
            this.bizStateConfig.updateBizStateConfig('mccurrency', status);
            ticker.end({
                pageId,
                apiName: masterObjApiName,
                biz: "ava_object_form",
                module: 'sfa',
                subModule: "mccurrency",
            });
        }).catch(err => {
            this.pluginApi.showToast(err);
        })
    }

    apply() {
        return [{
            event: "form.fetchDescribeLayout.before",
            functional: this.formFetchDescribeLayoutBefore.bind(this)
        }, {
            event: "field.edit.end",
            functional: this.fieldEditEnd.bind(this)
        }, {
            event: "price-service.batchSelectSkuConfig.sync",
            functional: this.mdBatchAddSelectSkuConfig.bind(this)
        }, {
            event: "price-service.batchAdd.isTriggerGetPrice.sync",
            functional: this.priceServiceBatchAddIsTriggerGetPrice.bind(this)
        }, {
            event: "price-service.batchAdd.getPriceParam.sync",
            functional: this.priceServiceGetPriceParam.bind(this)
        }, {
            event: "price-service.form.getPriceParam.sync",
            functional: this.priceServiceGetPriceParam.bind(this)
        }, {
            event: "multi-unit.calcPrice.before.sync",
            functional: this.multiUnitCalcPriceBeforeSync.bind(this)
        }, {
            event: "mccurrency.isOpenMultiCurrency.sync",
            functional: this.isOpenMultiCurrency.bind(this)
        }];
    }
}