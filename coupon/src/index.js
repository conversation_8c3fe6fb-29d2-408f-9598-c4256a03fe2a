import PPM from "plugin_public_methods";
import Base from "plugin_base";
import CouponImp from "./package/coupon.ts";

export default class Coupon extends Base {
   
    constructor(pluginService, pluginParam) {
        super(...arguments);
        this.couponPolicy=null;
    }
    getFields(param) {
		return {
			[this.masterApi]: param.dataGetter.getDescribeLayout().objectDescribe.fields,
			[this.detailApi]: param.dataGetter.getDescribe().fields
		}
	}
    //
    init(param) {
        this.masterApi = param.masterObjApiName;
        this.detailApi = param.objApiName;
        const masterData = param.dataGetter.getMasterData(), 
            fields = this.getFields(param),
            fieldMap = {
                isGift: "is_giveaway",
                accountId: "account_id",
                parentGiftKey: "parent_gift_key"
            },
            decimalMap = {
                quantity: 2,
                gift_amortize_price: 2,
                product_price: 2,
                price_book_price: 2,
                policy_dynamic_amount: 2
            };
        this.couponPolicy = new CouponImp(
            masterData.requestId,
            this.masterApi,
            this.detailApi,
            param.formType,
            fields,
            fieldMap,
            decimalMap,
            this.request,
            param.triggerCal,
        )
    }
    
    //查询可用优惠券
    async queryCoupon(param) {
        const couponData= await this.couponPolicy.queryCoupon({
                masterData: param.dataGetter.getMasterData(),
                detailDataMap:this.getDetailMap(param),
                policyInfo:{},
                changeInfo:{},
                modifyInfo:{}
            });
        return couponData;
    }
    //使用优惠券
    async useCoupon(userTrigger,coupons,param) {


        const args= this.getExecuteArgs(param);
        let useRes=null;
        if(rebates.rebateData.length <= 0 && rebates.productRebateData.length <= 0){
            useRes=await this.couponPolicy.cancelCoupon(args);
        }else{
            useRes=await this.couponPolicy.useCoupon(userTrigger,coupons,args);
        }
        
        this.updateData(param,useRes&&useRes.changeInfo);
        return useRes;
    }

    async checkAmortizeChange(userTrigger,param){
        const me=this,
            args= this.getExecuteArgs(param),
            matchRes=await this.couponPolicy.useCoupon(userTrigger,[],args);
        this.updateData(param,matchRes&&matchRes.changeInfo);
        return matchRes.policyInfo;
    }

    getExecuteArgs(param){
        return {
            masterData: param.dataGetter.getMasterData(),
            detailDataMap:this.getDetailMap(param),
            policyInfo:{},
            changeInfo:{},
            modifyInfo:{}
        }
    }
    //过滤价格政策赠品&返利产品
    getDetailMap(param) {
        const isPolicyGift = (data) => {
            return (data.is_giveaway == '1') && (data.parent_gift_key || data.rebate_coupon_object_api_name === 'RebateObj')
        };
        return (param.dataGetter.getDetail(this.detailApi) || [])
            .reduce((dataMap, d) => {
                if (!isPolicyGift(d)) {
                    d.children && delete d.children;
                    dataMap[d.rowId] = d;
                }
                return dataMap;
            }, {});
    }

    updateData(param,result={}){
        const { mdUpdate, masterUpdate } = result;

        //更新主对象数据
		param.dataUpdater.updateMaster(masterUpdate);
		//更新的明细数据
		Object.keys(mdUpdate).forEach(key => {
			param.dataUpdater.updateDetail(this.detailApi, key, mdUpdate[key])
		})
    }
}
