export interface FieldMapFace{

}

export interface DecimalMapFace{

}
export interface ExecuteFace {
    masterData: any;
    detailDataMap:{
        [key:string]:any;
    };
    policyInfo:any;
    changeInfo:changeInfoFace;
    modifyInfo:modifyInfoFace;
}

export interface changeInfoFace {
	masterUpdate: any;
	mdUpdate: any;
	mdAdd: Array<any>;
	mdDel: Array<any>;
	[props: string]: any;
}
export interface modifyInfoFace {
	modifyFields: {
		[objApi: string]: Array<string>;
	};
	modifyIndex: Array<string>;
}

//返利优惠券条件字段查询接口返回结果
export interface ConditionFieldsResFace {
    couponConditionField: {
        [apiname: string]: Array<string>
    };
    rebateConditionField: {
        [apiname: string]: Array<string>
    };
    rebateChange: boolean;
    couponChange: boolean;
}

//优惠券接口
export interface CouponFace {
    id: string;
    name: string;
    amount: string;
    lower_limit: string;
    desc: string;
    start_date: string;
    end_date: string;
}


export interface MatchAmortizeFace {
    masterData: object;
    detailDataMap: {
        [index: string]: object;
    };
    couponDatas: Array<{
        id: string;
        amount: string;
    }>;
    change: boolean;
    edit: boolean;
    requestId: string;
}

export interface MatchResFace {
    masterData: object;
    detailDataMap: {
        [prod_pkg_key: string]: object
    };
    couponChange:boolean;
    couponDatas:Array<any>;
    couponCOnditionField:{
        [api:string]:Array<string>;
    };
    [props:string]:any;
}