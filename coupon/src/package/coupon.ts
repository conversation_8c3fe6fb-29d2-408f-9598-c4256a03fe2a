import {
    FieldMapFace,
    DecimalMapFace,
    ExecuteFace,
    ConditionFieldsResFace,
    CouponFace,
    MatchResFace
} from './data_interface';
import PPM from 'plugin_public_methods';
export default class CouponImp {
    constructor(
        public requestId: string,
        public masterApiName: string,
        public detailApiName: string,
        public fromType: string,
        public fields: {
            [fieldName: string]: any
        },
        public fieldMap: FieldMapFace,
        public decimalMap: DecimalMapFace,
        public request: any,
        public triggerCal: any,
    ) { }
    /*****************************************************************************/
    /***********************************业务逻辑***********************************/
    /*****************************************************************************/
    //查询当前条件字段
    public async queryConditionFields(param: ExecuteFace): Promise<ConditionFieldsResFace> {
        const conditionRes = await this.reqCondition(param.masterData._id);
        return conditionRes;
    }
    //查询政策
    public async queryCoupon(param: ExecuteFace): Promise<Array<CouponFace>> {
        const coupons = PPM.composeAsync(
            PPM.curry(2, this.checkUsingPolicy)(param.masterData),
            this.reqCoupon.bind(this)
        )(param)
        return coupons;
    }
    //使用优惠券
    public async useCoupon(
        editTriggerCoupon: boolean,
        couponData: Array<any>,
        param: ExecuteFace
    ) {
        const me = this,
            data = await PPM.composeAsync(
                this.calBatch.bind(this),
                PPM.curry(2,this.updateMatchAmortizeRes.bind(this))(param),
                PPM.partial(this.reqMatchCoupon.bind(this), editTriggerCoupon, couponData)
            )(param);
        return data;
    }
    //取消使用优惠券优惠
    public async cancelCoupon() {

    }
    /*****************************************************************************/
    /***********************************实现方法***********************************/
    /*****************************************************************************/
    //查询条件字段接口请求
    private reqCondition(dataId: string) {
        const args: any = {
            requestId: this.requestId,
            masterObjectApiName: this.masterApiName,
            detailObjectApiName: this.detailApiName,
            dataId: dataId,
        },
            url = "FHH/EM1HNCRM/API/v1/object/rebate/service/getConditionFields";
        return PPM.ajax(this.request, url, args);
    }
    //查询优惠券接口请求
    private reqCoupon(param: ExecuteFace) {
        const args: any = {
            requestId: this.requestId,
            masterObjectApiName: this.masterApiName,
            detailObjectApiName: this.detailApiName,
            edit: this.fromType == 'edit',
            accountId: param.masterData.account_id,
            masterData: param.masterData,
            detailDataMap: param.detailDataMap
        },
            url = "FHH/EM1HNCRM/API/v1/object/coupon/service/query";
        return PPM.ajax(this.request, url, args, 'datas');
    }
    //检查使用中的优惠券返利单
    private checkUsingPolicy(masterData: any, data: Array<any>) {
        const miscContent = (masterData.misc_content || {}).coupon || [];
        data.forEach((d: any) => {
            let useItem = miscContent.find((c: any) => c.id == d.id);
            d.using = useItem ? true : false;
            if (useItem) {
                d.amount = useItem.amount;
            }
        });
        return data;
    }

    //优惠券分摊接口请求
    private reqMatchCoupon(
        editTriggerCoupon: boolean,
        couponData: Array<any>,
        param: ExecuteFace) {
        const args: any = {
            requestId: this.requestId,
            masterObjectApiName: this.masterApiName,
            detailObjectApiName: this.detailApiName,
            edit: this.fromType == 'edit',
            change: editTriggerCoupon,
            masterData: param.masterData,
            detailDataMap: param.detailDataMap,
            couponDatas: couponData,
        },
            url = "FHH/EM1HNCRM/API/v1/object/rebate/service/matchAmortize";
        return PPM.ajax(this.request, url, args);
    }
    //本地构造取消接口result:misc_content,coupon_amount,coupon_amortize_amount,coupon_dynamic_amount
    generateCancelRes(param: ExecuteFace) {
        let detailsMap: any = {};
        for (let key in param.detailDataMap) {
            let { miscContent } = param.detailDataMap[key];
            detailsMap[key] = {
                coupon_amortize_amount: 0,
                coupon_dynamic_amount: 0,
            }
            if (miscContent) {
                detailsMap[key].misc_content = Object.assign(miscContent, {
                    coupon_amortize: []
                })
            }
        }
        return {
            masterData: {
                coupon_amount: 0,
                misc_content: Object.assign(param.masterData.misc_content || {}, {
                    coupon: []
                })
            },
            detailDataMap: detailsMap,
            policy: {}
        }
    }
    //更新返利优惠券分摊算接口数据
    private updateMatchAmortizeRes(param: ExecuteFace, result: MatchResFace): ExecuteFace {
        if (!result) {
            return param;
        }
        return {
            masterData: Object.assign(param.masterData, result.masterData),
            detailDataMap: PPM.updateObject(param.detailDataMap, result.detailDataMap),
            policyInfo: Object.assign(param.policyInfo || {}, {
                couponChange:result.couponChange,
                couponConditionField:result.couponConditionField
            }),
            changeInfo: PPM.updateChangeInfo(param.changeInfo, result.masterData, result.detailDataMap),
            modifyInfo: PPM.generateModifyInfo(result.masterData, result.detailDataMap, this.masterApiName, this.detailApiName),
        };
    }

    //调用计算接口
    public async calBatch(param: ExecuteFace) {
        const dataFromPolicy = await PPM.composeAsync(
            PPM.curry(2, this.updateCalRes.bind(this))(param),
            this.triggerCal.bind(this),
            this.parseCalArgs.bind(this)
        )(param);
        return dataFromPolicy;
    }

    //格式化计算接口入参
    private parseCalArgs(param: ExecuteFace) {
        return {
            noMerge: true,
            noLoading: true,
            noRetry: true,
            changeFields: [],
            filterFields: {},
            extraFields: PPM.collectCalFields(this.fields, param.modifyInfo.modifyFields),
            operateType: "edit",
            dataIndex: param.modifyInfo.modifyIndex,
            objApiName: this.masterApiName,
            masterData: param.masterData,
            details: {
                [this.detailApiName]: Object.keys(param.detailDataMap || {})
                    .map((key: string) => param.detailDataMap[key])
            }
        }
    }
    private updateCalRes(param: ExecuteFace, result: any): ExecuteFace {
        if (!result) {
            return param;
        }
        const calRes = result.Value.calculateResult || {},
            resMaster = calRes[this.masterApiName][0] || {},
            resDetail = calRes[this.detailApiName] || {};
        return {
            masterData: Object.assign(param.masterData, resMaster),
            detailDataMap: PPM.updateObject(param.detailDataMap, resDetail),
            changeInfo: PPM.updateChangeInfo(param.changeInfo, resMaster, resDetail),
            modifyInfo: PPM.generateModifyInfo(resMaster, resDetail, this.masterApiName, this.detailApiName),
            policyInfo: param.policyInfo,
        }
    }
}