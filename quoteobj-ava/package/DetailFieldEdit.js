import {requireAsync} from '../../pluginbase-ava/package/pluginutils';
import {BackCalculation} from "./BackCalculation";

export class DetailFieldEdit {

    constructor(context) {
        this.context = context;
        this.discountBackCalculation = null;
        this.extraDiscountBackCalculation = new BackCalculation(context);
    }

    async detailFieldEditAfter(pluginExecResult, options) {
        let {fieldName} = options;
        if (fieldName === 'sales_price') {
            let discountBackCalculation = await this.getBackCalculation();
            return discountBackCalculation && discountBackCalculation.salesPriceEditAfter(options);
        } else if (fieldName === 'total_amount') {
            let discountBackCalculation = await this.getBackCalculation();
            return discountBackCalculation && discountBackCalculation.subtotalEditAfter(options);
        } else if (fieldName === 'selling_price') {
            return this.extraDiscountBackCalculation.sellingPriceEditAfter(options);
        } else if (fieldName === 'sales_amount') {
            return this.extraDiscountBackCalculation.salesAmountEditAfter(options);
        } else if (fieldName === 'extra_discount_amount') {
            return this.extraDiscountBackCalculation.extraDiscountAmountEditAfter(options);
        } else if (fieldName === 'total_discount') {
            return this.extraDiscountBackCalculation.totalDiscountEditAfter(options);
        }
    }

    async getBackCalculation() {
        let discountBackCalculation = this.discountBackCalculation;
        if (discountBackCalculation) {
            return discountBackCalculation;
        }
        let result = await requireAsync('../../objformplugin-salesorder/package/BackCalculation');
        discountBackCalculation = new result.BackCalculation(this.context);
        this.discountBackCalculation = discountBackCalculation;
        return discountBackCalculation;
    }
}