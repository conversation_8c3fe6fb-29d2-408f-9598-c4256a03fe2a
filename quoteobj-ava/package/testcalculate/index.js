import dialogSet from "../../../objformmain/dialogset/index";
import fsapi from "fs-hera-api";
import {i18n, isEmpty,} from "../../../pluginbase-ava/package/pluginutils";
import BizStateConfig from "../../../pluginbase-ava/package/BizStateConfig";

const BY_DISCOUNT = 'by_discount';
const BY_TARGET_PRICE = 'by_target_price';

Component({

    properties: {},

    data: {
        dIsTestCalculate: false,
        dCalculateByDiscount: undefined,
        dCalculateByTargetPrice: undefined,
        inputValue: undefined,//报价单试算输入的内容
    },

    methods: {
        calculateByDiscount() {
            let title = i18n('ava.object_form.quote.calc_by_discount')/*按折扣试算*/;
            this.showInputDialog(title, BY_DISCOUNT, rst => {
                this.runTestCalculateAfterEvent(BY_DISCOUNT, rst && rst.value)
            });
        },

        calculateByTargetPrice() {
            let title = i18n('ava.object_form.quote.calc_by_target_price')/*按目标报价试算*/;
            this.showInputDialog(title, BY_TARGET_PRICE, rst => {
                this.runTestCalculateAfterEvent(BY_TARGET_PRICE, rst && rst.value)
            });
        },

        showInputDialog(title, type, callback) {
            let self = this;
            dialogSet.confirm.show({
                title,
                type: 'prompt',
                cancelBtn: {
                    onClick() {
                    }
                },
                confirmBtn: {
                    onClick(rst) {
                        callback && callback(rst);
                    }
                },
                handleInput: (event) => {
                    let inputValue = event.detail.value;
                    if (inputValue === undefined || inputValue === null || inputValue === '') {
                        event.detail.value = '';
                        return;
                    }
                    let inputNumber = fsapi.format.field_formatNumber(inputValue);
                    if (inputNumber === undefined || inputNumber === null || inputNumber === '') {
                        event.detail.value = '';
                        return;
                    }
                    let value;
                    if (type === BY_DISCOUNT) {
                        let splitValue = self.splitValue(inputNumber);
                        let {intValue = '', decimalValue = ''} = splitValue || {};
                        let inputLength = intValue.length + decimalValue.length;
                        if (inputLength > 14) {
                            value = self.data.inputValue;
                            event.detail.cursor = event.detail.cursor - 1;
                        } else {
                            value = inputNumber + '%';
                        }
                    } else if (type === BY_TARGET_PRICE) {
                        let objectDescribe = self.formContext && self.formContext.getDetailDescribe('QuoteLinesObj');
                        let salesAmountField = objectDescribe && objectDescribe.fields && objectDescribe.fields['sales_amount'];
                        let {length = 14, decimal_places = 0} = salesAmountField || {};
                        let endWithPoint = inputNumber.endsWith(".");
                        let splitValue = self.splitValue(inputNumber);
                        let {intValue = '', decimalValue = ''} = splitValue || {};
                        if (decimalValue.length > decimal_places) {
                            let _splitValue = self.splitValue(self.data.inputValue);
                            decimalValue = _splitValue.decimalValue;
                        }
                        let inputLength = intValue.length + decimalValue.length;
                        if (inputLength > length) {
                            value = self.data.inputValue;
                            event.detail.cursor = event.detail.cursor - 1;
                        } else {
                            if (decimalValue) {
                                value = `${intValue}.${decimalValue}`;
                            } else {
                                value = intValue;
                            }
                            // value = fsapi.format.toMoney(value);//先不进行金额格式化，光标位置会有问题
                            if (endWithPoint) {
                                value = value + '.';
                            }
                        }
                    } else {
                        value = inputNumber;
                    }
                    event.detail.value = value;
                    self.data.inputValue = value;
                }
            })
        },

        runTestCalculateAfterEvent(type, value) {
            if (isEmpty(value)) {
                return;
            }
            value = fsapi.format.field_formatNumber(value);
            if (isEmpty(value)) {
                return;
            }
            let formContext = this.formContext;
            let inputValue = parseFloat(value);
            formContext && formContext.catchRunPluginHook("QuoteObj.testCalculate.after", {
                type: type,
                inputValue: inputValue,
            });
        },

        splitValue(value) {
            let intValue = '';
            let decimalValue = '';
            let temp = value ? fsapi.format.field_formatNumber(value) : value;
            if (temp !== undefined && temp !== null && temp !== '') {
                const _arr = `${temp}`.split('.');
                if (_arr.length > 1) {
                    intValue = _arr[0];
                    decimalValue = _arr[1];
                } else {
                    intValue = _arr[0];
                }
            }
            return {
                intValue,
                decimalValue
            };
        },

        getBizStateConfig() {
            let bizStateConfig = this.bizStateConfig;
            if (bizStateConfig == null) {
                let formContext = this.formContext;
                let pluginService = formContext && formContext.getPluginService && formContext.getPluginService();
                bizStateConfig = new BizStateConfig(pluginService && pluginService.bizStateConfig, pluginService.api.getPlugins());
                this.bizStateConfig = bizStateConfig;
            }
            return bizStateConfig;
        },
    },

    lifetimes: {
        attached() {
            let detail = {};
            this.triggerEvent("getDataDetail", detail)
            this.formContext = detail.dConfig.formContext;
            let bizStateConfig = this.getBizStateConfig();
            let isTestCalculate = bizStateConfig.isOpenTestCalculate();
            let openPricePolicy = bizStateConfig.isOpenPricePolicy();
            this.setData({
                dIsTestCalculate: isTestCalculate && !openPricePolicy,
                dCalculateByDiscount: i18n('ava.object_form.quote.calc_by_discount')/*按折扣试算*/,
                dCalculateByTargetPrice: i18n('ava.object_form.quote.calc_by_target_price')/*按目标报价试算*/
            })
        }
    }
})