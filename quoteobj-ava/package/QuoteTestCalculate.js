import {
    divide,
    formatValueDecimalPlaces,
    getPercentileFieldDecimalPlacesFromDescribe,
    i18n,
    multiply
} from "../../pluginbase-ava/package/pluginutils";

export class QuoteTestCalculate {

    constructor(context) {
        let {pluginApi} = context || {};
        this.pluginApi = pluginApi;
    }

    async testCalculateAfter(pluginExecResult, options) {
        let {type} = options;
        if (type === 'by_discount') {
            return this.byDiscount(options);
        } else if (type === 'by_target_price') {
            return this.byTargetPrice(options);
        }
    }

    byDiscount(options) {
        let {inputValue, dataGetter, dataUpdater, formApis} = options;
        let detailDataList = dataGetter.getDetail('QuoteLinesObj');
        if (detailDataList && detailDataList.length) {
            let objectDescribe = dataGetter.getDescribe('QuoteLinesObj');
            let decimalPlaces = getPercentileFieldDecimalPlacesFromDescribe('extra_discount', objectDescribe);
            let modifiedIndexList = [];
            let extraDiscount = divide(inputValue, 100);
            detailDataList.forEach(detail => {
                let dataIndex = detail.dataIndex;
                let _extraDiscount = multiply(extraDiscount, 100);
                _extraDiscount = _extraDiscount ? formatValueDecimalPlaces(_extraDiscount, decimalPlaces) : 0;
                let updateObj = {extra_discount: _extraDiscount}
                Object.assign(detail, updateObj);
                dataUpdater && dataUpdater.updateDetail('QuoteLinesObj', dataIndex, updateObj);
                modifiedIndexList.push(dataIndex);
            });
            return formApis.triggerCalAndUIEvent({
                objApiName: 'QuoteLinesObj',
                modifiedDataIndexs: modifiedIndexList,
                changeFields: ['sales_amount', 'extra_discount'],
                filterFields: {QuoteLinesObj: ['extra_discount']}
            })
        }
    }

    byTargetPrice(options) {
        let {inputValue, dataGetter, dataUpdater, formApis} = options;
        let detailDataList = dataGetter.getDetail('QuoteLinesObj');
        if (detailDataList && detailDataList.length) {
            let totalAmountCount = 0;
            detailDataList.forEach(it => {
                let total_amount = it.total_amount;
                totalAmountCount += parseFloat(total_amount);
            });
            let objectDescribe = dataGetter.getDescribe('QuoteLinesObj');
            let salesAmountField = objectDescribe && objectDescribe.fields && objectDescribe.fields['sales_amount'];
            let salesAmountCount = 0;
            let salesAmountDecimalPlaces = salesAmountField && salesAmountField.decimal_places;
            let modifiedIndexList = [];
            let length = detailDataList.length;
            let decimalPlaces = getPercentileFieldDecimalPlacesFromDescribe('extra_discount', objectDescribe);
            detailDataList.forEach((it, index) => {
                let salesAmount;
                if (index === (length - 1)) {
                    salesAmount = inputValue - salesAmountCount;
                } else {
                    let totalAmount = it.total_amount;
                    salesAmount = totalAmountCount == 0 ? 0 : multiply(inputValue, divide(totalAmount, totalAmountCount));
                    salesAmountCount += parseFloat(salesAmount);
                }
                let {sales_price, quantity, dataIndex} = it;
                let total_amount = multiply(sales_price, quantity);
                let extraDiscount = total_amount == 0 ? 1 : divide(salesAmount, total_amount);
                extraDiscount = multiply(extraDiscount, 100);
                let updateObj = {
                    extra_discount: extraDiscount ? formatValueDecimalPlaces(extraDiscount, decimalPlaces) : 0,
                    sales_amount: salesAmount ? formatValueDecimalPlaces(salesAmount, salesAmountDecimalPlaces) : 0
                }
                Object.assign(it, updateObj);
                dataUpdater && dataUpdater.updateDetail('QuoteLinesObj', dataIndex, updateObj);
                modifiedIndexList.push(dataIndex);
            });
            return formApis.triggerCalAndUIEvent({
                objApiName: 'QuoteLinesObj',
                modifiedDataIndexs: modifiedIndexList,
                changeFields: ['sales_amount', 'extra_discount'],
                filterFields: {QuoteLinesObj: ['extra_discount']}
            }).then(() => {
                let masterData = dataGetter.getMasterData();
                let quoteProductSum = masterData && masterData.quote_product_sum;
                let fQuoteProductSum = parseFloat(quoteProductSum);
                if (fQuoteProductSum !== inputValue) {
                    let title = i18n('ava.object_form.quote.test_calc_tip')/*与试算目标报价存在计算偏差*/;
                    this.pluginApi.showToast(title)
                }
            })
        }
    }
}