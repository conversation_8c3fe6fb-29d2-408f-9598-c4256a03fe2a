import {isEmpty} from "../../pluginbase-ava/package/pluginutils";

export const key_select_sku_attribute_value = 'key_select_sku_attribute_value';//存放在sku中的属性、属性值信息
export const key_select_sku_nonstandard_attribute_value = 'key_select_sku_nonstandard_attribute_value';//存放在sku中的非标属性、属性值信息

export function getAttributeNames(attribute) {
    return attribute && attribute.length && attribute.map(attribute => {
        let {attribute_id__r, attribute_value_id__r} = attribute;
        return `${attribute_id__r}:${attribute_value_id__r}`;
    });
}

export function getAttributeObj(attribute) {
    let attributeObj = {};
    attribute && attribute.length && attribute.forEach(attribute => {
        if (attribute) {
            let {attribute_id, attribute_value_id} = attribute;
            attributeObj[attribute_id] = attribute_value_id;
        }
    });
    return attributeObj;
}

export function parseAttributeJson2Obj(attributeJson) {
    if (attributeJson !== undefined && attributeJson !== null) {
        return typeof attributeJson === 'string' ? JSON.parse(attributeJson) : attributeJson;
    }
}

export function attributeJson2AttributeList(attributeJson) {
    let selectedAttributeValues = [];
    let attributeObj = parseAttributeJson2Obj(attributeJson);
    attributeObj && Object.keys(attributeObj).forEach(key => {
        let value = attributeObj[key];
        selectedAttributeValues.push({attribute_id: key, attribute_value_id: value})
    });
    return selectedAttributeValues;
}

export function getNonstandardAttributeNames(attribute) {
    return attribute && attribute.length && attribute.filter(attribute => {
        let {value} = attribute || {};
        return !isEmpty(value);
    }).map(attribute => {
        let {name, value} = attribute;
        return `${name}:${value}`;
    });
}

export function getNonstandardAttributeObj(attribute) {
    let attributeObj = {};
    attribute && attribute.length && attribute.forEach(attribute => {
        if (attribute) {
            let {id, value} = attribute;
            if (!isEmpty(value)) {
                attributeObj[id] = value;
            }
        }
    });
    return attributeObj;
}

export function nonstandardAttributeJson2AttributeList(nonstandardAttributeJson) {
    let nonstandardAttributeValues = [];
    let attributeObj = parseAttributeJson2Obj(nonstandardAttributeJson) || {};
    attributeObj && Object.keys(attributeObj).forEach(key => {
        let value = attributeObj[key];
        nonstandardAttributeValues.push({id: key, value: value})
    });
    return nonstandardAttributeValues;
}