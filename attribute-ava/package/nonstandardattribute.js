import FieldMapping from "../../pluginbase-ava/package/FieldMapping";
import {
    getNonstandardAttributeNames,
    getNonstandardAttributeObj,
    key_select_sku_nonstandard_attribute_value,
    nonstandardAttributeJson2AttributeList
} from "./utils";
import {isEmpty} from "../../pluginbase-ava/package/pluginutils";
import log from "../../pluginbase-ava/package/log";

export default class NonstandardAttribute {

    constructor(pluginService, pluginParam) {
        let {params, describe} = pluginParam || {};
        log.tickPluginUsed(describe);
        this.fieldMapping = new FieldMapping(params, {
            masterFields: {},
            detailFields: {
                product_id: 'product_id',// 产品名称
                price_book_product_id: 'price_book_product_id', // 价目表产品
                nonstandard_attribute: "nonstandard_attribute",//非标属性文本
                nonstandard_attribute_json: "nonstandard_attribute_json",//非标属性、属性值
            }
        })
    }

    mdBatchAddAfter(pluginExecResult, options) {
        let {lookupDatas, lookupField, newDatas, objApiName} = options || {};
        if (!newDatas || !lookupDatas) {
            return;
        }
        let fieldName = lookupField && lookupField.api_name;
        let {product_id, price_book_product_id} = this.fieldMapping.getDetailFields(objApiName);
        if (![product_id, price_book_product_id].includes(fieldName)) {
            return;
        }
        newDatas.forEach((newData, index) => {
            let lookupData = lookupDatas[index];
            let noAttributeBackFill = this.getNoAttributeInfoBySkuData(lookupData, objApiName);
            Object.assign(newData, noAttributeBackFill);
        })
    }

    bomParseBomData2DetailDataSync(pluginExecResult, options) {
        let preData = pluginExecResult && pluginExecResult.preData;
        let {subProduct, objApiName} = options || {};
        let noAttributeInfo = this.getNoAttributeInfoBySkuData(subProduct, objApiName);
        return Object.assign({}, preData, noAttributeInfo);
    }

    bomParseDetailData2BomDataSync(pluginExecResult, options) {
        let preData = pluginExecResult && pluginExecResult.preData;
        let {objectData} = options;
        let {nonstandard_attribute_json} = this.fieldMapping.getDetailFields(objectData.object_describe_api_name);
        let {[nonstandard_attribute_json]: nonstandardAttributeJson} = objectData || {};
        return Object.assign({}, preData, {
            nonstandard_attribute_json: (typeof nonstandardAttributeJson === 'string') ? nonstandardAttributeJson : nonstandardAttributeJson && JSON.stringify(nonstandardAttributeJson),
            nonstandardAttribute: nonstandardAttributeJson2AttributeList(nonstandardAttributeJson),
            selectedNonstandardAttributes: nonstandardAttributeJson2AttributeList(nonstandardAttributeJson)
        })
    }

    getNoAttributeInfoBySkuData(skuData, objApiName) {
        let {nonstandard_attribute, nonstandard_attribute_json} = this.fieldMapping.getDetailFields(objApiName);
        let {[key_select_sku_nonstandard_attribute_value]: nonstandardAttributeValue} = skuData || {};
        let nonstandardAttributeNames = getNonstandardAttributeNames(nonstandardAttributeValue);
        let nonstandardAttributeObj = getNonstandardAttributeObj(nonstandardAttributeValue);
        let nonstandardAttributeInfo = nonstandardAttributeNames && nonstandardAttributeNames.length ? nonstandardAttributeNames.join(';') : undefined;
        // let nonstandardAttributeJson = isEmpty(nonstandardAttributeObj) ? undefined : JSON.stringify(nonstandardAttributeObj);
        let attributeBackFill = {};
        if (nonstandardAttributeInfo) {
            attributeBackFill[nonstandard_attribute] = nonstandardAttributeInfo;
        }
        if (!isEmpty(nonstandardAttributeObj)) {
            attributeBackFill[nonstandard_attribute_json] = nonstandardAttributeObj;
        }
        return attributeBackFill;
    }

    queryAttributeBeforeSync(pluginExecResult, options) {
        let { objApiName, objectData } = options || {};
        let { nonstandard_attribute_json } = this.fieldMapping.getDetailFields(objApiName); 
        let { [nonstandard_attribute_json]: nonstandardAttributeJson } = objectData || {};
        if (!isEmpty(nonstandardAttributeJson)) {
            return {
                nonstandard_attribute_json: nonstandardAttributeJson,
            }
        }
    }

    queryAttributeAfterSync(pluginExecResult, options) {
        let { objApiName, queryResult } = options || {};
        let { nonstandard_attribute: nonstandardAttributeValue } = queryResult || {};
        if (!isEmpty(nonstandardAttributeValue)) {
            let { nonstandard_attribute } = this.fieldMapping.getDetailFields(objApiName);
            return {
                [nonstandard_attribute]: nonstandardAttributeValue,
            }
        }
    }

    apply() {
        return [{
            event: "md.batchAdd.after",
            functional: this.mdBatchAddAfter.bind(this)
        }, {
            event: "bom.parseBomData2DetailData.sync",
            functional: this.bomParseBomData2DetailDataSync.bind(this)
        }, {
            event: "bom.parseDetailData2BomData.sync",
            functional: this.bomParseDetailData2BomDataSync.bind(this)
        }, {
            event: "attribute.queryAttribute.before.sync",
            functional: this.queryAttributeBeforeSync.bind(this)
        }, {
            event: "attribute.queryAttribute.after.sync",
            functional: this.queryAttributeAfterSync.bind(this)
        }];
    }
}