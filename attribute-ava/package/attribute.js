import FieldMapping from "../../pluginbase-ava/package/FieldMapping";
import {
    attributeJson2AttributeList,
    getAttributeNames,
    getAttributeObj,
    key_select_sku_attribute_value,
    parseAttributeJson2Obj
} from "./utils";
import {isEmpty, request} from "../../pluginbase-ava/package/pluginutils";
import log from '../../pluginbase-ava/package/log'
import PluginA<PERSON> from "../../pluginbase-ava/package/PluginApi";

export default class Attribute {

    constructor(pluginService, pluginParam) {
        let {params, describe} = pluginParam || {};
        this.http = pluginService.api.request
        log.tickPluginUsed(describe);
        this.pluginApi = new PluginApi(pluginService);
        this.fieldMapping = new FieldMapping(params, {
            masterFields: {},
            detailFields: {
                product_id: 'product_id',// 产品名称
                price_book_product_id: 'price_book_product_id', // 价目表产品
                attribute: "attribute",//属性文本
                attribute_json: "attribute_json",//属性、属性值id
                attribute_price_book_id: "attribute_price_book_id",//属性价目表
            }
        })
    }

    formRenderEnd(pluginExecResult, options) {
        let {dataUpdater} = options;
        let objApiName = this.fieldMapping.getFirstDetailObjApiName();
        this.setFieldReadonly(objApiName, dataUpdater);
        return this.queryAttributeAndNonAttributeText(options);
    }

    mdRenderBefore(pluginExecResult, options) {
        let self = this;
        let preData = pluginExecResult && pluginExecResult.preData;
        let preHandleNormalButtons = preData && preData.handleNormalButtons || [];
        return Object.assign({}, preData, {
            handleNormalButtons: [...preHandleNormalButtons, (opt) => {
                opt = opt || {};
                let {objApiName} = options;
                let {product_id, price_book_product_id} = self.fieldMapping.getDetailFields(objApiName);
                opt.buttons.forEach(it => {
                    let fieldName = it.lookup_field_name;
                    if ([product_id, price_book_product_id].includes(fieldName)){
                        it.notFilterReadOnly = true;
                    }
                })
                return opt;
            }]
        })
    }

    mdBatchAddBefore(pluginExecResult, options) {
        let {objApiName, selectObjectParams, lookupField} = options;
        let fieldName = lookupField && lookupField.api_name;
        let detailFieldMapping = this.fieldMapping.getDetailFields(objApiName);
        let {product_id, price_book_product_id} = detailFieldMapping || {};
        if (!([product_id, price_book_product_id].includes(fieldName))) {
            return;
        }
        let formObjectData = Object.assign({}, selectObjectParams.formObjectData, {
            selectSKUConfig: Object.assign({}, selectObjectParams.formObjectData && selectObjectParams.formObjectData.selectSKUConfig, {
                selectObjectType: 'sku',
                isOpenAttribute: true,//开启了属性标识
            })
        });
        Object.assign(selectObjectParams, {
            formObjectData,
            disableAdd: true,
            includeAssociated: true,
            useWx: true,
        });
    }

    mdBatchAddAfter(pluginExecResult, options) {
        let {lookupDatas, lookupField, newDatas, objApiName} = options || {};
        if (!newDatas || !lookupDatas) {
            return;
        }
        let fieldName = lookupField && lookupField.api_name;
        let {product_id, price_book_product_id} = this.fieldMapping.getDetailFields(objApiName);
        if (![product_id, price_book_product_id].includes(fieldName)) {
            return;
        }
        newDatas.forEach((newData, index) => {
            let lookupData = lookupDatas && lookupDatas[index];
            let attributeBackFill = this.getAttributeInfoBySkuData(lookupData, objApiName);
            Object.assign(newData, attributeBackFill);
        })
    }

    mdBatchAddEnd(pluginExecResult, options) {
        let {dataUpdater, objApiName} = options || {};
        this.setFieldReadonly(objApiName, dataUpdater);
    }

    mdCloneEnd(pluginExecResult, options) {
        let {dataUpdater, objApiName} = options || {};
        this.setFieldReadonly(objApiName, dataUpdater);
    }

    priceServiceBatchSelectSkuConfig(pluginExecResult, options) {
        let preData = pluginExecResult && pluginExecResult.preData || {};
        return Object.assign({}, preData, {
            isOpenAttribute: true,//开启了属性标识
        });
    }

    priceServiceBatchAddIsTriggerGetPrice(pluginExecResult, options) {
        let preData = pluginExecResult && pluginExecResult.preData || {};
        return Object.assign({}, preData, {
            attribute: true,//开启属性，选sku回来后需要触发取价
        });
    }

    priceServiceBatchAddGetPriceParam(pluginExecResult, options) {
        let {params, lookupDatas} = options;
        params && params.fullProductList && params.fullProductList.length && params.fullProductList.forEach((it, index) => {
            let attrMap = {};
            let lookupData = lookupDatas && lookupDatas.length && lookupDatas[index];
            let attributeValue = lookupData && lookupData[key_select_sku_attribute_value];
            attributeValue && attributeValue.length && attributeValue.forEach(it => {
                let {attribute_id, attribute_value_id} = it || {};
                attrMap[attribute_id] = attribute_value_id;
            });
            Object.assign(it, {attrMap})
        })
    }

    priceServiceGetBackFillsBefore(pluginExecResult, options) {
        let preData = pluginExecResult && pluginExecResult.preData || {};
        let {objApiName, getRealPriceResult} = options;
        let attributePriceBookData = this.getAttributePriceBook(objApiName, getRealPriceResult);
        return Object.assign({}, preData, attributePriceBookData);
    }

    priceServiceFormParseFullProduct(pluginExecResult, options) {
        let preData = pluginExecResult && pluginExecResult.preData || {};
        let {objApiName, detailData} = options;
        let {attribute_json} = this.fieldMapping.getDetailFields(objApiName);
        let attributeJson = detailData && detailData[attribute_json];
        let attrMap = parseAttributeJson2Obj(attributeJson);
        return Object.assign({}, preData, {attrMap});
    }

    bomParseBomData2DetailDataSync(pluginExecResult, options) {
        let preData = pluginExecResult && pluginExecResult.preData;
        let {subProduct, objApiName} = options || {};
        let attributeInfo = this.getAttributeInfoBySkuData(subProduct, objApiName);
        return Object.assign({}, preData, attributeInfo);
    }

    bomParseDetailData2BomDataSync(pluginExecResult, options) {
        let preData = pluginExecResult && pluginExecResult.preData;
        let {objectData} = options;
        let {attribute_json} = this.fieldMapping.getDetailFields(objectData.object_describe_api_name);
        let attributeJson = objectData && objectData[attribute_json];
        return Object.assign({}, preData, {
            selectedAttributeValues: attributeJson2AttributeList(attributeJson)
        })
    }

    bomQueryBomPriceParseParamsBeforeSync(pluginExecResult, options) {
        let preData = pluginExecResult && pluginExecResult.preData;
        let {objApiName, objectData} = options;
        let {attribute_json} = this.fieldMapping.getDetailFields(objApiName);
        let attributeJson = objectData && objectData[attribute_json];
        let attrMap = parseAttributeJson2Obj(attributeJson);
        return Object.assign({}, preData, {attrMap});
    }

    getAttributeInfoBySkuData(skuData, objApiName) {
        let {attribute, attribute_json, attribute_price_book_id} = this.fieldMapping.getDetailFields(objApiName);
        let {
            [key_select_sku_attribute_value]: attributeValue, attribute_price_book_id: attributePriceBookId, attribute_price_book_id__r: attributePriceBookName
        } = skuData || {};
        let attributeNames = getAttributeNames(attributeValue);
        let attributeObj = getAttributeObj(attributeValue);
        let attributeInfo = attributeNames && attributeNames.length ? attributeNames.join(';') : undefined;
        // let attributeJson = isEmpty(attributeObj) ? undefined : JSON.stringify(attributeObj);
        let attributeBackFill = {};
        if (attributePriceBookId && attributePriceBookName) {
            attributeBackFill[attribute_price_book_id] = attributePriceBookId;
            attributeBackFill[`${attribute_price_book_id}__r`] = attributePriceBookName;
        }
        if (attributeInfo) {
            attributeBackFill[attribute] = attributeInfo;
        }
        if (!isEmpty(attributeObj)) {
            attributeBackFill[attribute_json] = attributeObj;
        }
        return attributeBackFill;
    }

    setFieldReadonly(objApiName, dataUpdater) {
        let {product_id, price_book_product_id} = this.fieldMapping.getDetailFields(objApiName);
        dataUpdater && dataUpdater.setReadOnly && dataUpdater.setReadOnly({
            objApiName,
            dataIndex: 'all',
            fieldName: [product_id, price_book_product_id],
            biz: 'attribute',
            priority: 11
        });
    }

    getAttributePriceBook(objApiName, getRealPriceResult) {
        let {attribute_price_book_id: attributePriceBookId, attribute_price_book_id__r} = getRealPriceResult || {};
        let {attribute_price_book_id} = this.fieldMapping.getDetailFields(objApiName);
        return {
            [attribute_price_book_id]: attributePriceBookId,
            [`${attribute_price_book_id}__r`]: attribute_price_book_id__r
        }
    }

    async queryAttributeAndNonAttributeText(options) {
        let { dataGetter, dataUpdater } = options || {};
        let objApiName = this.fieldMapping.getFirstDetailObjApiName();
        if (['SalesOrderProductObj', 'QuoteLinesObj', 'SaleContractLineObj'].includes(objApiName)) {
            return;
        }
        let detailDataList = dataGetter && dataGetter.getDetail && dataGetter.getDetail(objApiName);
        if (detailDataList && detailDataList.length) {
            let { attribute_json, attribute } = this.fieldMapping.getDetailFields(objApiName);
            let dataList = [];
            detailDataList.forEach(it => {
                let parms = {};
                let { dataIndex, [attribute_json]: attributeJson } = it || {};
                if (!isEmpty(attributeJson)) {
                    Object.assign(parms, { attribute_json: attributeJson });
                }
                let parmResult = this.pluginApi.runPluginSync('attribute.queryAttribute.before.sync', Object.assign({}, options, {
                    objApiName,
                    objectData: it,
                }));
                if (!isEmpty(parmResult)) {
                    Object.assign(parms, parmResult)
                }
                if (!isEmpty(parms)) {
                    dataList.push(Object.assign(parms, { _id: dataIndex }))
                }
            });
            return request(this.http, {
                url: 'FHH/EM1HNCRM/API/v1/object/attribute/service/queryAttributeAndNonAttributeText',
                data: { dataList }
            }).then(result => {
                result && result.dataList && result.dataList.forEach(it => {
                    let { _id, attribute: attributeValue } = it || {};
                    let backFill = this.pluginApi.runPluginSync('attribute.queryAttribute.after.sync', Object.assign({}, options, {
                        objApiName,
                        queryResult: it,
                    }));
                    let updateData = {};
                    if (!isEmpty(attributeValue)) {
                        Object.assign(updateData, { [attribute]: attributeValue })
                    }
                    if (!isEmpty(backFill)) {
                        Object.assign(updateData, backFill)
                    }
                    dataUpdater && dataUpdater.updateDetail(objApiName, _id, updateData);
                })
            })
        }
    }

    apply() {
        return [{
            event: "form.render.end",
            functional: this.formRenderEnd.bind(this)
        },{
            event: "md.render.before",
            functional: this.mdRenderBefore.bind(this)
        },{
            event: "md.batchAdd.before",
            functional: this.mdBatchAddBefore.bind(this)
        }, {
            event: "md.batchAdd.after",
            functional: this.mdBatchAddAfter.bind(this)
        }, {
            event: "md.batchAdd.end",
            functional: this.mdBatchAddEnd.bind(this)
        }, {
            event: "md.clone.end",
            functional: this.mdCloneEnd.bind(this)
        }, {
            event: "price-service.batchSelectSkuConfig.sync",
            functional: this.priceServiceBatchSelectSkuConfig.bind(this)
        }, {
            event: "price-service.batchAdd.isTriggerGetPrice.sync",
            functional: this.priceServiceBatchAddIsTriggerGetPrice.bind(this)
        }, {
            event: "price-service.batchAdd.getPriceParam.sync",
            functional: this.priceServiceBatchAddGetPriceParam.bind(this)
        }, {
            event: "price-service.getBackFills.before.sync",
            functional: this.priceServiceGetBackFillsBefore.bind(this)
        }, {
            event: "price-service.form.parseFullProduct.sync",
            functional: this.priceServiceFormParseFullProduct.bind(this)
        }, {
            event: "bom.parseBomData2DetailData.sync",
            functional: this.bomParseBomData2DetailDataSync.bind(this)
        }, {
            event: "bom.parseDetailData2BomData.sync",
            functional: this.bomParseDetailData2BomDataSync.bind(this)
        }, {
            event: "bom.queryBomPrice.parseParams.before",
            functional: this.bomQueryBomPriceParseParamsBeforeSync.bind(this)
        }];
    }
}