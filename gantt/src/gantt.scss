$bar-line-color: #F2F4FB !default;
$bar-line-stroke: #DEE1E8 !default;
$bar-light-color:#FFF7EB !default;
$bar-milestone-color: #E8D6FF !default;
$bar-milestone-pro:#BC97F7 !default;
$bar-stroke: #8d99a6 !default;
$stage-ongoing-pro: #368DFF !default;
$stage-complete-pro: #55D48C !default;
$stage-pro: #C1C5CE !default;
$stage-base-color: #F2F4FB !default;
$stage-base-ongoing-color: #EFF8FF !default;
$stage-base-complete-color: #F0FFF4 !default;
$border-color: #e5e9f2 !default;
$light-yellow: #fcf8e3 !default;
$text-muted: #c1c5ce !default;
$text-light: #545861 !default;
$text-color: #545861 !default;
$bar-pro: #FF9B29;
$handle-color: #454e59;
@mixin common-stroke-style {
    stroke: $border-color;
    stroke-width: 0.6px;
    stroke-linecap: butt;
    stroke-linejoin: miter;
    vector-effect: non-scaling-stroke;
    shape-rendering: crispedges;
}
.gantt-fixed{
    .grid-header {
        fill: #f0f2f5;
        stroke: $border-color;
        stroke-width: 0.5;
    }
    .lower-text,
    .upper-text {
        font-size: 12px;
        fill: $text-light;
        text-anchor: middle;
    }

    .lower-text-active {
        fill: $bar-pro;
    }
}

.gantt-body{

    .stage_mark {
        fill: transparent;
    }

    .grid-row {
        fill: #fff;
        @include common-stroke-style;
        &.no {
            stroke: none;
        }
        &.gantt-row-hover {
            fill: var(--color-info01, #e6f4ff) !important;
        }
    }
    .row-line {
        @include common-stroke-style;
        stroke-width: 1.6px;
    }

    .tick {
        @include common-stroke-style;
    }

    .today-highlight {
        fill: $light-yellow;
        stroke: #ff8000;
        stroke-width: 1;
        opacity: 0.5;
    }

    .arrow {
        fill: none;
        stroke: $text-muted;
        stroke-width: 1.4;
    }
    .arrow-hover {
        stroke-width: 3;
    }

    .bar {
        stroke: $bar-stroke;
        stroke-width: 0;
    }

    .bar-line,.mile-polygon {
        fill: $bar-line-color;
        transition: stroke-width 0.3s ease;
        user-select: none;
    }

    .mile-none-polygon {
        stroke: $bar-line-color;
        stroke-width: initial;
    }

    .bar-fill {
        opacity: 8;

        &.light-progress {
            fill: $bar-light-color;
            stroke: $bar-light-color;
        }
        &.milestone-progress {
            fill: $bar-light-color;
            stroke: none;
        }
    }

    .bar-status {
        fill: $stage-complete-pro;
        border-radius: 50%;
    }

    .bar-status-icon {
        stroke-width: 1.5;
    }

    .stageBar {
        fill: $stage-base-color;
        stroke-width: 0.8;
        stroke-dasharray: 4 4;
        &.ongoing {
            fill: $stage-base-ongoing-color;
        }
        &.completed {
            fill: $stage-base-complete-color;
        }
    }
    .stage-fill {
        stroke-width: 0;
    }
    .bar-progress {
        fill: $bar-pro;
        opacity: 8;
    }
    .mile-progress-group {
        .mile-polygon-progress {
            // fill: $bar-milestone-pro;
            fill: $bar-pro;
        }
    }
    .stageBar-progress {
        fill: $stage-pro;
        &.ongoing {
            fill: $stage-ongoing-pro;
        }
        &.completed {
            fill: $stage-complete-pro;
        }
    }

    .bar-label {
        fill: #2a304d;
        dominant-baseline: central;
        text-anchor: middle;
        font-size: 12px;
        font-weight: lighter;

        &.big {
            fill: $text-light;
            text-anchor: start;
        }

        &.milestone-progress {
            font-size: 9px;
        }
    }

    .bar-wrapper {
        cursor: pointer;
        outline: none;
        .handle {
            fill: $handle-color;
            cursor: ew-resize;
            opacity: 0;
            width: 5px;
            visibility: hidden;
            transition: opacity 0.3s ease;

            &.stage_mark {
                width: 14px;
                fill: transparent;
            }
        }

        &:hover {
            .handle {
                visibility: visible;
                opacity: 0.3;

                &.stage_mark, &.bar-status-wrapper{
                    opacity: 1;
                }

                &:hover {
                    opacity: 0.7;
                }
            }
        }
    }

    .lower-text,
    .upper-text {
        font-size: 12px;
        text-anchor: middle;
    }

    .upper-text {
        fill: $text-light;
    }

    .lower-text {
        fill: $text-color;
    }
    .bar-foreign-wrapper {
        position: relative;
        &.actual {
            display: none;
        }
        .icon-status {
            position: absolute;
            left:1px;
            top:1px;
            &.task.overdue::before {
                color: #fff;
            }
            &.ongoing::before {
                color: #0C6CFF;
            }
            &.stage {
                left: 8px;
                top: 2px;
            }
            
        }
        .icon-hide {
            display: none;
            position: absolute;
            left: 24px;
            top: -1px;
            font-size: 12px;
            color: #545861;
            &.stage {
                display: inline-block;
                
                &.ongoing {
                    color: $stage-ongoing-pro;
                }
                &.completed {
                    color: $stage-complete-pro;
                }
            }
        }
        .fx-icon-time, .fx-icon-zt, .fx-icon-close-2 {
            &::before {
                color: #737C8C;
            }
        }
    }
}

.project-popup-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    padding: 10px;
    width: fit-content;
    min-width: 200px;
    background: #fff;
    color: #959da5;
    box-shadow: 0px 8px 12px -4px rgba(9, 30, 66, 0.25),
        0px 0px 1px 0px rgba(9, 30, 66, 0.18);
    border-radius: 3px;
    display: none;
    z-index: 99999;
    .icon-name {
        color: white;
        padding: 2px;
        border-radius: 2px;
        margin-right: 4px;
    }

    .bg-task {
        background: $bar-pro;
    }

    .bg-task-milepost {
        background: $stage-pro;
    }

    .title {
        border-bottom: 3px solid $bar-pro;
        padding: 10px;
    }

    .subtitle {
        padding: 10px;
        color: #dfe2e5;
    }

    .pointer {
        position: absolute;
        height: 5px;
        margin: 0 0 0 -5px;
        border: 5px solid transparent;
        border-top-color: rgba(0, 0, 0, 0.8);
        display: none;
    }
    .name-box {
        display: flex;
        align-items: center;
        font-size: 12px;
        margin-bottom: 9px;

        .name {
            font-weight: 500;
            color: #181c25;
            flex-shrink: 0;
        }

        .icon-name {
            flex-shrink: 0;
        }
    }

    p {
        font-size: 12px;
        font-weight: 400;
        color: #91959e;
        line-height: 17px;
        padding: 0;
        margin: 0;
        margin-bottom: 2px;
    }

    .dependencies {
        display: flex;
    }

    .dependencies-info {
        line-height: 40px;
    }

    .dependencies-icon {
        position: relative;
        width: 14px;
        height: 40px;
        border-left: 2px solid #91959e;
        margin: 0 0 5px 14px;

        span {
            position: absolute;
            top: 13px;
            left: -8px;
            width: 14px;
            height: 14px;
            border-radius: 50%;
            background: #91959e;
        }
    }

    &.drag {
        background: #DEE1E6;
        padding: 6px 8px;

        .popup-drag {
            color: #545861;
        }
    }
}
.crm-gantt-dialog {
    margin-top: -10px !important;
    p {
        color: #2a304d;
        font-size: 14px;
        &:last-child {
            color: #91959E;
        }
    }
    ul {
        font-size: 14px;
        padding-left: 5px;
        margin-top: 8px;
        margin-bottom: 8px;
        line-height: 18px;
        color: #91959E;
    }
}