import date_utils from './date_utils';
import {
    $,
    createSVG,
    setInlineStyles,
    addScrollbarStyles
} from './svg_utils';
import Bar from './bar';
import Arrow from './arrow';
import Popup from './popup';

import './gantt.scss';

const VIEW_MODE = {
    QUARTER_DAY: 'Quarter Day',
    HALF_DAY: 'Half Day',
    DAY: 'Day',
    WEEK: 'Week',
    MONTH: 'Month',
    YEAR: 'Year'
};

export default class Gantt {
    constructor(wrapper, tasks, options) {
        this.popup = {};
        this.tasks = tasks;
        this.options = options;
        this.popup_wrapper = {};
        this.container = wrapper?.length && wrapper[0];
        this.projectTask = options.projectTask || {};
        this.clear();
        this.setup_wrapper();
        this.setup_options();
        this.setup_tasks();
        this.setup_layers();
        this.change_view_mode();
    }

    setup_wrapper () {
        this.$svg = createSVG('svg', {
            append_to: this.container,
            class: 'gantt-body'
        });
        //头部容器
        this.$svgHeader = createSVG('svg',{
            append_to: this.container,
            class:'gantt-header'
        });
        this.$headerLay = createSVG('g', {
            class: 'header-wrapper grid',
            append_to: this.$svgHeader
        });
        const styles = {
            position: 'relative',
            overflow: 'hidden',
            overflowX: 'auto',
            fontSize: '12px',
            height: '100%'
        };
        this.$fixElment = document.createElement('div');
        this.$fixElment.classList.add('gantt-fixed');
        this.container.appendChild(this.$fixElment);
        this.container.appendChild(this.$svg);
        this.$fixElment.appendChild(this.$svgHeader);
        setInlineStyles(this.container, styles);
        addScrollbarStyles(this.container);
    }
   setup_options() {
        const options = this.options;
        const view_mode = options.projectTask?.view_mode || 'Day';

        // 定义默认选项
        const default_options = this.getDefaultOptions(view_mode);

        // 合并默认选项和用户提供的选项
        this.options = { ...default_options, ...options };
    }

    getDefaultOptions(view_mode) {
        return {
            header_height: 50,
            column_width: 30,
            bar_height: 18,
            bar_corner_radius: 2,
            stage_corner_radius: 4,
            stage_padding: 30,
            stageBarHeight: 8,
            arrow_curve: 5,
            arrow_space: 10,
            padding: 20,
            step: 24,
            view_mode,
            date_format: "YYYY-MM-DD",
            popup_trigger: "mouseenter",
            popup_off: "mouseleave",
            language: "zh",
            ...this.bindEventHandlers(),
            custom_popup_html: this.getCustomPopupHtml()
        };
    }

    /**
     * 统一的事件处理方法
     * @returns {Object} 所有事件处理器的配置对象
     */
    bindEventHandlers() {
        // 定义所有事件类型及其参数映射
        const eventTypes = {
            custom_popup_click_handle: 'popup_click',
            on_click: 'task_lick',
            on_date_change: 'date_change',
            on_progress_change: 'progress_change'
        };

        // 生成所有事件处理器
        return Object.entries(eventTypes).reduce((handlers, [key, eventName]) => {
            handlers[key] = (...args) => {
                console.log(`Event: ${eventName}`, ...args);
                if (this.options[eventName]) {
                    this.options[eventName].apply(this, args);
                }
            };
            return handlers;
        }, {});
    }

    /**
     * 视图弹窗模板
     * @returns HTML模板字符串
     */
    getCustomPopupHtml() {
        return {
            bar: (data) => {
                const task = data.task;
                return `<div>${task.name}</div>`;
            },
            arrow: (data) => {
                const { from_task, to_task, dependencies_type } = data;
                return `<div>${from_task.name} -> ${to_task.name} ${dependencies_type}</div>`;
            }
        }
    }
    /**
     * Set z-index
     * @param {Object} sourceElement
     * @param {String} type
     * @param {String} current
     * @param {String} target
     */
    setZindex (sourceElement, type, current, target) {
        let curLayer = this.layers[current];
        let targetLayer = this.layers[target];

        switch (type) {
            case 'top':
                if (curLayer) {
                    this.$svg.appendChild(curLayer);
                    this.options.setZindex && this.options.setZindex(sourceElement, type);
                }
                break;
            case 'before':
                if (curLayer && targetLayer) {
                    this.$svg.insertBefore(curLayer, targetLayer);
                    this.options.setZindex && this.options.setZindex(sourceElement, type);
                }
                break;
        }
    }
    setup_tasks () {
        let tasks = this.tasks || [];
        let cacheIndex = {};
        let index = 0;

        // prepare tasks
        this.tasks = tasks.map((task, i) => {
            let _index = index;

            if (cacheIndex[task.id]) {
                _index = cacheIndex[task.id];
            } else {
                cacheIndex[task.id] = _index;
                index++;
            }

            // convert to Date objects
            task._start = date_utils.parse(task.start);
            task._end = date_utils.parse(task.end);

            // make task invalid if duration too large
            if (date_utils.diff(task._end, task._start, 'year') > 10) {
                task.end = null;
            }

            // cache index
            task._index = _index;

            // invalid dates
            if (!task.start && !task.end) {
                const today = date_utils.today();
                task._start = today;
                task._end = date_utils.add(today, 2, 'day');
            }

            if (!task.start && task.end) {
                task._start = date_utils.add(task._end, -2, 'day');
            }

            if (task.start && !task.end) {
                task._end = date_utils.add(task._start, 2, 'day');
            }

            // if hours is not set, assume the last day is full day
            // e.g: 2018-09-09 becomes 2018-09-09 23:59:59
            const task_end_values = date_utils.get_date_values(task._end);
            if (task_end_values.slice(3).every(d => d === 0)) {
                task._end = date_utils.add(task._end, 24, 'hour');
            }

            // invalid flag
            if (!task.start || !task.end) {
                task.invalid = true;
            }

            // dependencies
            if (typeof task.dependencies === 'string' || !task.dependencies) {
                let deps = [];
                if (task.dependencies) {
                    deps = task.dependencies
                        .split(',')
                        .map(d => d.trim())
                        .filter(d => d);
                }
                task.dependencies = deps;
            }

            // uids
            if (!task.id) {
                task.id = generate_id(task);
            }

            return task;
        });

        this.setup_dependencies();
    }

    setup_dependencies () {
        this.dependency_map = {};
        for (let t of this.tasks) {
            for (let d of t.dependencies) {
                this.dependency_map[d] = this.dependency_map[d] || [];
                this.dependency_map[d].push(t.id);
            }
        }
    }

    change_view_mode (mode = this.options.view_mode) {
        this.update_view_scale(mode);
        this.tasks.length && this.setup_dates();
        this.render();
        // fire viewmode_change event
        this.trigger_event('view_change', [mode]);
        this.bind_events();
    }

    update_view_scale (view_mode) {
        this.options.view_mode = view_mode;
        this.projectTask.view_mode = view_mode;
        if (view_mode === VIEW_MODE.DAY) {
            this.options.step = 24;
            this.options.column_width = 30;
        } else if (view_mode === VIEW_MODE.HALF_DAY) {
            this.options.step = 24 / 2;
            this.options.column_width = 30;
        } else if (view_mode === VIEW_MODE.QUARTER_DAY) {
            this.options.step = 24 / 4;
            this.options.column_width = 30;
        } else if (view_mode === VIEW_MODE.WEEK) {
            this.options.step = 24 * 7;
            this.options.column_width = 140;
        } else if (view_mode === VIEW_MODE.MONTH) {
            this.options.step = 24 * 30;
            this.options.column_width = 120;
        } else if (view_mode === VIEW_MODE.YEAR) {
            this.options.step = 24 * 365;
            this.options.column_width = 120;
        }
    }
    //计划、实际视图区分
    update_view_plan_actual(view) {
        (this.bars || []).filter((item,index) => {
            const isActual = item.$barForeignGroup?.classList.contains('actual');
            const isEmpty = item.task?.isEmpty;
            item.group.style.display = 'none';
            if(view !== 'plan_actual') {
                item.group.classList.contains(view) && (item.group.style.display = 'block');
            }else {
                item.group.style.display = isEmpty ? 'none' : 'block';
            }
            isActual && (item.$barForeignGroup.style.display = view == 'plan' ? 'block' : 'none');
        });
    }

    setup_dates () {
        this.setup_gantt_dates();
        this.setup_date_values();
    }

    setup_gantt_dates () {
        this.gantt_start = this.gantt_end = null;

        for (let task of this.tasks) {
            // set global start and end date
            if (!this.gantt_start || task._start < this.gantt_start) {
                this.gantt_start = task._start;
            }
            if (!this.gantt_end || task._end > this.gantt_end) {
                this.gantt_end = task._end;
            }
        }

        this.gantt_start = date_utils.start_of(this.gantt_start, 'day');
        this.gantt_end = date_utils.start_of(this.gantt_end, 'day');

        // add date padding on both sides
        if (this.view_is([VIEW_MODE.QUARTER_DAY, VIEW_MODE.HALF_DAY])) {
            this.gantt_start = date_utils.add(this.gantt_start, -7, 'day');
            this.gantt_end = date_utils.add(this.gantt_end, 7, 'day');
        } else if (this.view_is(VIEW_MODE.MONTH)) {
            this.gantt_start = date_utils.start_of(this.gantt_start, 'year');
            this.gantt_end = date_utils.add(this.gantt_end, 1, 'year');
        } else if (this.view_is(VIEW_MODE.YEAR)) {
            this.gantt_start = date_utils.add(this.gantt_start, -2, 'year');
            this.gantt_end = date_utils.add(this.gantt_end, 2, 'year');
        } else {
            this.gantt_start = date_utils.add(this.gantt_start, -1, 'month');
            this.gantt_end = date_utils.add(this.gantt_end, 1, 'month');
        }
    }

    setup_date_values () {
        this.dates = [];
        let cur_date = null;

        while (cur_date === null || cur_date < this.gantt_end) {
            if (!cur_date) {
                cur_date = date_utils.clone(this.gantt_start);
            } else {
                if (this.view_is(VIEW_MODE.YEAR)) {
                    cur_date = date_utils.add(cur_date, 1, 'year');
                } else if (this.view_is(VIEW_MODE.MONTH)) {
                    cur_date = date_utils.add(cur_date, 1, 'month');
                } else {
                    cur_date = date_utils.add(
                        cur_date,
                        this.options.step,
                        'hour'
                    );
                }
            }
            this.dates.push(cur_date);
        }
    }

    bind_events () {
        this.bind_grid_click();
        this.bind_bar_events();
    }

    render () {
        this.make_grid();
        this.make_bars();
        this.make_arrows();
        this.map_arrows_on_bars();
        this.set_width();
    }

    setup_layers () {
        this.layers = {};
        const layers = ['grid', 'arrow', 'progress', 'bar', 'details'];
        // make group layers
        for (let layer of layers) {
            this.layers[layer] = createSVG('g', {
                class: layer,
                append_to: this.$svg
            });
            // $('.gantt').append(this.layers[layer])
        }

    }

    make_grid () {
        this.make_grid_header();
        this.make_grid_rows();
        this.make_grid_ticks();
        this.make_grid_highlights();
    }

    make_grid_rows () {
        const rows_layer = createSVG('g', {
            append_to: this.layers.grid
        });
        const row_width = this.dates.length * this.options.column_width;
        const row_height = this.options.bar_height + this.options.padding;
        const tasks = this.get_tasks() || [];
        let row_y = 0;

        tasks.forEach((task) => {
            createSVG('rect', {
                x: 0,
                y: row_y,
                width: row_width,
                height: row_height,
                id: task.id,
                class: `grid-row ${task.isEmpty ? 'no': ''}`,
                append_to: rows_layer
            });
            row_y += this.options.bar_height + this.options.padding;
        });
        createSVG('line', {
            x1: 0,
            y1: row_y,
            x2: row_width,
            y2: row_y,
            class: 'row-line',
            append_to: rows_layer
        });
    }

    make_grid_header () {
        const header_width = this.dates.length * this.options.column_width;
        const header_height = this.options.header_height + this.options.padding / 2;
        const grid_height = (this.options.bar_height + this.options.padding) * this.get_tasks().length;
        this.$fixElment.style.height = header_height + 'px';
        createSVG('rect', {
            x: 0,
            y: 0,
            width: header_width,
            height: header_height,
            class: 'grid-header',
            append_to: this.$headerLay
        });
        this.$layerDate = createSVG('g', {
            class: 'date',
            append_to: this.$headerLay
        });
        $.attr(this.$svg, {
            height: grid_height,
            width: '100%'
        });
        $.attr(this.$svgHeader, {
            width: header_width,
            height: header_height
        });
        this.make_dates();
    }

    make_grid_ticks () {
        let tick_x = 0;
        let tick_y = 0;
        let tick_height =
            (this.options.bar_height + this.options.padding) *
            this.get_tasks().length;

        for (let date of this.dates) {
            let tick_class = 'tick';
            // thick tick for monday
            if (this.view_is(VIEW_MODE.DAY) && date.getDate() === 1) {
                tick_class += ' thick';
            }
            // thick tick for first week
            if (
                this.view_is(VIEW_MODE.WEEK) &&
                date.getDate() >= 1 &&
                date.getDate() < 8
            ) {
                tick_class += ' thick';
            }
            // thick ticks for quarters
            if (this.view_is(VIEW_MODE.MONTH) && (date.getMonth() + 1) % 3 === 0) {
                tick_class += ' thick';
            }

            createSVG('path', {
                d: `M ${tick_x} ${tick_y} v ${tick_height}`,
                class: tick_class,
                append_to: this.layers.grid
            });
            tick_x += this.options.column_width;
        }
    }

    make_grid_highlights () {
        // highlight today's date
        const _this = this;
        let x =
            date_utils.diff(date_utils.today(), this.gantt_start, 'hour') /
            this.options.step *
            this.options.column_width + this.options.column_width / 2;
        if (_this.options.view_mode === 'Week') {
            this.dates.forEach((item, index) => {
                if (_this.dates[index + 1] && (new Date().getTime() > item.getTime() && new Date().getTime() < _this.dates[index + 1].getTime())) {
                    const days = Math.floor((new Date().getTime() - item.getTime()) / (1000 * 60 * 60 * 24)) * (_this.options.column_width / 7);
                    x = index * _this.options.column_width + days;
                } else if (new Date().getTime() === item.getTime()) {
                    x = index * _this.options.column_width;
                } else if (_this.dates[index + 1] && (new Date().getTime() === _this.dates[index + 1].getTime())) {
                    x = (index + 1) * _this.options.column_width;
                }
            })
        } else if(_this.options.view_mode === 'Month'){
            x = date_utils.diff(date_utils.today(), this.gantt_start, 'hour') / this.options.step * this.options.column_width
        }
        const height =
            (this.options.bar_height + this.options.padding) *
            this.get_tasks().length
        // +this.options.padding / 2;

        // createSVG('rect', {
        //     x,
        //     y,
        //     width,
        //     height,
        //     class: 'today-highlight',
        //     append_to: this.layers.grid
        // });
        createSVG('path', {
            d: `M ${x} ${0} v ${height}`,
            class: 'today-highlight',
            append_to: this.layers.grid
        });
    }

    make_dates () {
        let get_dates_to_draw = this.get_dates_to_draw();
        let _this = this;
        const x =
            date_utils.diff(date_utils.today(), this.gantt_start, 'hour') /
            this.options.step *
            this.options.column_width + this.options.column_width / 2;
        get_dates_to_draw.forEach((item, index) => {
            let date = item; let is_show_month = false;
            //日
            if (get_dates_to_draw[index - 1] && date.month !== get_dates_to_draw[index - 1].month) {
                is_show_month = true;
            }
            createSVG('text', {
                x: +date.lower_x,
                y: _this.options.view_mode === 'Day' ? (date.lower_y - 10) : date.lower_y - 1,
                millisecond: date.time,
                innerHTML: ((_this.options.view_mode === 'Week' && is_show_month) ? date.month + $t('月') : '') + date.lower_text + `${this.options.view_mode === 'Week' ? $t('日') : ''}`,
                class: `lower-text ${x == date.lower_x ? 'lower-text-active' : ''}`,
                append_to: _this.$layerDate
            });
            //周
            if (_this.options.view_mode === 'Day') {
                createSVG('text', {
                    x: +date.lower_x,
                    y: date.lower_y + 4,
                    innerHTML: date_utils.format_week(date.week, 'zh'),
                    class: `lower-text ${x == date.lower_x ? 'lower-text-active' : ''}`,
                    append_to: _this.$layerDate
                });
            }

            //upper的日期区间
            if (_this.options.view_mode === 'Day' && Number(date.lower_text) === 15) {
                createSVG('text', {
                    x: date.lower_x,
                    y: date.lower_y - 30,
                    innerHTML: date.year + $t('年') + date.month + $t('月'),
                    class: 'upper-text',
                    append_to: _this.$layerDate,
                });
            } else if (_this.options.view_mode === 'Week' && get_dates_to_draw[index + 1]) {
                if (Number(get_dates_to_draw[index].lower_text) <= 16 && Number(get_dates_to_draw[index + 1].lower_text) > 16) {
                    const x = (get_dates_to_draw[index + 1].lower_x - date.lower_x) / 2;
                    createSVG('text', {
                        x: date.lower_x + x,
                        y: date.lower_y - 30,
                        innerHTML: date.year + $t('年') + date.month + $t('月'),
                        class: 'upper-text',
                        append_to: _this.$layerDate,
                    });
                }
            } else if (_this.options.view_mode === 'Month' && get_dates_to_draw[index].month === 6 && get_dates_to_draw[index + 1]) {
                const x = (get_dates_to_draw[index + 1].lower_x - date.lower_x) / 2;
                createSVG('text', {
                    x: date.lower_x + x,
                    y: date.lower_y - 30,
                    innerHTML: date.year + $t('年'),
                    class: 'upper-text',
                    append_to: _this.$layerDate,
                });
            }
        })

    }

    get_dates_to_draw () {
        let last_date = null;
        const dates = this.dates.map((date, i) => {
            last_date = date;
            const d = this.get_date_info(date, last_date, i);
            //暂时特殊处理以后扩展
            d['time'] = date.getTime();
            d['year'] = date.getFullYear();
            d['month'] = +date.getMonth() + 1;
            d['week'] = date.getDay();
            return d;
        });
        return dates;
    }

    get_date_info (date, last_date, i) {
        last_date = date_utils.add(date, 1, 'year');
        const date_text = {
            'Quarter Day_lower': date_utils.format(
                date,
                'HH',
                this.options.language
            ),
            'Half Day_lower': date_utils.format(
                date,
                'HH',
                this.options.language
            ),
            Day_lower: date_utils.format(date, 'D', this.options.language),
            Week_lower: date.getMonth() !== last_date.getMonth() ?
                date_utils.format(date, 'D MMM', this.options.language) : date_utils.format(date, 'D', this.options.language),
            Month_lower: date_utils.format(date, 'MMMM', this.options.language),
            Year_lower: date_utils.format(date, 'YYYY', this.options.language),
            'Quarter Day_upper': date.getDate() !== last_date.getDate() ?
                date_utils.format(date, 'D MMM', this.options.language) : '',
            'Half Day_upper': date.getDate() !== last_date.getDate() ?
                date.getMonth() !== last_date.getMonth() ?
                    date_utils.format(date, 'D MMM', this.options.language) :
                    date_utils.format(date, 'D', this.options.language) : '',
            Day_upper: date.getMonth() !== last_date.getMonth() ?
                date_utils.format(date, 'MMMM', this.options.language) : '',
            Week_upper: date.getMonth() !== last_date.getMonth() ?
                date_utils.format(date, 'MMMM', this.options.language) : '',
            Month_upper: date.getFullYear() !== last_date.getFullYear() ?
                date_utils.format(date, 'YYYY', this.options.language) : '',
            Year_upper: date.getFullYear() !== last_date.getFullYear() ?
                date_utils.format(date, 'YYYY', this.options.language) : ''
        };

        const base_pos = {
            x: i * this.options.column_width,
            lower_y: this.options.header_height,
            upper_y: this.options.header_height - 25
        };

        const x_pos = {
            'Quarter Day_lower': this.options.column_width * 4 / 2,
            'Quarter Day_upper': 0,
            'Half Day_lower': this.options.column_width * 2 / 2,
            'Half Day_upper': 0,
            Day_lower: this.options.column_width / 2,
            Day_upper: this.options.column_width * 30 / 2,
            Week_lower: 0,
            Week_upper: this.options.column_width * 4 / 2,
            Month_lower: this.options.column_width / 2,
            Month_upper: this.options.column_width * 12 / 2,
            Year_lower: this.options.column_width / 2,
            Year_upper: this.options.column_width * 30 / 2
        };

        return {
            upper_text: date_text[`${this.options.view_mode}_upper`],
            lower_text: date_text[`${this.options.view_mode}_lower`],
            upper_x: base_pos.x + x_pos[`${this.options.view_mode}_upper`],
            upper_y: base_pos.upper_y,
            lower_x: base_pos.x + x_pos[`${this.options.view_mode}_lower`],
            lower_y: base_pos.lower_y
        };
    }

    make_bars () {
        this.bars = this.tasks.map(task => {
            const bar = new Bar(this, task);
            this.layers.bar.appendChild(bar.group);
            return bar;
        });
    }

    make_arrows () {
        this.arrows = [];
        for (let task of this.tasks) {
            let arrows = [];
            arrows = task.dependencies
                .map((task_id, index) => {
                    const dependency = this.get_task(task_id, true);
                    if (!dependency) return;
                    const arrow = new Arrow(
                        this,
                        this.get_bar(dependency.id, true), // from_task
                        this.get_bar(task.id, true), // to_task
                        task.dependenciesType[index]
                    );
                    this.layers.arrow.appendChild(arrow.element);
                    return arrow;
                })
                .filter(Boolean); // filter falsy values
            this.arrows = this.arrows.concat(arrows);
        }
    }

    map_arrows_on_bars () {
        for (let bar of this.bars) {
            bar.arrows = this.arrows.filter(arrow => {
                return (
                    arrow.from_task.task.id === bar.task.id ||
                    arrow.to_task.task.id === bar.task.id
                );
            });
        }
    }

    set_width () {
        const cur_width = this.$svg.getBoundingClientRect().width;
        const actual_width = this.$svg
            .querySelector('.grid .grid-row')
            .getAttribute('width');
        if (cur_width < actual_width) {
            this.$svg.setAttribute('width', actual_width);
        }
    }

    add_grid_row_class (className, index, trId) {
        let row = this.$svg.querySelectorAll('.grid .grid-row');

        row.forEach((item, key) => {
            if(item.isEmpty) {
                item.classList[!trId ? 'add' : 'remove'](className);
            }else {
                item.classList[item.id === trId ? 'add' : 'remove'](className);
            }
        })
    }

    //今天竖线位置定位
    set_scroll_position (date) {
        let container = $('.project-gantt-container');
        let main_gantt = $(".crm-scroll.gantt-scroll");
        let gantt_start = $('#gantt-project').querySelector('.date').querySelectorAll('.lower-text ')[0].getAttribute('millisecond');
        let hours_before_first_task = date_utils.diff(date, new Date(Number(gantt_start)),'hour');
        const scroll_pos = hours_before_first_task / this.options.step * this.options.column_width - container.clientWidth / 2;
        container.scrollLeft = scroll_pos;
        main_gantt && (main_gantt.scrollLeft = scroll_pos);
    }

    bind_grid_click () {
        $.on(
            this.$svg,
            this.options.popup_trigger,
            '.grid-row, .grid-header',
            () => {
                this.unselect_all();
                this.hide_popup();
            }
        );
    }

    bind_bar_events () {
        const _this = this;
        let bars = [];
        let x_on_start = 0;
        let y_on_start = 0;
        let is_dragging = false;
        let is_resizing_left = false;
        let is_resizing_right = false;
        this.isHasFill = false;//拖动的是否是实际
        let parent_bar_id = null;
        this.bar_being_dragged = null;
        function action_in_progress () {
            return is_dragging || is_resizing_left || is_resizing_right;
        }
        function handleMouseDown(e, element) {
            if(_this.projectTask.hasOwnProperty('gantt_allow_update_date') && !_this.projectTask.gantt_allow_update_date) return;
            const bar_wrapper = $.closest('.bar-wrapper', element);
            if (e.target.classList.contains('left') && element.classList.contains('bar-wrapper')) {
                is_resizing_left = true;
            } else if (e.target.classList.contains('right') && element.classList.contains('bar-wrapper')) {
                is_resizing_right = true;
            } else if (element.classList.contains('bar-wrapper')) {
                is_dragging = true;
                bar_wrapper.style.cursor = 'move';
            }
            bar_wrapper.classList.add('active');
            x_on_start = e.offsetX;
            y_on_start = e.offsetY;
            parent_bar_id = bar_wrapper.getAttribute('data-id');
            bars = [_this.get_bar(parent_bar_id)]
            _this.bar_being_dragged = parent_bar_id;
            bars.forEach(bar => {
                const $bar = bar.$bar;
                $bar.ox = $bar.getX();
                $bar.oy = $bar.getY();
                $bar.owidth = $bar.getWidth();
                $bar.oheight = $bar.getHeight();
                $bar.element = element;
                $bar.is_milestone = $bar.classList.contains('milestone-progress');
                $bar.finaldx = 0;
            });
        }
        function handleMouseMove(e) {
            if (!action_in_progress()) return;
            const dx = e.offsetX - x_on_start;
            const dy = e.offsetY - y_on_start;
            let bar_fill = false;
            bars.forEach(bar => {
                _this.hide_popup();
                const $bar = bar.$bar;
                $bar.finaldx = dx;
                bar.action_completed = true;
                bar.task.isOn = false;
                //阶段实际bar
                bar_fill = bar.task.tasktype === 'stage' ? $bar.classList.contains('stage-fill') : $bar.classList.contains('bar-fill');
                _this.isHasFill = bar_fill || $bar.classList.contains('bar-progress');
                const obj = {
                    barWidth: $bar.owidth,
                    barX: $bar.ox,
                    is_dragging,
                    is_resizing_left,
                    is_resizing_right
                }
                localStorage.setItem('drag_old_value', JSON.stringify(obj))
                if (is_resizing_left) {
                    _this.drag_direc = 'left';
                    if (parent_bar_id === bar.task.id) {
                        bar.update_bar_position({
                            x: $bar.ox + $bar.finaldx,
                            width: $bar.owidth - $bar.finaldx,
                            is_resizing_left: true,
                        });
                    }
                } else if (is_resizing_right) {
                    _this.drag_direc = 'right';
                    if (parent_bar_id === bar.task.id) {
                        bar.update_bar_position({
                            width: $bar.owidth + $bar.finaldx,
                            is_resizing_right: true,
                        });
                    }
                } else if (is_dragging) {
                    _this.drag_direc = 'drag';
                    bar.update_bar_position({
                        x: $bar.ox + $bar.finaldx,
                        is_dragging
                    });
                }
                //日期悬浮框
                bar.show_time_popup({
                    clientX: e.clientX,
                    is_left: is_resizing_left,
                    is_right: is_resizing_right,
                    drag: is_dragging
                });
            });
        }
        function handleMouseUp(e) {
            if (!action_in_progress()) return;
            $.clear(e)
            _this.on_mouse_up_fun(bars,{is_dragging, is_resizing_left, is_resizing_right});
            is_dragging = false;
            is_resizing_left = false;
            is_resizing_right = false;
            //设置拖拽埋点
            if(bars.length){
              const isStage = bars[0].$bar.classList.contains('stageBar') ? 'stage' : 'task';
              CRM.util.sendLog('ProjectObj', 'gantt', {
                operationId: 'dragView',
                eventType: 'pv',
                eventData: {
                  type: isStage,
                  direction: _this.drag_direc
                }
              })
            }
        }
        $.on(this.$svg, 'mousedown', '.bar-wrapper', handleMouseDown);
        $.on(this.$svg, 'mousemove', handleMouseMove);
        document.addEventListener('mouseup', handleMouseUp);
        $.on(this.$svg, 'mouseup', e => {
            $.clear(e);
            handleMouseUp(e);
        });
    }
    on_mouse_up_fun (bars,obj) {
        const _this = this;
        this.bar_being_dragged = null;
        this.hide_popup();
        bars.forEach(bar => {
            const $bar = bar.$bar;
            if (!$bar.finaldx) return;
            CRM.util.waiting();
            bar.group.classList.remove('active');
            bar.date_changed(_this.isHasFill,obj);
        })
    }
    get_all_dependent_tasks (task_id) {
        let out = [];
        let to_process = [task_id];
        while (to_process.length) {
            const deps = to_process.reduce((acc, curr) => {
                acc = acc.concat(this.dependency_map[curr]);
                return acc;
            }, []);

            out = out.concat(deps);
            to_process = deps.filter(d => !to_process.includes(d));
        }

        return out.filter(Boolean);
    }
    unselect_all () {
        [...this.$svg.querySelectorAll('.bar-wrapper')].forEach(el => {
            el.classList.remove('active');
        });
    }

    view_is (modes) {
        if (typeof modes === 'string') {
            return this.options.view_mode === modes;
        }

        if (Array.isArray(modes)) {
            return modes.some(mode => this.options.view_mode === mode);
        }

        return false;
    }
    get_tasks () {
        let taskObj = {};

        return (this.tasks || []).filter(item => {
            if (!taskObj[item.id]) {
                taskObj[item.id] = true;
                return item;
            }
        });
    }

    get_task (id, notActual) {
        return this.tasks.find(task => {
            return notActual ? task.id === id && !task._is_actual_bar : task.id === id;
        });
    }

    get_bar (id, notActual) {
        return this.bars.find(bar => {
            return notActual ? bar.task.id === id && !bar.task._is_actual_bar : (bar.group.classList.contains('active') && bar.task.id === id);
        });
    }

    get_popup_wrapper (type) {
        if (this.popup_wrapper[type]) {
            return this.popup_wrapper[type];
        }

        this.popup_wrapper[type] = document.createElement('div');
        this.popup_wrapper[type].classList.add('project-popup-wrapper');
        this.popup_wrapper[type].classList.add(`project-popup-wrapper-${type}`);
        document.body.appendChild(this.popup_wrapper[type]);

        return this.popup_wrapper[type];
    }
    show_popup (type, options) {
        clearTimeout(this.hide_popup_timer);
        this.hide_popup();
        const objectDescribe_dates = this.projectTask.objectDescribe_dates;
        options.mousemove && this.popup_wrapper[type].classList.add('drag');
        !options.mousemove && this.popup_wrapper[type] && this.popup_wrapper[type].classList.remove('drag');
        if (!this.popup[type]) {
            let wrapper = this.get_popup_wrapper(type);

            this.popup[type] = new Popup(
                wrapper,
                this.options.custom_popup_html[type]
            );

            $.on(wrapper, 'mouseenter', e => {
                clearTimeout(this.hide_popup_timer);
            })

            $.on(wrapper, 'mouseleave', e => {
                this.hide_popup();
            })

            $.on(wrapper, 'click', e => {
                this.options.custom_popup_click_handle && this.options.custom_popup_click_handle(e, options.data.task);
            })
        }
        this.popup[type].show(options, objectDescribe_dates);
    }

    hide_popup (type) {
        if (type) {
            this.popup[type] && this.popup[type].hide();
        } else {
            Object.keys(this.popup).forEach(item => {
                this.popup[item].hide();
            })
        }
    }

    trigger_event (event, args) {
        if (this.options['on_' + event]) {
            this.options['on_' + event].apply(null, args);
        }
    }

    /**
     * Gets the oldest starting date from the list of tasks
     *
     * @returns Date
     * @memberof Gantt
     */
    get_oldest_starting_date () {
        return this.tasks
            .map(task => task._start)
            .reduce(
                (prev_date, cur_date) =>
                    cur_date <= prev_date ? cur_date : prev_date
            );
    }

    clear () {
        this.container && (this.container.innerHTML = '');
    }
}

Gantt.VIEW_MODE = VIEW_MODE;

function generate_id (task) {
    return (
        task.name +
        '_' +
        Math.random()
            .toString(36)
            .slice(2, 12)
    );
}
