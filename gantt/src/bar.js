import date_utils from './date_utils';
import {
    $,
    createSVG,
    animateSVG
} from './svg_utils';
import {createMile, draw_mile_progress_bar, update_mile_position} from './milestone';

export default class Bar {
    constructor(gantt, task) {
        this.set_defaults(gantt, task);
        this.prepare();
        this.draw();
        this.bind();
    }

    set_defaults (gantt, task) {
        this.action_completed = false;
        this.gantt = gantt;
        this.task = task;
    }

    prepare () {
        this.prepare_values();
        this.prepare_helpers();
        this.prepare_bar_fields();
    }

    prepare_values () {
        this.invalid = this.task.invalid;
        this.height = this.task.tasktype === 'stage' ? this.gantt.options.stageBarHeight : this.gantt.options.bar_height;
        this.x = this.compute_x();
        this.y = this.compute_y();
        this.corner_radius = this.gantt.options.bar_corner_radius;
        this.stage_corner_radius = this.gantt.options.stage_corner_radius;
        this.duration =
            date_utils.diff(this.task._end, this.task._start, 'hour') /
            this.gantt.options.step;
        this.width = this.gantt.options.column_width * this.duration;
        this.polygon_w = this.compute_polygon_w();
        this.progress_width =
            this.gantt.options.column_width *
            this.duration *
            (this.task.progress / 100) || 0;
        this.group = createSVG('g', {
            class: `bar-wrapper ${this.task.isEmpty ? 'empty' : (this.task._is_actual_bar ? 'actual' : 'plan')}`,
            'data-id': this.task.id
        });
        this.bar_group = createSVG('g', {
            class: 'bar-group',
            append_to: this.group
        });
        this.handle_group = createSVG('g', {
            class: 'handle-group',
            append_to: this.group
        });
    }

    prepare_bar_fields() {
        this.otherBar = '';
        this.milestoneBar = '';
        this.otherBarProgress = '';
        this.otherBar = !this.task._is_actual_bar ? 'bar-line' : 'bar-fill';
        switch (this.task.tasktype) {
            case 'son':
            case 'roottask':
                this.milestoneBar = this.task.is_milestone ? 'milestone-progress' : '';
                break;
            case 'stage':
                this.milestoneBar = '';
                this.otherBarProgress = 'stageBar-progress';
                this.otherBar = `stageBar ${this.task._is_actual_bar ? 'stage-fill' : ''}`;
                break;
            default:
                break;
        }
    }

    prepare_helpers () {
        SVGElement.prototype.getX = function () {
            return +this.getAttribute('x');
        };
        SVGElement.prototype.getY = function () {
            return +this.getAttribute('y');
        };
        SVGElement.prototype.getWidth = function () {
            return +this.getAttribute('width');
        };
        SVGElement.prototype.getHeight = function () {
            return +this.getAttribute('height');
        };
        SVGElement.prototype.getEndX = function () {
            return this.getX() + this.getWidth();
        };
    }

    draw () {
        if (this.x) {
            this.draw_bar();
            this.draw_resize_handles();
        }
    }

    draw_bar () {
        let rxy = this.task.tasktype === 'stage' ? this.stage_corner_radius : this.corner_radius;
        this.$bar = createSVG('rect', {
            x: this.milestoneBar ? this.x+this.polygon_w/2 : this.x,
            y: this.y,
            width: this.milestoneBar ? this.width - this.polygon_w : this.width,
            height: this.height,
            rx: this.task.is_milestone ? 0 : rxy,
            ry: this.task.is_milestone ? 0 : rxy,
            class: `bar ${this.otherBar} ${this.milestoneBar} ${this.task.biz_status}`,
            append_to: this.bar_group
        });
        if(this.task.is_milestone) {
            const {polygon_left, polygon_right, polygon_none_left, polygon_none_right} = createMile(this);
            this.$polygon_left = polygon_left;
            this.$polygon_right = polygon_right;
            if(!this.task._is_actual_bar) {
                this.$polygon_left_none = polygon_none_left;
                this.$polygon_right_none = polygon_none_right;
            }
        }
        animateSVG(this.$bar, 'width', 0, this.width);

        if (this.invalid) {
            this.$bar.classList.add('bar-invalid');
        }
    }

    draw_resize_handles () {
        this.draw_handle_line();
        if (this.task.progress) {
            if (!this.task.is_milestone) {
                this.draw_progress_bar();
            } else {
                draw_mile_progress_bar(this);
            }
        }
        this.draw_stage_mark();
        this.draw_label();
        this.draw_status();
    }

    draw_handle_line (isDrag) {
        if(this.gantt.projectTask.hasOwnProperty('gantt_allow_update_date') && !this.gantt.projectTask.gantt_allow_update_date) return;
        const bar = this.$bar;
        const handle_width = 8;
        if (this.task.tasktype === 'stage') {
            this.$handleLeft = createSVG('circle', {
                cx: +bar.getX(),
                cy: +bar.getY() + 3,
                r: 6,
                class: 'handle left stage_mark',
                append_to: this.handle_group
            });
            this.$handleRight = createSVG('circle', {
                // x: bar.getX() + bar.getWidth() - 9,
                cx: +bar.getX() + bar.getWidth(),
                cy: +bar.getY() + 3,
                r: 6,
                class: 'handle right stage_mark',
                append_to: this.handle_group
            });
        }else {
            const isActualEq = !isDrag ? this.task.isActualEq : bar.getWidth() === 0 ? true : false;
            const isPlanEq = !isDrag ? this.task.isPlanEq : bar.getWidth() === 0 ? true : false;
            let x_r = +bar.getX() + (+bar.getWidth()) - (this.task.is_milestone ? 3 : 9);
            let x_l = +bar.getX() + (this.task.is_milestone ? -2 : 3);
            x_l = ((this.invalid || this.task._has_actual_bar) ? isPlanEq : isActualEq) ? (this.task.is_milestone ? x_l-this.polygon_w/2-1 : x_l) : x_l;
            x_r = ((this.invalid || this.task._has_actual_bar) ? isPlanEq : isActualEq) ? (this.task.is_milestone ? x_r+this.polygon_w/2-1 : x_r) : x_r;

            this.$handleLeft = createSVG('rect', {
                x: x_l,
                y: +bar.getY() + 1,
                width: handle_width,
                height: this.height - 2,
                rx: this.corner_radius,
                ry: this.corner_radius,
                class: 'handle left',
                append_to: this.handle_group
            });
            this.$handleRight = createSVG('rect', {
                x: x_r,
                y: bar.getY() + 1,
                width: handle_width,
                height: this.height - 2,
                rx: this.corner_radius,
                ry: this.corner_radius,
                class: 'handle right',
                append_to: this.handle_group
            });
        }
    }

    draw_progress_bar () {
        if (this.invalid || this.task._has_actual_bar) return;
        let rxy = this.task.tasktype === 'stage' ? this.stage_corner_radius : this.corner_radius;
        this.$bar_progress = createSVG('rect', {
            x: this.x,
            y: this.y,
            width: this.progress_width,
            height: this.height,
            rx: rxy,
            ry: rxy,
            class: `bar-progress ${this.otherBarProgress} ${this.task.biz_status}`,
            append_to: this.bar_group
        });

        animateSVG(this.$bar_progress, 'width', 0, this.progress_width);
    }

    //阶段各状态图标
    draw_stage_status() {
        if (this.task.tasktype !== 'stage') {
            return
        }
        let label_x = this.x + this.width;
        let label_y = this.y - 4;
        this.$barForeignGroup = createSVG('foreignObject', {
            x: label_x,
            y: label_y,
            width: 100,
            height: 16,
            class: `bar-foreign-wrapper ${this.task._has_actual_bar ? 'actual' : 'plan'}`,
            append_to: this.bar_group
        });
        this.$barForeignGroup.innerHTML = this.update_status_icon('stage');
        this.gantt.update_view_plan_actual(this.gantt.projectTask.selectPlanActual?.selectvalue);
    }

     //阶段bar的两个端点
     draw_stage_mark () {
        if (this.task.tasktype != 'stage') {
            return
        }
        let x2 = this.x + this.width;
        this.$stage_mark_left = createSVG('circle', {
            cx: this.x,
            cy: this.y + 3,
            r: 5,
            class: 'stage_mark left',
            append_to: this.bar_group
        });
        this.$stage_mark_right = createSVG('circle', {
            cx: x2,
            cy: this.y + 3,
            r: 5,
            class: 'stage_mark right',
            append_to: this.bar_group
        });
    }

    draw_label () {
        this.$bar.classList.add('light-progress');
        const isNotDraw = function(type, task) {
            switch (type) {
                case "label":
                    return task.biz_status !== "unstarted" && task.biz_status !== "ongoing";
                case "status":
                    return task.biz_status !== "completed";
                default:
                    return false;
            
            }
        }
        if (!this.task.progress || this.task.tasktype == 'stage' || this.task._has_actual_bar || isNotDraw('label', this.task)) {
            return
        }
        const label_poly_x = this.$bar.getX() + this.$bar.getWidth();
        this.$bar_label = createSVG('text', {
            x: this.task.is_milestone ? label_poly_x - (this.task.isActualEq || this.task.isPlanEq ? 0 : 5) : label_poly_x - this.polygon_w/2,
            y: this.y + this.height / 2,
            innerHTML: parseInt(this.task.progress) + '%',
            class: `bar-label ${this.milestoneBar}`,
            append_to: this.bar_group
        });
    }

    draw_status () {
        if(this.task.tasktype == 'stage') return this.draw_stage_status();
        if (!this.task.progress || this.task._has_actual_bar || this.task.biz_status === 'ongoing' || this.task.biz_status === 'unstarted') {
            return;
        };
        let status_poly_x = this.$bar.getX() + this.$bar.getWidth();
        let status_dis = this.$bar.getWidth() ? 8 : 7;
        let cx = this.task.is_milestone ? status_poly_x - status_dis : (status_poly_x - 16);
        let cy = this.y + (this.height - 14)/2;
        this.$barStatusWrapper = createSVG('g', {
            x: cx,
            y: cy,
            class: 'bar-status-wrapper',
            append_to: this.bar_group
        });
        this.$barForeignWrapper = createSVG('foreignObject', {
            x: cx,
            y: cy,
            width: 14,
            height: 14,
            class: 'bar-foreign-wrapper',
            append_to: this.$barStatusWrapper
        });
        
        this.$barForeignWrapper.innerHTML = this.update_status_icon('task');
        this.gantt.update_view_plan_actual(this.gantt.projectTask.selectPlanActual?.selectvalue);
    }

    //状态图标标识
    update_status_icon (barType) {
        let className = '',stage_label = '';
        const overdueProgress = this.task.progress === 100 ? 'overdue' : '';
        switch (this.task.biz_status) {
            case 'unstarted':
                className ="fx-icon-time";
                stage_label = $t('未开始');
                break;
            case 'ongoing':
                className ="fx-icon-time  ongoing";
                stage_label = $t('进行中');
                break;
            case 'completed':
                className ="fx-icon-ok";
                stage_label = $t('已完成');
                break;
            case 'pause':
                className ="fx-icon-zt";
                stage_label = $t('已暂停');
                break;
            case 'cancel':
                className ="fx-icon-close-2";
                stage_label = $t('已取消');
                break;
        };
        return `<span class="${className} ${barType} ${overdueProgress} icon-status"></span><span class="icon-hide ${this.task.biz_status} ${barType}">${stage_label}</span>`;
    }

    bind () {
        this.setup_click_event();
    }

    setup_click_event () {
        const _this = this;
        let popup_off = this.gantt.options.popup_off;
        if (popup_off) {
            $.on(this.group, popup_off, e => {
                if (_this.action_completed && !_this.task.isOn) return;
                clearTimeout(_this.gantt.hide_popup_timer);
                clearTimeout(_this.gantt.hide_popup_timer);
                _this.gantt.hide_popup_timer = setTimeout(() => {
                    _this.gantt.hide_popup('bar');
                    _this.gantt.unselect_all();
                }, 300);
                return;
            });
        }

        $.on(this.group, 'focus ' + this.gantt.options.popup_trigger, e => {
            if (_this.action_completed && !_this.task.isOn) return;
            _this.show_popup(e);
            _this.gantt.unselect_all();
            _this.group.classList.add('active');
        });

        $.on(this.group, 'click', e => {
            if (_this.action_completed && !_this.task.isOn) return;
            _this.gantt.trigger_event('click', [_this.task]);
        });
    }
    show_popup (e) {
        if (this.gantt.bar_being_dragged) return;
        const subtitle = this.task.start + ' - ' + this.task.end;
        let left = e.clientX;
        let top = this.$bar.getBoundingClientRect().bottom;
        this.gantt.show_popup('bar', {
            target_element: this.$bar,
            title: this.task.name,
            subtitle: subtitle,
            position: 'follow',
            position_data: {
                left: left,
                top: top
            },
            data: {
                task: this.task
            },
            view_mode: this.gantt.options.view_mode
        });
    }

    update_bar_position({ x = null, width = null, is_dragging, is_resizing_left = null, is_resizing_right = null }, isHasFill, isMouseUp) {
        const bar = this.$bar;
        const column_w = this.gantt.options.column_width;
        const isHasProgress = !!this.$bar_progress;
        let isChangeLeft = false;
        let isChangeRight = false;
        const stageBar = bar.classList.contains('stageBar');
        const isHasCircle = !!this.$barStatusWrapper;

        // 计算新的 x 坐标
        x = this.compute_x_fun(is_resizing_left, isMouseUp, bar, x);
        // 计算新的宽度
        width = isMouseUp ? Math.ceil(bar.getWidth() / column_w) * column_w : width;
        // 计算新的进度宽度
        const pro_w = (width + column_w) * (this.task.progress / 100) || 0;
        const bar_width = this.set_bar_progress(pro_w, width);

        // 处理拖动
        if (is_dragging && x) {
            this.update_drag_position(bar, x, isHasProgress);
            if (bar.is_milestone) {
                this.update_milestone_positions(bar, 'left');
                this.update_milestone_positions(bar, 'right');
                if (!this.task._is_actual_bar) {
                    this.update_milestone_positions(bar, 'left', 'none');
                    this.update_milestone_positions(bar, 'right', 'none');
                }
                isHasProgress && this.remove_Progress_polygon();
            }
        }

        // 处理左侧调整大小
        if (x && (bar.is_milestone ? width >= 0 : width >= 30) && is_resizing_left) {
            this.update_resize_left(bar, x, width, isHasProgress, bar_width);
            if (bar.is_milestone) {
                this.update_milestone_positions(bar, 'left');
                if (!this.task._is_actual_bar) {
                    this.update_milestone_positions(bar, 'left', 'none');
                }
                isHasProgress && this.remove_Progress_polygon();
            }
            isChangeLeft = true;
        }

        // 处理右侧调整大小
        if ((bar.is_milestone ? width >= 0 : width >= 30) && is_resizing_right) {
            this.update_resize_right(bar, width, isHasProgress, bar_width);
            if (bar.is_milestone) {
                this.update_milestone_positions(bar, 'right');
                if (!this.task._is_actual_bar) {
                    this.update_milestone_positions(bar, 'right', 'none');
                }
                isHasProgress && this.remove_Progress_polygon();
            }
            isChangeRight = true;
        }

        // 处理阶段任务
        if (stageBar) {
            this.update_stage_position(x, width, isChangeLeft, isChangeRight, is_dragging, isHasFill, isMouseUp);
            return;
        }

        // 更新状态图标
        if (isHasCircle && this.$barStatusWrapper) {
            this.$barStatusWrapper.remove();
            this.draw_status();
        }

        // 移除状态句柄
        if (isChangeLeft || isChangeRight || (is_dragging && x)) {
            this.remove_status_handles(isChangeLeft);
        }

        // 更新箭头位置
        this.update_arrow_position();

        // 更新任务日期
        if (isMouseUp) {
            const { new_start_date, new_end_date } = this.compute_start_end_date();
            const start_date = date_utils.format(new_start_date, 'YYYY-MM-DD');
            const end_date = date_utils.format(new_end_date, 'YYYY-MM-DD');
            this.task.start = start_date;
            this.task.end = end_date;
            this.task._start = new_start_date;
            this.task._end = new_end_date;
            this.gantt.trigger_event('date_change', [this.task, this.task._start, this.task._end, isHasFill]);
        }
    }

    update_drag_position(bar, x, isHasProgress) {
        this.x = x;
        this.update_attr(bar, 'x', x);
        if (isHasProgress) {
            this.update_attr(this.$bar_progress, 'x', x);
        }
    }

    update_resize_left(bar, x, width, isHasProgress, bar_width) {
        this.x = x;
        this.width = width;
        this.update_attr(bar, 'x', x);
        this.update_attr(bar, 'width', width);
        if (isHasProgress) {
            this.update_attr(this.$bar_progress, 'x', x);
            this.update_attr(this.$bar_progress, 'width', bar_width);
        }
    }

    update_resize_right(bar, width, isHasProgress, bar_width) {
        this.x = bar.getX();
        this.width = width;
        this.update_attr(bar, 'width', width);
        if (isHasProgress) {
            this.update_attr(this.$bar_progress, 'width', bar_width);
        }
    }

    update_milestone_positions(bar, side, type = '') {
        update_mile_position(this.polygon_w, bar, this[`$polygon_${side}${type ? `_${type}` : ''}`], side, type);
    }

    //计算当前任务横坐标
    compute_x_fun(left, isMouseUp, bar, x) {
        const column_w = this.gantt.options.column_width;
        let desc = bar.is_milestone ? (bar.getX() - column_w/2)/column_w : (bar.getX() / column_w);
        let bar_x = (left ? Math.floor(desc) : Math.ceil(desc)) * column_w + (bar.is_milestone ? column_w/2 : 0);
        return isMouseUp ? bar_x : x;
    }

    set_bar_progress(pro_w, width) {
        let bar_width = 0;
        const column_width = this.gantt.options.column_width;
        if(pro_w <= column_width/2) {
            bar_width = 0;
        }else if(pro_w > column_width/2 && pro_w <= column_width/2 + width) {
            bar_width = pro_w - column_width/2;
        }else {
            bar_width = width;
        }
        return bar_width;
    }

    remove_status_handles (isChangeLeft) {
        if (!isChangeLeft && this.$bar_label) {
            this.$bar_label.remove();
            this.draw_label();
        };
        this.$handleLeft.remove();
        this.$handleRight.remove();
        this.draw_handle_line('isDrag');
    }

    update_stage_position (x, width, isChangeLeft, isChangeRight, is_dragging, isHasFill, mouseUp) {
        const bar = this.$bar;
        if (isChangeLeft) {
            //左侧拉伸
            this.width = width;
            this.x = x;
            this.update_attr(this.$stage_mark_left, 'cx', x);
            this.update_attr(this.$handleLeft, 'cx', x);
        } else if (isChangeRight && width && width > 35) {
            //右侧拉伸
            this.width = width;
            this.update_attr(this.$stage_mark_right, 'cx', bar.getEndX());
            this.update_attr(this.$handleRight, 'cx', bar.getEndX());
        } else if (is_dragging && x) {
            //拖动
            this.x = x;
            this.update_attr(this.$stage_mark_left, 'cx', x);
            this.update_attr(this.$stage_mark_right, 'cx', bar.getEndX());
            this.update_attr(this.$handleLeft, 'cx', x);
            this.update_attr(this.$handleRight, 'cx', bar.getEndX());
        }
        this.remove_stage_status(isChangeLeft);
        mouseUp && this.update_stage_pop_date(isChangeLeft, isChangeRight, is_dragging, isHasFill);
    }

    remove_stage_status(isChangeLeft) {
        if(!isChangeLeft && this.$barForeignGroup) {
            this.$barForeignGroup.remove();
            this.draw_stage_status();
        }
    }

    //显示拖动日期框
    show_time_popup ({
        clientX = null,
        is_left = false,
        is_right = false,
        drag = false
    }) {
        const {new_start_date, new_end_date} = this.compute_start_end_date();
        let start_date = date_utils.format(new_start_date, 'YYYY-MM-DD');
        let end_date = date_utils.format(new_end_date, 'YYYY-MM-DD');
        const subtitle = `${start_date}－${end_date}`;
        let left = clientX;
        let top = this.$bar.getBoundingClientRect().bottom;
        this.task.start = drag || is_left ? start_date : this.task.start;
        this.task.end = drag || !is_left ? end_date : this.task.end;
        this.gantt.show_popup('bar', {
            target_element: this.$bar,
            title: '',
            subtitle: subtitle,
            position: 'follow',
            position_data: { left, top },
            isChangeLeft: is_left,
            isChangeRight: is_right,
            drag,
            mousemove: true,
            data: { task: this.task },
            view_mode: this.gantt.options.view_mode
        })
    }
    // 任务日期发生变化时的处理
    date_changed (isHasFill, dragObj) {
        const { new_start_date, new_end_date } = this.compute_start_end_date();
        if (Number(this.task._start) !== Number(new_start_date)) {
            this.task._start = new_start_date;
        }

        if (Number(this.task._end) !== Number(new_end_date)) {
            this.task._end = new_end_date;
        }
        CRM.util.waiting(false);
        this.update_bar_position(dragObj, isHasFill, true);
    }

    // 更新阶段任务的日期弹出框
    update_stage_pop_date(isChangeLeft, isChangeRight, is_dragging, isHasFill) {
        const { new_start_date, new_end_date } = this.compute_start_end_date();
        const start_date = date_utils.format(new_start_date, 'YYYY-MM-DD');
        const end_date = date_utils.format(new_end_date, 'YYYY-MM-DD');
    
        if (is_dragging) {
            this.task.start = start_date;
            this.task.end = end_date;
        } else if (isChangeLeft) {
            this.task.start = start_date;
        } else {
            this.task.end = end_date;
        }
    
        this.task._start = new_start_date;
        this.gantt.trigger_event('date_change', [this.task, this.task._start, this.task._end, isHasFill]);
    }

    progress_changed () {
        const new_progress = this.compute_progress();
        this.task.progress = new_progress;
        this.gantt.trigger_event('progress_change', [this.task, new_progress]);
    }

    compute_start_end_date () {
        const bar = this.$bar;
        const x_in_units = (bar.is_milestone ? bar.getX ()- this.gantt.options.column_width/2 : bar.getX()) / this.gantt.options.column_width;
        const new_start_date = date_utils.add(
            this.gantt.gantt_start,
            x_in_units * this.gantt.options.step,
            'hour'
        );
        let width_in_units = ((bar.is_milestone ? bar.getWidth() + this.gantt.options.column_width : bar.getWidth()) - 0.4) / this.gantt.options.column_width;
        let new_end_date = date_utils.add(
            new_start_date,
            width_in_units * this.gantt.options.step,
            'hour'
        );
        return {
            new_start_date,
            new_end_date
        };
    }

    compute_progress () {
        const progress =
            this.$bar_progress.getWidth() / this.$bar.getWidth() * 100;
        return parseInt(progress, 10);
    }
    // 计算任务的 x 坐标
    compute_x () {
        const {
            step,
            column_width
        } = this.gantt.options;
        const task_start = this.task._start;
        const gantt_start = this.gantt.gantt_start;

        const diff = date_utils.diff(task_start, gantt_start, 'hour');
        let x = diff / step * column_width;

        if (this.gantt.view_is('Month')) {
            const diff = date_utils.diff(task_start, gantt_start, 'day');
            x = diff * column_width / 30;
        }
        return x;
    }

    // 计算任务的 y 坐标
    compute_y () {
        const padding = this.task.tasktype === 'stage'? this.gantt.options.stage_padding : this.gantt.options.padding;
        return (
            padding/2 +
            this.task._index * (this.height + padding)
        );
    }

    compute_polygon_w () {
        let width = this.gantt.options.view_mode === 'Week' ? this.gantt.options.column_width/7 : this.gantt.options.view_mode === 'Month' ? this.gantt.options.column_width/30: this.gantt.options.column_width;
        return width;
    }

    update_attr (element, attr, value) {
        value = +value;
        if (!isNaN(value)) {
            element.setAttribute(attr, value);
        }
        return element;
    }

    remove_Progress_polygon () {
        this.mile_group.remove();
        this.$bar_progress.remove();
        draw_mile_progress_bar(this);
    }

    update_arrow_position () {
        this.arrows = this.arrows || [];
        for (let arrow of this.arrows) {
            arrow.update();
        }
    }
}
