class UnitOptionsCache {

    constructor() {
        this.cache = {};
    }

    getOptions(productId) {
        return productId && this.cache[productId];
    }

    updateOptions(productId, options) {
        this.cache[productId] = options;
    }

    clear() {
        this.cache = {};
    }

    async cacheOptions(productIds, batchGetLazyLoadOptions) {
        let filterProductIds = productIds && productIds.length && productIds.filter(productId => {
            if (!productId) {
                return false;
            }
            let options = this.getOptions(productId);
            return !(options && options.length)
        })
        if (!filterProductIds || !filterProductIds.length) {
            return;
        }
        let result = await batchGetLazyLoadOptions(filterProductIds).catch(err => {
            console.log(err);
        });
        if (result && result.length) {
            result.forEach(it => {
                let {productId, options} = it;
                this.updateOptions(productId, options);
            })
        }
    }
}

export default new UnitOptionsCache();