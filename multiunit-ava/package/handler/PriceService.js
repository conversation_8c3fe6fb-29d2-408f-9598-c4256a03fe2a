export class PriceService {

    constructor(context) {
        let {fieldMapping, bizStateConfig} = context || {};
        this.fieldMapping = fieldMapping;
        this.bizStateConfig = bizStateConfig;
    }

    priceServiceBatchSelectSkuConfigSync(pluginExecResult, options) {
        let preData = pluginExecResult && pluginExecResult.preData || {};
        let openMultiUnitPriceBook = this.bizStateConfig.isOpenMultiUnitPriceBook();
        let multiUnitConfig = this.bizStateConfig.getMultiUnitConfig();
        return Object.assign({}, preData, {
            isOpenMultiUnit: true,//开启了多单位标识
            isOpenMultiUnitPriceBook: openMultiUnitPriceBook,//是否开启了多单位价目表定价
            multiUnitConfig,
        });
    }

    priceServiceBatchAddGetPriceParamSync(pluginExecResult, options) {
        let {params, lookupDatas} = options;
        params && params.fullProductList && params.fullProductList.length && params.fullProductList.forEach((it, index) => {
            let lookupData = lookupDatas && lookupDatas.length && lookupDatas[index];
            let {object_describe_api_name, unit, unit__v, actual_unit} = lookupData || {};
            let isProduct = object_describe_api_name === 'ProductObj';
            let unitId = isProduct ? unit : (actual_unit || unit__v);
            let baseUnit = isProduct ? unit : unit__v;
            Object.assign(it, {unit: unitId, baseUnit: baseUnit,})
        })
    }

    priceServiceFormParseFullProductSync(pluginExecResult, options) {
        let preData = pluginExecResult && pluginExecResult.preData || {};
        let {objApiName, detailData} = options;
        let {unit, actual_unit} = this.fieldMapping.getDetailFields(objApiName);
        let {[`${unit}__v`]: unitId, [actual_unit]: actualUnit} = detailData;
        return Object.assign({}, preData, {unit: actualUnit, baseUnit: unitId});
    }
}