import {
    formatQuantityDecimalPlaces,
    getPriceBookData,
    getRealPriceResult,
    getRowId,
    isMultipleUnit,
    multiUnitPlacesDecimal
} from "../utils";
import {divide, equals, formatDataDecimalPlaces, isEmpty, multiply} from "../../../pluginbase-ava/package/pluginutils";
import {emitEvent} from "../events";
import unitOptionsCache from "../unitOptionsCache";

const key_other_unit_id = 'key_other_unit_id';//其他单位id
export class MdBatchAdd {

    constructor(context) {
        let {fieldMapping, bizStateConfig, pluginApi, requestApi} = context || {};
        this.fieldMapping = fieldMapping;
        this.bizStateConfig = bizStateConfig;
        this.pluginApi = pluginApi;
        this.requestApi = requestApi;
    }

    async mdBatchAddAfter(pluginExecResult, options) {
        let {dataGetter, lookupDatas, lookupField, objApiName, masterObjApiName, newDatas, seriesId} = options || {};
        let {
            product_id, price_book_product_id, other_unit, quantity, price: product_price, price_book_price,
            is_multiple_unit, actual_unit, conversion_ratio, base_unit_count, stat_unit_count
        } = this.fieldMapping.getDetailFields(objApiName);
        if (![product_id, price_book_product_id].includes(lookupField && lookupField.api_name)) {//选择的不是产品也不是价目表产品不处理
            return;
        }
        /* 对lookupDatas中的unit__v字段进行一次补偿，web端通过商品选价目表产品时，返回的数据中没有unit__v
        https://www.tapd.cn/21309261/bugtrace/bugs/view/1121309261001278874 */
        lookupDatas && lookupDatas.length && lookupDatas.forEach(it => {
            let {unit, unit__v, object_describe_api_name} = it;
            if (object_describe_api_name === 'PriceBookProductObj') {
                if (unit__v === undefined || unit__v === null) {
                    it.unit__v = it.product_id__ro ? it.product_id__ro.unit__v : unit;
                }
            } else if (object_describe_api_name === "ProductObj") {
                if (unit__v) {//通过商品选产品，web端返回的unit的label，unit__v是code，兼容下
                    it.unit = unit__v;
                }
            }
        });
        let detailObjDescribe = dataGetter && dataGetter.getDescribe(objApiName);
        let otherUnitField = detailObjDescribe && detailObjDescribe.fields && detailObjDescribe.fields[other_unit];
        let quantityField = detailObjDescribe && detailObjDescribe.fields && detailObjDescribe.fields[quantity];
        let quantityDecimalPlaces = quantityField && quantityField.decimal_places || 0;
        await this.fillOtherUnit(otherUnitField, lookupDatas, objApiName);
        let calcResult;
        let params = this.buildParams(lookupDatas);
        if (params && params.length) {
            let param = {
                params,
                masterObjectApiName: masterObjApiName,
                detailObjectApiName: objApiName,
                seriesId
            };
            this.pluginApi.runPluginSync(emitEvent.multiUnitCalcPriceBeforeSync, Object.assign({}, options, {
                param,
                type: 'batchAdd',
                lookupDatas
            }));
            calcResult = await this.requestApi.calc(param).catch(err => {
                this.pluginApi.showToast(err);
            });
        }
        newDatas && newDatas.length && newDatas.forEach((newData, index) => {
            let lookupData = lookupDatas[index];
            let multiUnitResult = this.getMultiUnitResult(lookupData, calcResult, detailObjDescribe);
            let updateData = this.toMultiUnitData(multiUnitResult, objApiName) || {};
            let updateQuantity = !isEmpty(lookupData._selected_num) && isEmpty(updateData[quantity]);
            let isMultiUnit = isMultipleUnit(lookupData, 'is_multiple_unit');
            let filterFields = [];
            if (isMultiUnit) {//按多单位设置的小数位数格式化数量
                let inputQuantity = updateQuantity ? lookupData._selected_num : updateData[quantity];
                let {[multiUnitPlacesDecimal]: unitDecimalPlaces} = updateData || {};
                let formatQuantity = formatQuantityDecimalPlaces(inputQuantity, quantityDecimalPlaces, unitDecimalPlaces);
                Object.assign(updateData, {[quantity]: formatQuantity});
                filterFields.push(quantity);
            }
            formatDataDecimalPlaces(updateData, detailObjDescribe, filterFields);
            Object.assign(newData, updateData, {
                [`${is_multiple_unit}__v`]: isMultiUnit
            });
            //更新子产品多单位信息
            let {key_sub_products_selected_in_product: subProducts} = lookupData;
            subProducts && subProducts.length && subProducts.forEach(subProduct => {
                let result = this.getMultiUnitResultBySubProduct(lookupData, subProduct, calcResult, detailObjDescribe);
                let {unitId: actualUnit, conversion_ratio: conversionRatio, base_unit_count: baseUnitCount, stat_unit_count: statUnitCount} = result;
                subProduct.multiUnitResult = {
                    [actual_unit]: actualUnit,
                    [conversion_ratio]: conversionRatio,
                    [base_unit_count]: baseUnitCount,
                    [stat_unit_count]: statUnitCount
                };
            })
        });
        return this.getNoCalFieldsBatchAdd(pluginExecResult, options);
    }

    mdBatchAddEnd(pluginExecResult, options) {
        let {newDatas} = options;
        this.setDetailDataReadonlyFields(newDatas, options);
    }

    mdCloneAfter(pluginExecResult, options) {
        return this.getNoCalFieldsBatchAdd(pluginExecResult, options);
    }

    mdCloneEnd(pluginExecResult, options) {
        let {newDatas} = options;
        this.setDetailDataReadonlyFields(newDatas, options);
    }

    async fillOtherUnit(otherUnitField, selectedList) {
        if (!otherUnitField) {
            return;
        }
        let productIds = selectedList && selectedList.length && selectedList.map(skuData => {
            let {object_describe_api_name, _id, product_id} = skuData;
            return object_describe_api_name === 'ProductObj' ? _id : product_id;
        });
        if (!productIds || !productIds.length) {
            return;
        }
        await unitOptionsCache.cacheOptions(productIds, this.requestApi.batchGetLazyLoadOptions.bind(this.requestApi));
        selectedList.forEach(skuData => {
            let {object_describe_api_name, _id, product_id, unit, unit__v, actual_unit} = skuData;
            let isProduct = object_describe_api_name === 'ProductObj';
            let productId = isProduct ? _id : product_id;
            let unitId = isProduct ? unit : (actual_unit || unit__v);
            let options = unitOptionsCache.getOptions(productId);
            let otherUnitId = options && options.length && options.find(option => option.value !== unitId);
            skuData[key_other_unit_id] = otherUnitId && otherUnitId.value;
        });
    }

    buildParams(lookupDataList) {
        let skuParams = this.buildParamBySku(lookupDataList);
        let subProductParams = this.buildParamBySubProduct(lookupDataList);
        return [...(skuParams || []), ...(subProductParams || [])];
    }

    buildParamBySku(lookupDataList) {
        return lookupDataList && lookupDataList.length && lookupDataList.filter(it => {
            return isMultipleUnit(it, 'is_multiple_unit');
        }).map(it => {
            let {
                _id, product_id, unit, unit__v, actual_unit, object_describe_api_name, _selected_num, [key_other_unit_id]: otherUnitId,
                [getRealPriceResult]: getRealPriceResultData
            } = it;
            let _priceBookProductId = this.getPriceBookProductId(getRealPriceResultData);
            let isProduct = object_describe_api_name === 'ProductObj';
            let productId = isProduct ? _id : product_id;
            let priceBookProductId = isProduct ? _priceBookProductId : _id;
            let unitId = isProduct ? unit : (actual_unit || unit__v);
            let rowId = getRowId(it, true);
            return {productId, priceBookProductId, unitId, count: _selected_num || 1, otherUnitId, rowId};
        });
    }

    buildParamBySubProduct(lookupDataList) {
        let subProductParams = [];
        lookupDataList && lookupDataList.forEach(skuData => {
            let {_selected_num, key_sub_products_selected_in_product: subProducts} = skuData;
            let rootQuantity = _selected_num || 1;
            subProducts && subProducts.length && subProducts.forEach(subProduct => {
                let multiUnit = isMultipleUnit(subProduct, 'is_multiple_unit');
                if (multiUnit) {
                    let {amount, modified_adjust_amount, product_id, price_book_product_id, unit_id} = subProduct;
                    let rowId = getRowId(subProduct, true);
                    let subQuantity = modified_adjust_amount || amount || 1;
                    subProductParams.push({
                        productId: product_id,
                        priceBookProductId: price_book_product_id,
                        unitId: unit_id,
                        count: multiply(subQuantity, rootQuantity),
                        rowId
                    })
                }
            })
        });
        return subProductParams;
    }

    getMultiUnitResult(selected, multiUnitResults, objectDescribe) {
        let {
            unit, unit__v, actual_unit, object_describe_api_name, price, pricebook_sellingprice, pricebook_price, _selected_num,
            [getRealPriceResult]: getRealPriceResultData, stand_price
        } = selected;
        let isProduct = object_describe_api_name === 'ProductObj';
        let unitId = isProduct ? unit : (actual_unit || unit__v);
        let orgRowId = getRowId(selected);
        let result = multiUnitResults && multiUnitResults.length && multiUnitResults.find(it => {
            return orgRowId === (it && it.rowId);
        });
        if (!result) {//如果没有找到计算结果，则特殊创建一个计算结果
            let {pricebook_price: _priceBookPrice, selling_price: sellingPrice} = getRealPriceResultData || {};
            let priceBookPrice = isEmpty(_priceBookPrice) ? (isProduct ? undefined : pricebook_price) : _priceBookPrice;
            let realPrice = isEmpty(sellingPrice) ? (isProduct ? price : pricebook_sellingprice) : sellingPrice;
            let count = _selected_num || 1;
            let {api_name: objApiName, fields} = objectDescribe || {};
            let {actual_unit} = this.fieldMapping.getDetailFields(objApiName);
            let actualUnitField = fields && fields[actual_unit];
            let selectOption = actualUnitField && actualUnitField.options && actualUnitField.options.find(it => it.value === unitId);
            let unitLabel = selectOption && selectOption.label || '';
            let stat_unit_count = (count + unitLabel);
            result = {
                price: realPrice,
                unitId,
                conversion_ratio: 1,
                base_unit_count: count,
                stat_unit_count,
                priceBookPrice: priceBookPrice,
                count: _selected_num,
                basic_price_backup: stand_price,
            }
        } else if (result.conversion_ratio && Number(result.conversion_ratio) > 0) {
            result.basic_price_backup = divide(isProduct ? selected.price : stand_price, result.conversion_ratio)
        }
        return result;
    }

    getMultiUnitResultBySubProduct(skuData, subProduct, multiUnitResults, objectDescribe) {
        let {unit_id, modified_adjust_amount, amount} = subProduct;
        let orgRowId = getRowId(subProduct);
        let result = multiUnitResults && multiUnitResults.length && multiUnitResults.find(it => {
            return orgRowId === (it && it.rowId);
        });
        if (!result) { //如果没有找到计算结果，则特殊创建一个计算结果
            let rootQuantity = skuData && skuData._selected_num || 1;
            let subQuantity = modified_adjust_amount || amount || 1;
            let count = multiply(subQuantity, rootQuantity);
            let {api_name: objApiName, fields} = objectDescribe || {};
            let {actual_unit} = this.fieldMapping.getDetailFields(objApiName);
            let actualUnitField = fields && fields[actual_unit];
            let selectOption = actualUnitField && actualUnitField.options && actualUnitField.options.find(it => it.value === unit_id);
            let unitLabel = selectOption && selectOption.label || '';
            let stat_unit_count = (count + unitLabel);
            result = {
                unitId: unit_id,
                conversion_ratio: 1,
                base_unit_count: count,
                stat_unit_count,
            }
        }
        return result;
    }

    getPriceBookProductId(getRealPriceResult) {
        if (getRealPriceResult) {
            let {_id, object_describe_api_name} = getRealPriceResult;
            return object_describe_api_name === 'PriceBookProductObj' ? _id : null;
        }
    }

    setDetailDataReadonlyFields(objectDataList, options) {
        let {dataUpdater, objApiName} = options;
        let {actual_unit, other_unit, is_multiple_unit} = this.fieldMapping.getDetailFields(objApiName);
        objectDataList && objectDataList.forEach(objectData => {
            let isMultiUnit = isMultipleUnit(objectData, is_multiple_unit);
            dataUpdater && dataUpdater.setReadOnly && dataUpdater.setReadOnly({
                objApiName,
                dataIndex: objectData.dataIndex,
                fieldName: [actual_unit, other_unit],
                status: !isMultiUnit,
                biz: 'multi-unit',
                priority: 11
            });
        });
    }

    toMultiUnitData(multiUnitResult, objApiName) {
        let {
            price, count, unitId,
            base_unit_count: baseUnitCount,
            conversion_ratio: conversionRatio,
            stat_unit_count: statUnitCount,
            other_unit: otherUnit,
            other_unit_quantity: otherUnitQuantity,
            priceBookPrice,
            basic_price_backup,
            places_decimal = null,
        } = multiUnitResult || {};
        let detailFieldMap = this.fieldMapping.getDetailFields(objApiName);
        let {
            price: product_price, quantity,
            actual_unit, conversion_ratio, base_unit_count, stat_unit_count, other_unit, other_unit_quantity,
            price_book_price,
        } = detailFieldMap;
        let result = {
            [product_price]: price,
            [actual_unit]: unitId,
            [base_unit_count]: baseUnitCount,
            [conversion_ratio]: conversionRatio,
            [stat_unit_count]: statUnitCount,
            [other_unit]: otherUnit,
            [other_unit_quantity]: otherUnitQuantity,
            [multiUnitPlacesDecimal]: places_decimal,
            [price_book_price]: priceBookPrice,
            basic_price_backup
        };
        if (count) {
            result[quantity] = count;
        }
        let priceBookData = getPriceBookData(multiUnitResult, detailFieldMap);
        return Object.assign(result, priceBookData);
    }

    getNoCalFieldsBatchAdd(pluginExecResult, options) {
        let {dataGetter, objApiName} = options || {};
        let detailObjDescribe = dataGetter && dataGetter.getDescribe(objApiName);
        let preData = pluginExecResult && pluginExecResult.preData;
        let preObjectFilterFields = preData
            && preData.extraCalUiParams
            && preData.extraCalUiParams.filterFields
            && preData.extraCalUiParams.filterFields[objApiName];
        let {price_book_product_id, quantity, price: product_price, price_book_price, price_book_discount} = this.fieldMapping.getDetailFields(objApiName);
        let noCalFields = [quantity, product_price];
        let isOpenPriceBook = this.bizStateConfig.isOpenPriceBook();
        if (isOpenPriceBook) {
            let priceBookPriceField = detailObjDescribe && detailObjDescribe.fields && detailObjDescribe.fields[price_book_price];
            let variable1 = `$${price_book_product_id}__r.pricebook_sellingprice$`;
            let variable2 = `$${price_book_product_id}__r.discount$`;
            let variable3 = `$${product_price}$`;//$product_price$*$price_book_discount$
            let variable4 = `$${price_book_discount}$`;
            let defaultFormula = priceBookPriceField && priceBookPriceField.default_value;
            if (equals(`${variable1}*${variable2}`, defaultFormula)
                || equals(`${variable3}*${variable4}`, defaultFormula)) {
                noCalFields.push(price_book_price)
            }
        }
        return Object.assign({}, preData, {
            extraCalUiParams: Object.assign({}, preData && preData.extraCalUiParams, {
                filterFields: Object.assign({}, preData && preData.extraCalUiParams && preData.extraCalUiParams.filterFields,
                    {[objApiName]: [...(preObjectFilterFields || []), ...noCalFields]})
            })
        })
    }
}