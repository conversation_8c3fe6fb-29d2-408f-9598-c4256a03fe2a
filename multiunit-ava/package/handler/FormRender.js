import {isMultipleUnit} from "../utils";
import {BatchCalc} from "./BatchCalc";
import {isConvert, isEmpty} from "../../../pluginbase-ava/package/pluginutils";
import log from "../../../pluginbase-ava/package/log";

export class FormRender {

    constructor(context) {
        let {fieldMapping} = context || {};
        this.fieldMapping = fieldMapping;
        this.batchCalc = new BatchCalc(context);
    }

    async formRenderEnd(pluginExecResult, options) {
        let {dataGetter} = options;
        let {getDetail, getSourceAction} = dataGetter || {};
        let objApiName = this.fieldMapping.getFirstDetailObjApiName();
        let detailDataList = getDetail && getDetail(objApiName);
        this.batchSetFieldReadonly(options, detailDataList);
        //映射到销售订单时，订单产品要进行多单位取价
        let sourceAction = getSourceAction && getSourceAction();
        let isConvertMode = isConvert(sourceAction);
        if (objApiName === 'SalesOrderProductObj' && isConvertMode) {
            let triggerDataIndexList = detailDataList && detailDataList.length && detailDataList.map(it => it.dataIndex);
            await this.batchCalc.calc(pluginExecResult, Object.assign(options, {triggerDataIndexList}));
        }
    }

    formSubmitPostBefore(pluginExecResult, options) {
        let data = options && options.postParams && options.postParams.data;
        if (isEmpty(data)) {
            return;
        }
        let detailObjApiName = this.fieldMapping.getFirstDetailObjApiName();
        let {object_data, details} = data || {};
        let detailDataList = details && details[detailObjApiName];
        this.dataCheckError(object_data, detailDataList);
    }

    batchSetFieldReadonly(options, detailDataList) {
        let {dataUpdater, objApiName} = options;
        objApiName = objApiName || this.fieldMapping.getFirstDetailObjApiName();
        let detailFieldMap = this.fieldMapping.getDetailFields(objApiName);
        let {actual_unit, other_unit, is_multiple_unit} = detailFieldMap;
        detailDataList && detailDataList.length && detailDataList.forEach(detailData => {
            let isMultiUnit = isMultipleUnit(detailData, is_multiple_unit);
            dataUpdater && dataUpdater.setReadOnly && dataUpdater.setReadOnly({
                objApiName,
                dataIndex: detailData.dataIndex,
                fieldName: [actual_unit, other_unit],
                status: !isMultiUnit,
                biz: 'multi-unit',
                priority: 11
            });
        });
    }

    /**
     * 校验数据错误
     * 1、多单位产品的actual_unit、conversion_ratio、base_unit_count、stat_unit_count为空
     */
    dataCheckError(masterData, detailDataList) {
        if (isEmpty(masterData) || !detailDataList || !detailDataList.length) {
            return;
        }
        let objApiName = this.fieldMapping.getFirstDetailObjApiName();
        let detailFieldMapping = this.fieldMapping.getDetailFields(objApiName);
        let {actual_unit, conversion_ratio, base_unit_count, stat_unit_count, is_multiple_unit} = detailFieldMapping || {};
        let {_id, object_describe_api_name} = masterData;
        let multipleUnitDataList = detailDataList.filter(it => {
            return isMultipleUnit(it, is_multiple_unit);
        });
        if (multipleUnitDataList && multipleUnitDataList.length) {
            let hasEmptyValue = multipleUnitDataList.some(it => {
                let {[actual_unit]: actualUnit, [conversion_ratio]: conversionRatio, [base_unit_count]: baseUnitCount, [stat_unit_count]: statUnitCount} = it;
                return isEmpty(actualUnit) || isEmpty(conversionRatio) || isEmpty(baseUnitCount) || isEmpty(statUnitCount);
            })
            if (hasEmptyValue) {
                let content = JSON.stringify({
                    _id,
                    object_describe_api_name,
                    error_info: 'multiple unit product relate field value empty'
                });
                console.log("sfa", 'multipleUnitValueEmpty', content)
                log.kLog("sfa", 'multipleUnitValueEmpty', content);
            }
        }
    }
}