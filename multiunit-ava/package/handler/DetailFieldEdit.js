import unitOptionsCache from "../unitOptionsCache";
import {getPriceBookData, isMultipleUnit, multiUnitPlacesDecimal} from "../utils";
import {i18n, isEmpty} from "../../../pluginbase-ava/package/pluginutils";
import {emitEvent} from "../events";

const key_selected_price_book_product = 'key_selected_price_book_product';//选择价目表返回的价目表明细数据

export class DetailFieldEdit {

    constructor(context) {
        let {fieldMapping, pluginApi, requestApi} = context || {};
        this.fieldMapping = fieldMapping;
        this.pluginApi = pluginApi;
        this.requestApi = requestApi;
    }

    fieldRenderBeforeSync(pluginExecResult, options) {
        let {fieldName, objApiName} = options;
        let {actual_unit, other_unit} = this.fieldMapping.getDetailFields(objApiName);
        if (fieldName === actual_unit || fieldName === other_unit) {
            return this.updateUnitOptionHiddenState(pluginExecResult, options, [fieldName]);
        }
    }

    async fieldEditAfter(pluginExecResult, options) {
        let {fieldName, objApiName} = options || {};
        let {quantity, actual_unit, other_unit, product_id, price_book_product_id, price_book_id} = this.fieldMapping.getDetailFields(objApiName);
        if (fieldName === quantity) {//变更的是数量
            return await this.quantityEditAfter(options, pluginExecResult);
        } else if ([actual_unit, other_unit].includes(fieldName)) {//变更的是单位、其他单位字段
            return await this.unitEditAfter(options, pluginExecResult);
        } else if ([product_id, price_book_product_id].includes(fieldName)) {//变更的是产品、价目表产品字段
            return await this.skuEditAfter(options, pluginExecResult);
        } else if ([price_book_id].includes(fieldName)) {//变更的是价目表字段
            return await this.priceBookEditAfter(options, pluginExecResult);
        }
    }

    fieldEditEnd(pluginExecResult, options) {
        let {fieldName, objApiName} = options || {};
        let {product_id, price_book_product_id, price_book_id, actual_unit, other_unit} = this.fieldMapping.getDetailFields(objApiName);
        if ([product_id, price_book_product_id, price_book_id].includes(fieldName)) {
            return this.updateUnitOptionHiddenState(pluginExecResult, options, [actual_unit, other_unit]);
        }
    }

    updateUnitOptionHiddenState(pluginExecResult, options, updateFields) {
        let {objApiName, dataIndex, dataGetter, dataUpdater} = options || {};
        let {product_id, actual_unit, other_unit, unit, is_multiple_unit} = this.fieldMapping.getDetailFields(objApiName);
        let objectData = dataGetter.getData && dataGetter.getData(objApiName, dataIndex);
        let {[product_id]: productId, [`${unit}__v`]: unit__v} = objectData || {};
        let objectDescribe = dataGetter && dataGetter.getDescribe(objApiName);
        let actualUnitField = objectDescribe && objectDescribe.fields && objectDescribe.fields[actual_unit];
        let otherUnitField = objectDescribe && objectDescribe.fields && objectDescribe.fields[other_unit];
        let orgUnitOptions = actualUnitField && actualUnitField.options || otherUnitField && otherUnitField.options || [];
        let showUnitOptionValues;
        if (isEmpty(productId)) {//兼容产品id为空的场景
            showUnitOptionValues = [];
        } else {
            let multiUnit = isMultipleUnit(objectData, is_multiple_unit);
            if (multiUnit) {
                let unitOptions = unitOptionsCache.getOptions(productId);
                unitOptions = unitOptions && unitOptions.length ? unitOptions : orgUnitOptions;
                showUnitOptionValues = unitOptions && unitOptions.map(it => it.value);
            } else {
                showUnitOptionValues = isEmpty(unit__v) ? [] : [unit__v];
            }
        }

        let hiddenOptApiNames = [];
        let showOptApiNames = [];
        orgUnitOptions.forEach(option => {
            let value = option.value;
            if (showUnitOptionValues && showUnitOptionValues.includes(value)) {
                showOptApiNames.push(value);
            } else {
                hiddenOptApiNames.push(value);
            }
        })

        function setOptionHidden(optApiName, status, fieldName) {
            dataUpdater.setOptionHidden({objApiName, dataIndex, fieldName, optApiName, status});
        }

        updateFields.forEach(fieldName => {
            setOptionHidden(hiddenOptApiNames, true, fieldName);
            setOptionHidden(showOptApiNames, false, fieldName);
        })
    }

    async quantityEditAfter(options) {
        let {dataGetter, objApiName, dataIndex, changeData} = options || {};
        let {getMasterData, getDetail, getDescribe} = dataGetter;
        let detailDataList = getDetail && getDetail(objApiName);
        let objectData = detailDataList && detailDataList.length && detailDataList.find(detailData => {
            return dataIndex === detailData.dataIndex;
        });
        let changedData = Object.assign({}, objectData, changeData);
        let detailFieldMap = this.fieldMapping.getDetailFields(objApiName);
        let {
            quantity, is_multiple_unit, actual_unit, base_unit_count, conversion_ratio, stat_unit_count, other_unit,
            other_unit_quantity
        } = detailFieldMap;
        let isMultiUnit = isMultipleUnit(changedData, is_multiple_unit);
        if (isMultiUnit) {
            let masterData = getMasterData && getMasterData();
            let multiUnitResult = await this.calcPrice(masterData, changedData, objApiName, options);
            let {
                base_unit_count: baseUnitCount,
                conversion_ratio: conversionRatio,
                stat_unit_count: statUnitCount,
                other_unit_quantity: otherUnitQuantity,
            } = multiUnitResult || {};
            let priceBookData = getPriceBookData(multiUnitResult, detailFieldMap);
            Object.assign(changeData, {
                [base_unit_count]: baseUnitCount,
                [conversion_ratio]: conversionRatio,
                [stat_unit_count]: statUnitCount,
                [other_unit_quantity]: otherUnitQuantity
            }, priceBookData);
        } else {
            let objectDescribe = getDescribe && getDescribe(objApiName);
            let {[actual_unit]: actualUnit, [quantity]: count} = changedData;
            let actualUnitField = objectDescribe && objectDescribe.fields && objectDescribe.fields[actual_unit];
            let selectedOption = actualUnitField && actualUnitField.options && actualUnitField.options.find(it => it.value === actualUnit);
            let actualUnitLabel = selectedOption && selectedOption.label || '';
            let statUnitCount = (count + actualUnitLabel);
            Object.assign(changeData, {
                [base_unit_count]: count,
                [conversion_ratio]: 1,
                [stat_unit_count]: statUnitCount,
                [other_unit]: null,
                [other_unit_quantity]: null,
            });
        }
    }

    async unitEditAfter(options) {
        let {masterObjApiName, dataGetter, objApiName, fieldName, dataIndex, changeData, dataUpdater, seriesId, formApis} = options || {};
        let {getMasterData, getDetail} = dataGetter;
        let detailDataList = getDetail && getDetail(objApiName);
        let objectData = detailDataList && detailDataList.length && detailDataList.find(detailData => {
            return dataIndex === detailData.dataIndex;
        });
        let changedData = Object.assign({}, objectData, changeData);
        let detailFieldMap = this.fieldMapping.getDetailFields(objApiName);
        let {
            price: product_price, is_multiple_unit, price_book_price, quantity,
            actual_unit, other_unit, base_unit_count, conversion_ratio, stat_unit_count, other_unit_quantity,
        } = detailFieldMap;
        let isMultiUnit = isMultipleUnit(changedData, is_multiple_unit);
        if (!isMultiUnit) {//非多单位产品不处理
            return;
        }
        let fieldValue = changeData[fieldName];
        if (fieldValue) {
            let masterData = getMasterData && getMasterData();
            let multiUnitResult = await this.calcPrice(masterData, changedData, objApiName, options);
            let {
                price = 0,
                base_unit_count: baseUnitCount,
                conversion_ratio: conversionRatio,
                stat_unit_count: statUnitCount,
                other_unit_quantity: otherUnitQuantity,
                places_decimal = null,
                priceBookPrice,
                count,
            } = multiUnitResult || {};
            let updateData = {
                [product_price]: price,
                [base_unit_count]: baseUnitCount,
                [conversion_ratio]: conversionRatio,
                [stat_unit_count]: statUnitCount,
                [other_unit_quantity]: otherUnitQuantity,
                [multiUnitPlacesDecimal]: places_decimal
            };
            if (fieldName === actual_unit) {
                let priceBookData = getPriceBookData(multiUnitResult, detailFieldMap);
                Object.assign(updateData, priceBookData);
                /*
                * 先通过dataUpdater更新price_book_price，走计算接口计算price_book_price相关的计算字段
                * https://www.tapd.cn/21309261/bugtrace/bugs/view?bug_id=1121309261001302699
                * https://www.tapd.cn/21309261/bugtrace/bugs/view/1121309261001306747
                * */
                dataUpdater.updateDetail(objApiName, dataIndex, {
                    [price_book_price]: priceBookPrice
                })
                await formApis.triggerCalAndUIEvent({
                    objApiName: objApiName,
                    modifiedDataIndexs: [dataIndex],
                    changeFields: [price_book_price]
                });
            }
            if (count) {
                updateData[quantity] = count;
            }
            Object.assign(changeData, updateData);
        } else {
            if (fieldName === actual_unit) {
                this.pluginApi.alert && this.pluginApi.alert(i18n('ava.object_form.order.multi_unit_not_null'/*多单位产品单位不可为空*/));
                delete changeData[fieldName];
            } else if (fieldName === other_unit) {
                Object.assign(changeData, {
                    [other_unit_quantity]: null
                });
            }
        }
    }

    async skuEditAfter(options) {
        let {dataGetter, dataUpdater, objApiName, dataIndex, changeData, lookupData} = options || {};
        let {getMasterData, getDetail, getDescribe} = dataGetter;
        let isMultiUnit = lookupData && isMultipleUnit(lookupData, 'is_multiple_unit');
        let detailFieldMap = this.fieldMapping.getDetailFields(objApiName);
        let {
            product_id, price_book_product_id, price: product_price, quantity,
            actual_unit, other_unit, base_unit_count, conversion_ratio, stat_unit_count, other_unit_quantity
        } = detailFieldMap;
        dataUpdater && dataUpdater.setReadOnly && dataUpdater.setReadOnly({
            objApiName,
            dataIndex: dataIndex,
            fieldName: [actual_unit, other_unit],
            status: !isMultiUnit,
            biz: 'multi-unit',
            priority: 11
        });
        let detailDataList = getDetail && getDetail(objApiName);
        let objectData = detailDataList && detailDataList.length && detailDataList.find(detailData => {
            return dataIndex === detailData.dataIndex;
        });
        if (lookupData) {
            let objectDescribe = getDescribe && getDescribe(objApiName);
            let {object_describe_api_name, unit, unit__v, actual_unit: actualUnit, _id, product_id: _productId, price, pricebook_sellingprice} = lookupData;
            let isProductObj = object_describe_api_name === 'ProductObj';//选择的是否是产品
            let unitId = isProductObj ? unit : (actualUnit || unit__v);
            let productId = isProductObj ? _id : _productId;
            let priceBookProductId = isProductObj ? null : _id;
            let {[quantity]: count} = objectData;
            if (isMultiUnit) {
                await unitOptionsCache.cacheOptions([productId], this.requestApi.batchGetLazyLoadOptions.bind(this.requestApi));
                let unitOptions = unitOptionsCache.getOptions(productId);
                let otherUnitOption = unitOptions && unitOptions.length && unitOptions.find(option => option.value !== unitId)
                let otherUnitId = otherUnitOption && otherUnitOption.value;
                let masterData = getMasterData && getMasterData();
                let multiUnitResult = await this.calcPrice(masterData, {
                    [product_id]: productId,
                    [price_book_product_id]: priceBookProductId,
                    [actual_unit]: unitId,
                    [other_unit]: otherUnitId,
                    [quantity]: count
                }, objApiName, options);
                let {
                    price = 0, unitId: actualUnit,
                    base_unit_count: baseUnitCount,
                    conversion_ratio: conversionRatio,
                    stat_unit_count: statUnitCount,
                    other_unit: otherUnit,
                    other_unit_quantity: otherUnitQuantity,
                    places_decimal = null, count: _quantity,
                } = multiUnitResult || {};
                let updateData = {
                    [product_price]: price,
                    [actual_unit]: actualUnit,
                    [base_unit_count]: baseUnitCount,
                    [conversion_ratio]: conversionRatio,
                    [stat_unit_count]: statUnitCount,
                    [other_unit]: otherUnit,
                    [other_unit_quantity]: otherUnitQuantity,
                    [multiUnitPlacesDecimal]: places_decimal
                };
                if (_quantity) {
                    updateData[quantity] = _quantity;
                }
                let priceBookData = getPriceBookData(multiUnitResult, detailFieldMap);
                Object.assign(changeData, updateData, priceBookData);
            } else {//如果选择的是非多单位产品，处理相关字段
                let actualUnitField = objectDescribe && objectDescribe.fields && objectDescribe.fields[actual_unit];
                let selectedOption = actualUnitField && actualUnitField.options.find(option => option.value === unitId);
                let actualUnitLabel = selectedOption && selectedOption.label || '';
                let statUnitCount = (count + actualUnitLabel);
                Object.assign(changeData, {
                    [actual_unit]: unitId,
                    [base_unit_count]: count,
                    [conversion_ratio]: 1,
                    [stat_unit_count]: statUnitCount,
                    [other_unit]: null,
                    [other_unit_quantity]: null,
                    [multiUnitPlacesDecimal]: null
                });
                let productPriceField = objectDescribe && objectDescribe.fields && objectDescribe.fields[product_price];
                if (productPriceField && isEmpty(productPriceField.default_value)) {//如果价格字段默认值公式是空的， 则回填价格
                    let productPrice = isProductObj ? price : pricebook_sellingprice;
                    Object.assign(changeData, {[product_price]: productPrice})
                }
            }
        } else {//未选择产品时，清空多单位相关字段信息
            Object.assign(changeData, {
                [actual_unit]: null,
                [base_unit_count]: null,
                [conversion_ratio]: null,
                [stat_unit_count]: null,
                [other_unit]: null,
                [other_unit_quantity]: null,
                [multiUnitPlacesDecimal]: null
            });
        }
    }

    async priceBookEditAfter(options) {
        let {lookupData} = options || {};
        let priceBookProduct = lookupData && lookupData[key_selected_price_book_product] || lookupData;//选择的价目表明细数据
        await this.skuEditAfter(Object.assign({}, options, {//走skuChanged逻辑
            lookupData: priceBookProduct
        }))
    }

    async calcPrice(masterData, objectData, objectApiName, options) {
        let {product_id, price_book_product_id, actual_unit, other_unit, quantity} = this.fieldMapping.getDetailFields(objectApiName);
        let {[product_id]: productId, [price_book_product_id]: priceBookProductId, [actual_unit]: unitId, [other_unit]: otherUnitId, [quantity]: count} = objectData;
        let param = {
            params: [{productId, priceBookProductId, unitId, otherUnitId, count}],
            masterObjectApiName: masterData && masterData.object_describe_api_name,
            detailObjectApiName: objectApiName,
            seriesId: options.seriesId
        };
        this.pluginApi.runPluginSync(emitEvent.multiUnitCalcPriceBeforeSync, Object.assign({}, options, {
            param,
            type: 'form',
            detailDataList: [objectData]
        }));
        let res = await this.requestApi.calc(param).catch(err => {
            this.pluginApi.showToast(err);
        });
        return res ? res[0] : {
            base_unit_count: null,
            conversion_ratio: null,
            stat_unit_count: null,
            other_unit_quantity: null
        }
    }
}