import {getPriceBookData, getRowId, isMultipleUnit, multiUnitPlacesDecimal} from "../utils";
import {isEmpty} from "../../../pluginbase-ava/package/pluginutils";
import {emitEvent} from "../events";

export class BatchCalc {

    constructor(context) {
        let {fieldMapping, pluginApi, requestApi} = context || {};
        this.fieldMapping = fieldMapping;
        this.pluginApi = pluginApi;
        this.requestApi = requestApi;
    }

    async calc(pluginExecResult, options) {
        let {dataGetter, dataUpdater, masterObjApiName, seriesId, objApiName, triggerDataIndexList, triggerObjectDataList, doNotUpdateData = false} = options;
        if ((!triggerDataIndexList || !triggerDataIndexList.length) && (!triggerObjectDataList || !triggerObjectDataList.length)) {
            return;
        }
        let {getDetail, getDescribe} = dataGetter || {};
        objApiName = objApiName || this.fieldMapping.getFirstDetailObjApiName();
        let detailFieldMap = this.fieldMapping.getDetailFields(objApiName);
        let {base_unit_count, conversion_ratio, stat_unit_count} = detailFieldMap;
        let detailDataList = getDetail && getDetail(objApiName);
        let triggerDataList;
        if (triggerObjectDataList && triggerObjectDataList.length) {
            triggerDataList = triggerObjectDataList;
        } else {
            triggerDataList = detailDataList && detailDataList.length && detailDataList.filter(detailData => {
                return triggerDataIndexList.includes(detailData.dataIndex)
            })
        }
        if (!triggerDataList || !triggerDataList.length) {
            return;
        }
        let calcResult;
        let params = this.buildMultiUnitParams(triggerDataList, objApiName);
        if (params && params.length) {
            let param = {
                params,
                masterObjectApiName: masterObjApiName,
                detailObjectApiName: objApiName,
                seriesId
            };
            this.pluginApi.runPluginSync(emitEvent.multiUnitCalcPriceBeforeSync, Object.assign({}, options, {
                param,
                type: 'form',
                detailDataList: triggerDataList
            }));
            calcResult = await this.requestApi.calc(param).catch(err => {
                this.pluginApi.showToast(err);
            });
        }
        let updateResult = {};
        let detailObjDescribe = getDescribe(objApiName);
        triggerDataList.forEach(detailData => {
            let multiUnitResult = this.getMultiUnitResult(detailData, calcResult, detailObjDescribe);
            if (multiUnitResult) {
                let {
                    base_unit_count: baseUnitCount, conversion_ratio: conversionRatio, stat_unit_count: statUnitCount, places_decimal = null
                } = multiUnitResult;
                let updateData = {
                    [base_unit_count]: baseUnitCount,
                    [conversion_ratio]: conversionRatio,
                    [stat_unit_count]: statUnitCount,
                    [multiUnitPlacesDecimal]: places_decimal
                };
                let priceBookData = getPriceBookData(multiUnitResult, detailFieldMap);
                Object.assign(updateData, priceBookData);
                let dataIndex = detailData.dataIndex;
                Object.assign(updateResult, {
                    [dataIndex]: updateData
                });
                if (!doNotUpdateData) {
                    dataUpdater && dataUpdater.updateDetail(objApiName, dataIndex, updateData);
                }
            }
        });
        return {updateResult, calcResult}
    }

    buildMultiUnitParams(detailDataList, objApiName) {
        let {product_id, quantity, price_book_product_id, actual_unit, other_unit, is_multiple_unit} = this.fieldMapping.getDetailFields(objApiName);
        return detailDataList && detailDataList.length && detailDataList.filter(it => {
            return isMultipleUnit(it, is_multiple_unit) && (!isEmpty(it[actual_unit]));
        }).map(it => {
            let {[product_id]: productId, [quantity]: count, [price_book_product_id]: priceBookProductId, [actual_unit]: unitId, [other_unit]: otherUnitId} = it;
            let rowId = getRowId(it, true);
            return {productId, priceBookProductId, unitId, otherUnitId, count, rowId}
        });
    }

    getMultiUnitResult(detailData, calcResults, objectDescribe) {
        let {api_name: objApiName, fields} = objectDescribe || {};
        let {actual_unit, quantity} = this.fieldMapping.getDetailFields(objApiName);
        let {[actual_unit]: unitId, [quantity]: count} = detailData || {};
        let orgRowId = getRowId(detailData);
        let calcResult = calcResults && calcResults.length && calcResults.find(it => {
            return orgRowId === (it && it.rowId);
        });
        if (!calcResult) {//如果没有找到计算结果，则特殊创建一个计算结果
            let actualUnitField = fields && fields[actual_unit];
            let selectOption = actualUnitField && actualUnitField.options && actualUnitField.options.find(it => it.value === unitId);
            let unitLabel = selectOption && selectOption.label || '';
            let stat_unit_count = (count + unitLabel);
            calcResult = {
                conversion_ratio: 1,
                base_unit_count: count,
                stat_unit_count: stat_unit_count
            }
        }
        return calcResult;
    }
}