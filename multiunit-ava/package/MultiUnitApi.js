import {request} from "../../pluginbase-ava/package/pluginutils";

export class MultiUnitApi {

    constructor(http) {
        this.http = http;
    }

    calc({params, mcCurrency, detailObjectApiName, masterObjectApiName, seriesId}) {
        return request(this.http, {
            url: 'FHH/EM1ANCRM/API/v1/object/mutipleUnit/service/calc',
            data: {
                params,
                mcCurrency: mcCurrency,
                objectApiName: masterObjectApiName,
                detailObjectApiName,
                seriesId
            }
        }).then(res => {
            return res && res.caclResult;
        })
    }

    batchGetLazyLoadOptions(productIds) {
        return request(this.http, {
            url: `FHE/EM1ANCRM/API/v1/object/lazyLoadOptions/service/batchGetLazyLoadOptions`,
            data: {dataIds: productIds}
        }).then(rst => {
            return rst && rst.optionList;
        })
    }
}