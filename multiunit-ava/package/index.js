import {PriceService} from "./handler/PriceService";
import {MdBatchAdd} from "./handler/MdBatchAdd";
import {FormRender} from "./handler/FormRender";
import {DetailFieldEdit} from "./handler/DetailFieldEdit";
import log from "../../pluginbase-ava/package/log";
import unitOptionsCache from "./unitOptionsCache";
import FieldMapping from "../../pluginbase-ava/package/FieldMapping";
import BizStateConfig from "../../pluginbase-ava/package/BizStateConfig";
import PluginApi from "../../pluginbase-ava/package/PluginApi";
import {MultiUnitApi} from "./MultiUnitApi";
import {BatchCalc} from "./handler/BatchCalc";

/**
 * 多单位插件：http://wiki.firstshare.cn/pages/viewpage.action?pageId=*********
 */
export default class MultiUnitPlugin {

    constructor(pluginService, pluginParam) {
        let {bizStateConfig, params, describe} = pluginParam || {};
        log.tickPluginUsed(describe);
        this.context = {
            fieldMapping: new FieldMapping(params, {
                masterFields: {
                    form_account_id: 'form_account_id',// 客户id
                    form_partner_id: 'form_partner_id',// 合作伙伴id
                    form_price_book_id: 'form_price_book_id',// 价目表id
                },

                detailFields: {
                    price: 'product_price',// 价格
                    quantity: 'quantity',// 数量
                    product_id: 'product_id',// 产品
                    price_book_product_id: 'price_book_product_id',//价目表产品
                    price_book_id: "price_book_id",//价目表
                    unit: 'unit',//单位(基准单位)，引用字段：引用产品对象的单位字段
                    actual_unit: 'actual_unit',// 单位
                    conversion_ratio: 'conversion_ratio',// 转换比例
                    base_unit_count: 'base_unit_count', // 基准单位数量
                    stat_unit_count: 'stat_unit_count',// 统计单位数量
                    other_unit: 'other_unit',// 其他单位
                    other_unit_quantity: 'other_unit_quantity',// 其他单位数量
                    is_multiple_unit: 'is_multiple_unit',//是否是多单位
                    price_book_price: 'price_book_price',
                    price_book_discount: 'price_book_discount'
                }
            }),
            bizStateConfig: new BizStateConfig(bizStateConfig, pluginService.api.getPlugins()),
            pluginApi: new PluginApi(pluginService),
            requestApi: new MultiUnitApi(pluginService.api.request)
        }
        this.priceService = new PriceService(this.context);
        this.mdBatchAdd = new MdBatchAdd(this.context);
        this.formRender = new FormRender(this.context);
        this.detailFieldEdit = new DetailFieldEdit(this.context);
        this.batchCalc = new BatchCalc(this.context);
    }

    formSubmitPostBefore(pluginExecResult, options) {
        return this.formRender.formSubmitPostBefore(pluginExecResult, options)
    }

    async formRenderEnd(pluginExecResult, options) {
        await this.cacheUnitOptions(pluginExecResult, options);
        return await this.formRender.formRenderEnd(pluginExecResult, options)
    }

    async cacheUnitOptions(pluginExecResult, options) {
        let {objApiName, dataGetter} = options;
        objApiName = objApiName ? objApiName : this.context.fieldMapping.getFirstDetailObjApiName();
        let {product_id} = this.context.fieldMapping.getDetailFields(objApiName);
        let detailDataList = dataGetter && dataGetter.getDetail && dataGetter.getDetail(objApiName);
        let productIds = detailDataList && detailDataList.length && detailDataList.map(detailData => {
            return detailData[product_id]
        })
        await unitOptionsCache.cacheOptions(productIds, this.context.requestApi.batchGetLazyLoadOptions.bind(this.context.requestApi));
    }

    fieldRenderBeforeSync(pluginExecResult, options) {
        let {masterObjApiName, objApiName} = options;
        if (objApiName !== masterObjApiName) {
            return this.detailFieldEdit.fieldRenderBeforeSync(pluginExecResult, options);
        }
    }

    async fieldEditAfter(pluginExecResult, options) {
        let {masterObjApiName, objApiName} = options;
        if (objApiName !== masterObjApiName) {
            return await this.detailFieldEdit.fieldEditAfter(pluginExecResult, options);
        }
    }

    fieldEditEnd(pluginExecResult, options) {
        let {masterObjApiName, objApiName} = options;
        if (objApiName !== masterObjApiName) {
            return this.detailFieldEdit.fieldEditEnd(pluginExecResult, options);
        }
    }

    async mdBatchAddAfter(pluginExecResult, options) {
        return await this.mdBatchAdd.mdBatchAddAfter(pluginExecResult, options);
    }

    async mdBatchAddEnd(pluginExecResult, options) {
        await this.cacheUnitOptions(pluginExecResult, options);
        return this.mdBatchAdd.mdBatchAddEnd(pluginExecResult, options);
    }

    mdCloneAfter(pluginExecResult, options) {
        return this.mdBatchAdd.mdCloneAfter(pluginExecResult, options);
    }

    mdCloneEnd(pluginExecResult, options) {
        return this.mdBatchAdd.mdCloneEnd(pluginExecResult, options);
    }

    priceServiceBatchSelectSkuConfigSync(pluginExecResult, options) {
        return this.priceService.priceServiceBatchSelectSkuConfigSync(pluginExecResult, options);
    }

    priceServiceBatchAddGetPriceParamSync(pluginExecResult, options) {
        return this.priceService.priceServiceBatchAddGetPriceParamSync(pluginExecResult, options);
    }

    priceServiceFormParseFullProductSync(pluginExecResult, options) {
        return this.priceService.priceServiceFormParseFullProductSync(pluginExecResult, options);
    }

    async triggerBatchCalc(pluginExecResult, options) {
        return await this.batchCalc.calc(pluginExecResult, options);
    }

    apply() {
        return [
            {
                event: "form.submit.post.before",
                functional: this.formSubmitPostBefore.bind(this)
            }, {
                event: 'form.render.end',
                functional: this.formRenderEnd.bind(this)
            }, {
                event: 'field.render.before.sync',
                functional: this.fieldRenderBeforeSync.bind(this)
            }, {
                event: 'field.edit.after',
                functional: this.fieldEditAfter.bind(this)
            }, {
                event: 'field.edit.end',
                functional: this.fieldEditEnd.bind(this)
            }, {
                event: 'md.batchAdd.after',
                functional: this.mdBatchAddAfter.bind(this)
            }, {
                event: 'md.batchAdd.end',
                functional: this.mdBatchAddEnd.bind(this)
            }, {
                event: 'md.clone.after',
                functional: this.mdCloneAfter.bind(this)
            }, {
                event: 'md.clone.end',
                functional: this.mdCloneEnd.bind(this)
            }, {
                event: 'price-service.batchSelectSkuConfig.sync',
                functional: this.priceServiceBatchSelectSkuConfigSync.bind(this)
            }, {
                event: 'price-service.batchAdd.getPriceParam.sync',
                functional: this.priceServiceBatchAddGetPriceParamSync.bind(this)
            }, {
                event: 'price-service.form.parseFullProduct.sync',
                functional: this.priceServiceFormParseFullProductSync.bind(this)
            }, {
                event: 'multi-unit.triggerBatchCalc',
                functional: this.triggerBatchCalc.bind(this)
            }];
    }
};