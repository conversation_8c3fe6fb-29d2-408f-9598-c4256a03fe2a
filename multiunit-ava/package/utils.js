import {formatValueDecimalPlaces, i18n, isEmpty, uuid} from "../../pluginbase-ava/package/pluginutils";

export const multiUnitPlacesDecimal = 'multi_unit_places_decimal';//多单位取价接口返回的数量字段的小数位数
export const getRealPriceResult = 'get_real_price_result';//getRealPrice接口返回的明细数据

export function isMultipleUnit(objectData, fieldApiName) {
    if (!objectData || !fieldApiName) {
        return false;
    }
    objectData = objectData.product_id__ro || objectData;
    if (!objectData) {
        return false;
    }
    let field_value = objectData[fieldApiName];
    let field_value__v = objectData[`${fieldApiName}__v`];
    let isMultiUnit = (field_value__v === undefined || field_value__v === null) ? field_value : field_value__v;
    if (typeof isMultiUnit === 'boolean') {
        return isMultiUnit;
    } else if (typeof isMultiUnit === 'string') {
        return isMultiUnit === i18n('ava.object_form.crm.yes')/*是*/ || isMultiUnit === 'true'
    }
    return false;
}

export function getRowId(objectData, isRegenerate) {
    let rowId;
    if (isRegenerate) {
        rowId = uuid();
    } else {
        rowId = objectData.key_row_id || uuid();//存放在数据中的rowId，保证唯一，用来匹配接口返回的数据
    }
    objectData.key_row_id = rowId;
    return rowId;
}

export function getPriceBookData(multiUnitResult, detailFieldMap) {
    if (isEmpty(multiUnitResult) || isEmpty(detailFieldMap)) {
        return;
    }
    let {needChangePriceBookProduct, changePriceBookProduct,} = multiUnitResult;
    if (!needChangePriceBookProduct || !changePriceBookProduct) {
        return;
    }
    let {price_book_product_id, price_book_id} = detailFieldMap;
    let {id, _id, name, containerDocument} = changePriceBookProduct;
    let priceBookData = {
        [price_book_product_id]: id || _id,
        [`${price_book_product_id}__r`]: name
    };
    if (containerDocument) {
        priceBookData[price_book_id] = containerDocument.pricebook_id;
        priceBookData[`${price_book_id}__r`] = containerDocument.pricebook_id__r;
    }
    return priceBookData;
}

export function formatQuantityDecimalPlaces(quantity, defaultDecimalPlaces, unitDecimalPlaces) {
    if (isEmpty(quantity)) {
        return quantity;
    }
    if (!isEmpty(defaultDecimalPlaces) && !isNaN(parseInt(defaultDecimalPlaces))
        && !isEmpty(unitDecimalPlaces) && !isNaN(parseInt(unitDecimalPlaces))) {
        let decimalPlaces = Math.min(Number(defaultDecimalPlaces), Number(unitDecimalPlaces));
        return formatValueDecimalPlaces(quantity, decimalPlaces);
    }
    return quantity;
}