/**
 * @desc: 属性公共插件
 * @author: wangshaoh
 * @date: 12/29/21
 */
import PPM from 'plugin_public_methods'
import Base from 'plugin_base'
// import Add from './src/add'

export default class AttributeBase_Base extends Base {

    options() {
        return {
            defMasterFields: {
                // form_account_id: 'account_id',  // 客户id
                // form_partner_id: 'partner_id',  // 合作伙伴id
                // form_price_book_id: 'price_book_id',  // 价目表id
            },
            defMdFields: {
                product_price: 'price', // 价格
                discount: 'discount', // 折扣
                price_book_product_id: 'price_book_product_id', // 开启价目表，替换价目表产品id
                product_id: 'product_id', //
                attribute: 'attribute',
                attribute_json: 'attribute_json', //
                attribute_price_book_id: "attribute_price_book_id", // 属性价目表名称
            },
        }
    }

    constructor(pluginService, pluginParam) {
        super(...arguments);
    }

    _parseFullProductList_after(plugin, param) {
        let data = this.addSomeFieldsToParam(param.data, param.metaData, param);
    }

    /**
     * @desc 给取价接口参数添加属性信息
     * @param data
     * @param metaData
     * @returns {*}
     */
    addSomeFieldsToParam(data = [], metaData = [], param) {
        let {
            attribute_json
        } = this.getAllFields(param.objApiName);
        return data.map((item, index) => {
            let d = metaData[index];
            if (!d) return item; // 检查 metaData[index] 是否存在
            let attrMap = d.attribute_group || d[attribute_json] || {};
            if (PPM.isString(attrMap)) attrMap = JSON.parse(attrMap);
            return Object.assign(item, {
                "attrMap": attrMap,
            })
        })
    }

    /**
     * @desc 取价后数据匹配逻辑
     * @param plugin
     * @param param
     * @returns {{data: *}}
     * @private
     */
    _matchRealPrice(plugin, param = {}) {
        let data = this.matchRealPriceData(param.curData, param.realPriceList, param.value, param);
        if (data) return {
            data
        }
    }

    /**
     * @desc 属性
     * @param rowData
     * @param realPriceData
     * @param value
     * @returns {*}
     */
    matchRealPriceData(rowData = {}, realPriceData = [], value = {}, param) {
        let data = value;
        let {
            attribute_json,
            product_id
        } = this.getAllFields(param.objApiName);
        if (rowData.attribute) {
            realPriceData.forEach((listItem) => {
                if (PPM.isEqual(rowData[attribute_json], listItem.attr_group) && rowData[product_id] === listItem.product_id) {
                    data = listItem;
                }
            });
            return data;
        }
    }

    //  属性价目表如果取价服务返回的没有值，需要清掉；可能是被禁用或删除了
    async _matchRealPrice_after(plugin, opt) {
        let {data, mdApiName, value, param} = opt;
        let r = await this.runPlugin('attribute.matchRealPrice', {
            rowData: data,
            mdApiName,
            relData: value,
            param
        });
        if (r && r.notMatch) return;
        let {
            attribute_price_book_id
        } = this.getAllFields(mdApiName);
        param.dataUpdater.updateDetail(mdApiName, data.rowId, {
            [attribute_price_book_id]: value.attribute_price_book_id,
            [attribute_price_book_id + '__r']: value.attribute_price_book_id__r,
            attribute_price_book_lines_ids: value.attribute_price_book_lines_ids,
        });
         await this.runPlugin('attribute.attributePriceBook.change', {
            data: [data],
            mdApiName,
            param
        });
    }

    /**
     * @desc 选完数据，走完取价服务，需要补属性
     * @param plugin
     * @param param
     * @returns {{data: *}}
     * @private
     */
    _batchAddAfter_after(plugin, param) {
        this.parseAddData(param.data, param, param.param);
    }

    /**
     * @desc 补属性信息，校验重复数据
     * @param data
     * @param param
     * @param mdMethod
     * @returns {Array}
     */
    parseAddData(data = [], param = {}, mdMethod = {}) {
        this.supplementAttrData(data, param, mdMethod.lookupField.api_name);
        // let needDel = this.filterTableData(data, mdMethod.dataGetter.getDetail(), param);
        // if (needDel.length) {
        //     needDel.forEach(item => mdMethod.dataUpdater.del(mdMethod.objApiName, item.rowId))
        // }
        return data
    }

    /**
     * @desc  补充属性信息
     * @param pickedList 当前选中数据
     * @param param
     * @param lookupField
     */
    supplementAttrData(pickedList = [], param = {}, lookupField = '') {
        let {
            product_price,
            price_book_product_id,
            product_id,
            attribute,
            discount,
            attribute_price_book_id,
            attribute_json
        } = this.getAllFields(param.objApiName);
        let lData = param.lookupDatas;
        let rData = param.realPriceData;

        pickedList.forEach(p => {
            let item = lData.find(l => (l.product_id || l._id == p[product_id]) && !l.picked);
            let rd = rData.find(l => (l.product_id == p[product_id]) && !l.picked);
            if (lookupField === price_book_product_id) {
                item = lData.find(l => l._id == p[price_book_product_id]);
                // p[product_id + '__r'] = item.product_id__r;
            }
            if (!item) return p;
            item.picked = true;
            if(!rd) return p;   
            rd.picked = true;
            p = Object.assign(p, {
                [attribute]: item.attribute_group_text || null,
                [attribute_json]: item.attribute_group || null,
                [discount]: rd.discount,
                [attribute_price_book_id]: rd.attribute_price_book_id || null,
                [attribute_price_book_id + '__r']: rd.attribute_price_book_id__r || null,
                attribute_price_book_lines_ids: rd.attribute_price_book_lines_ids,

            });
            delete p.attribute_group;
            delete p.attribute_group_text;
        });
    }

    /**
     * @desc 明细中已经存在产品相同 属性相同的数据，则不再添加；
     * @param list
     * @param tableData
     * @returns {Array}
     */
    filterTableData(list = [], tableData = [], param) {
        let data = (Array.isArray(tableData) ? tableData : tableData.data) || [];
        let {
            attribute_json
        } = this.getAllFields(param.objApiName);
        let arr = [];
        if (!data || !data.length) {
            return arr;
        }
        data = data.filter(item => !item.parent_rowId);
        list.forEach(l => {
            let f = data.find(t => {
                let flag = false;
                if (!t[attribute_json]) return flag;
                let tAttr = typeof (t[attribute_json]) == 'string' ? JSON.parse(t[attribute_json]) : t[attribute_json],
                    lAttr = l[attribute_json];
                for (let key in lAttr) {
                    if (lAttr[key] == tAttr[key]) flag = true;
                }
                return t.product_id === l.product_id && t.rowId !== l.rowId && flag;
            });
            if (f) arr.push(l);
        });
        return arr;
    }

    /**
     * @desc
     * @param plugin
     * @param param
     * @returns {{data: *}}
     * @private
     */
    _batchAddAfter_parseData(plugin, param) {
        let data = param.data;
        let lookupDatas = param.lookupDatas;
        let {
            attribute_json,
            attribute
        } = this.getAllFields(param.objApiName);
        data.forEach((item, index) => {
            let ld = lookupDatas[index];
            if (ld.attribute_group) item.attribute_group = ld.attribute_group;
            if (ld.attribute_json) item[attribute_json] = ld.attribute_json;
            if (ld.attribute_group_text || item[attribute]) item.attribute_group_text = ld.attribute_group_text || item[attribute];
        });
        return {
            data
        }
    }

    /**
     * @desc 开属性，价目表明细字段不可编辑；
     * @param plugin
     * @param param
     * @private
     */
    async _mdRenderAfter(plugin, param) {
        this.setFieldsReadonly(param);
    }

    // 开属性，价目表明细字段不可编辑；
    setFieldsReadonly(param) {
        let {
            price_book_product_id
        } = this.getAllFields(param.objApiName);
        param.dataUpdater.setReadOnly({
            fieldName: [price_book_product_id],
            dataIndex: 'all',
            objApiName: param.objApiName,
            recordType: param.recordType,

            status: true
        });
    }

    /**
     * @desc 从xx添加数据时，是否走取价
     * @param plugin
     * @param param
     * @returns {{isDoPriceService: boolean}}
     * @private
     */
    _batchAddAfter_before(plugin, param) {
        let {
            product_id,
            price_book_product_id
        } = this.getAllFields(param.objApiName);
        if ([product_id, price_book_product_id].includes(param.lookUpApiName)) {
            return {
                isDoPriceService: true
            }
        }
    }

    /**
     * @desc 添加bom数据时，保留一些属性相关字段；
     * @param plugin
     * @param param
     * @returns {{usefulFields: string[]}}
     * @private
     */
    _bom_addChildren_before(plugin, param) {
        return {
            __execResult:{
                usefulFields: ['attribute', 'attribute_json', "selectedAttr", "attribute_price_book_id", "attribute_price_book_id__r"]
            },
            __mergeDataType:{
                array: 'concat'
            }
        }
    }

    // 更新BOM子产品补充属性信息
    _bom_updateChildrenAttribute(plugin, opt) {
        let {
            param,
            mdApiName,
            newChildren,
            oldChildren
        } = opt;
        this.updateDataAttrValue(oldChildren, newChildren, mdApiName, param)
    }

    // bom 添加完子件数据之后，先更新一次属性；如果调用了 querybom，后边还会更新一次；
    _bom_parseAddBomData_after(plugin, opt) {
        let {
            param,
            mdApiName,
            allAddData,
        } = opt;
        PPM.forEachTreeData(allAddData, item => {
            if (!item.isGroup && ( item.selectedAttr || item.attribute_json)) {
                this.updateDataAttrValue(item, item, mdApiName, param);
            }
        })
    }

    // 更新数据属性
    updateDataAttrValue(oldData = {}, newData = {}, mdApiName = '', param){
        let {
            attribute,
            attribute_price_book_id,
            attribute_json
        } = this.getAllFields(mdApiName);
        if (!oldData.isGroup && (newData.selectedAttr || newData.attribute_json)) {
            param.dataUpdater.updateDetail(mdApiName, oldData.rowId, {
                [attribute]: newData.attribute,
                [attribute_json]: newData.attribute_json,
                [attribute_price_book_id]: newData.attribute_price_book_id,
                [attribute_price_book_id + '__r']: newData.attribute_price_book_id__r,
                selectedAttr: newData.selectedAttr,
                attribute_price_book_lines_ids: newData.attribute_price_book_lines_ids,

            })
        }
    }


    _bom_updateRootData(plugin, opt){
        let {
            rootData,
            newRootData,
            mdApiName,
            param
        } = opt;
        this.updateDataAttrValue(rootData, newRootData, mdApiName, param);
    }

    // 给querybom接口新返回的BOM子产品补充默认属性信息
    _bom_parseNewChildren(plugin, opt) {
        let {
            param,
            mdApiName,
            newChildren,
        } = opt;
        let {
            attribute,
            attribute_price_book_id,
            attribute_json
        } = this.getAllFields(param.objApiName);
       
        PPM.forEachTreeData(newChildren, item => {
            if (item.attribute && !item.selectedAttr) {
                let defAttr = this.getDefAttribute(item);
                let up = {};
                up[attribute] = defAttr.attribute || null;
                up[attribute_json] = defAttr.attribute_json || null;
                up[attribute_price_book_id] = item.attribute_price_book_id || null;
                up[attribute_price_book_id + '__r'] = item.attribute_price_book_id__r || null;
                up.attribute_price_book_lines_ids = item.attribute_price_book_lines_ids || null;
                param.dataUpdater.updateDetail(mdApiName, item.rowId, up)
            }
        })
    }

    // 获取属性信息
    getAttribute(data) {
        let res = {};
        if (data.selectedAttr) {
            data.selectedAttr.forEach((val, key) => {
                res[key] = val.value_ids[0].id;
            })
        } else if (data.attribute) {
            data.attribute.forEach(item => {
                let key = item.id;
                let d = item.attribute_values.find(a => a.is_default == '1');
                if (d) res[key] = d.id;
            })
        }
        return res;
    }

    // 获取默认属性信息;
    getDefAttribute(data) {
        let res = {},
            attrObj = {},
            attrJson = {},
            attrTxt = "";
        if (!data.attribute) return res;
        data.attribute.forEach(a => {
            let defAttr = a.attribute_values.find(i => i.is_default == '1');
            attrObj[a.id] = {
                name: a.name,
                value_ids: [{
                    id: defAttr.id,
                    name: defAttr.name
                }]
            };
            attrJson[a.id] = defAttr.id;
            attrTxt += a.name + ":" + defAttr.name + ";"
        });
        res.attribute_json = attrJson;
        res.selectedAttr = attrObj;
        res.attribute = attrTxt.slice(0, attrTxt.length - 1);
        return res;
    }

    // bom 二次配置之前，格式化属性信息
    _bom_renderSelectBom_before(plugin, opt) {
        let {
            param,
            rootData,
            childrenData,
        } = opt;
        let {
            attribute,
            attribute_json
        } = this.getAllFields();
        let allData = [rootData, ...childrenData];
        PPM.forEachTreeData(allData, item => {
            if(item[attribute_json]){
                item.attribute_json = item[attribute_json];
                item.attribute = item[attribute];
            }
        })
    }

    // 从对象渲染之前，格式化自定义对象属性数据
    async _mdRenderBefore(plugin, param) {
        let mdApiName = param.objApiName;
        if(mdApiName.includes('__c')){
            let {attribute_json, attribute} = this.getAllFields(mdApiName);
             // 属性值多语转换
            if(CRM._cache.currencyStatus){
                let mdData = param.dataGetter.getDetail(mdApiName);
                if (!mdData || !mdData.length) {
                    return;
                }
                let p = mdData.map(item => {
                    return {
                        _id: item.rowId,
                        attribute_json: item[attribute_json]
                    }
                });
                let r = await this.i18nToAttribute({
                    dataList: p,
                });
                r.dataList.forEach(item => {
                    let up = {
                        [attribute]: item.attribute
                    };
                    param.dataUpdater.updateDetail(mdApiName, item._id, up);
                })  
            }
        }
    }

    // 属性数据多语转换
    i18nToAttribute(param) {
        let url = `FHH/EM1HNCRM/API/v1/object/attribute/service/queryAttributeAndNonAttributeText`;
        let p = Object.assign({}, {
           "dataList": [
                // {
                //     "_id": "11",     //行id
                //     "attribute_json": {
                //         "630058e388ef5d00012cef78": "630058e388ef5d00012cef79"
                //     },   //属性json
                //     "nonstandard_attribute_json": {
                //         "62f61cf81a4820000161e33b": "11",
                //         "630f05f104370f000116be92": "1"
                //     } //非标属性json
                // }
            ]
        }, param);
        return PPM.ajax(this.request, url, p);
    }

    // convertToJSON(dataString) {
    //     let obj = {};
    //     dataString = dataString.replace(/{|}/g,'');
    //     let arr = dataString.split(',');
    //     arr.forEach(item => {
    //         item = item.replace(/\s/g, "");
    //         let a = item.split('=');
    //         obj[a[0]] = a[1]
    //     });
    //     return obj;
    // }

    // 选数据后补充属性 （单独使用属性插件时需要）
    _batchAddAfterHook(plugin, param){
        let mdApiName = param.objApiName;
        let addData = param.addDatas;
        let lData = param.lookupDatas;
        this.supplementAttrData({
            addData,
            lookUpData: lData,
            mdApiName,
            param
        });
    }

    // 补充属性信息
    supplementAttrData({
        addData = [],
        lookUpData = [],
        mdApiName = '',
        param = {}
    }){
        let {
            attribute,
            attribute_json
        } = this.getAllFields(mdApiName);

        addData.forEach((item,index) => {
            let ld = lookUpData[index];
            if(!ld || !ld.attribute_group) return;
            let up = {
                [attribute]: ld.attribute_group_text || null,
                [attribute_json]: ld.attribute_group || null,
            };
            param.dataUpdater.updateDetail(mdApiName, item.rowId, up)
        });

    }

    // 切换产品，补充属性信息
    _getRealPriceForEdit_before(plugin, obj){
        let {
            param,
            changeDataList,
            lookupData
        } = obj;
        let {
            attribute,
            attribute_json
        } = this.getAllFields(param.objApiName);
        
    }   

    // 编辑产品，取价之前，补充属性信息
    _getRealPriceForEdit_after(plugin, obj){
        let {
            param,
            changeDataList,
            lookupData
        } = obj;
      
        this.supplementAttrData({
            addData: changeDataList,
            lookUpData: lookupData,
            mdApiName: param.objApiName,
            param
        });
    }

    // 编辑产品之后补充属性信息
    _mdEditAfter(plugin, param){
        let mdApiName = param.objApiName;
        let {product_id, price_book_product_id} = this.getAllFields(mdApiName);
        if(![product_id, price_book_product_id].includes(param.fieldName)){
            return;
        }
        let changeRowIds = param.dataIndex;
        let detailDataList = param.dataGetter.getDetail(mdApiName) || [];
        // 根据changeRowIds取明细数据
        let changeDataList = detailDataList.filter(item => {
            return changeRowIds.includes(item.rowId || item.dataIndex)
        });
        let lookupData = param.lookupData;
        if(lookupData){
            this.supplementAttrData({
                addData: changeDataList,
                lookUpData: [lookupData],
                mdApiName,
                param
            });
        }
    }

    // 编辑产品，取价之前，补充属性信息
    _mdEditAfter_before(plugin, obj){
        let {
            param,
        } = obj;
        this._mdEditAfter(plugin, param);
    }

    getHook() {
        return [{
                event: 'price-service.parseFullProductList.after',
                functional: this._parseFullProductList_after.bind(this)
            }, {
                event: 'price-service.matchRealPriceData.after',
                functional: this._matchRealPrice.bind(this)
            }, {
                event: 'price-service.matchRealPrice.after',
                functional: this._matchRealPrice_after.bind(this)
            }, {
                event: 'price-service.batchAddAfter.after',
                functional: this._batchAddAfter_after.bind(this)
            }, {
                event: 'price-service.batchAddAfter.parseData',
                functional: this._batchAddAfter_parseData.bind(this)
            }, {
                event: 'md.render.before',
                functional: this._mdRenderBefore.bind(this)
            },  {
                event: 'md.render.after',
                functional: this._mdRenderAfter.bind(this)
            }, {
                event: 'price-service.batchAddAfter.before',
                functional: this._batchAddAfter_before.bind(this)
            }, {
                event: 'bom.addChildren.before',
                functional: this._bom_addChildren_before.bind(this)
            }, {
                event: 'bom.parseNewChildren',
                functional: this._bom_parseNewChildren.bind(this)
            }, {
                event: 'bom.parseAddBomData.after',
                functional: this._bom_parseAddBomData_after.bind(this)
            }, {
                event: 'bom.updateBomChildren',
                functional: this._bom_updateChildrenAttribute.bind(this)
            }, {
                event: 'bom.updateRootData',
                functional: this._bom_updateRootData.bind(this)
            },{
                event: 'bom.renderSelectBom.before',
                functional: this._bom_renderSelectBom_before.bind(this)
            }, {
                event: 'md.batchAdd.after',
                functional: this._batchAddAfterHook.bind(this)
            }, {
                event: 'price-service.getRealPriceForEdit.before',
                functional: this._getRealPriceForEdit_before.bind(this)
            }, {
                event: 'price-service.getRealPriceForEdit.after',
                functional: this._getRealPriceForEdit_after.bind(this)
            }, {
                event: 'md.edit.after',
                functional: this._mdEditAfter.bind(this)
            }, {
                event: 'price-service.mdEditAfter.before',
                functional: this._mdEditAfter_before.bind(this)
            }

        ];
    }

}