// testData.ts
import { fieldMapData } from './testhelper';
import { DetailDataFace, PolicyDataFace, ResMatchFace } from '../src/package/new_data_interface';
import { GiftRuleFace, executePolicyFace } from '../src/package/data_interface';

export const newDataIsEmpty = {
	type: 'detail',
	matchFrom: 'fullyUpdate',
	nData: <PolicyDataFace>{},
	oData: <DetailDataFace>{},
	fieldMap: fieldMapData,
};

export const newDataNoPolicyId = {
	type: 'detail',
	matchFrom: 'fullyUpdate',
	nData: <PolicyDataFace>{
		policy_dynamic_amount: 0,
	},
	oData: <DetailDataFace>{},
	fieldMap: fieldMapData,
};
export const newDataOnlyPolicyId = {
	type: 'detail',
	matchFrom: 'fullyUpdate',
	nData: <PolicyDataFace>{
		price_policy_id: ""
	},
	oData: <DetailDataFace>{},
	fieldMap: fieldMapData,
};
export const newDataHoldChose = <any>{
	type: 'detail',
	matchFrom: 'fullyUpdate',
	nData: {
		price_policy_id: "aGiftPolicy",
		gift_map: {
			"ruleId_1": {
				hold_chose: "1",
				productList: [{
					name: "产品A",
					quantity: 1
				}]
			}
		}
	},
	oData: {
		price_policy_id: "aGiftPolicy",
		gift_map: {
			"ruleId_1": {
				productList: [{
					name: "产品A",
					quantity: 2
				}]
			}
		}
	},
	fieldMap: fieldMapData,
}
export const newDataNormal = {
	type: 'detail',
	matchFrom: 'fullyUpdate',
	nData: <PolicyDataFace>{
		price_policy_id: 'policy1',
		price_policy_rule_ids: ['rule1'],
		group_key: null,
		policy_dynamic_amount: -50,
		gift_map: null,
	},
	oData: <DetailDataFace><unknown>{
		"prod_pkg_key": "prodPkgKey4",
		"product_id": "normal_product", //普通产品
	},
	fieldMap: fieldMapData,
}
//价格政策数据
export const mockMatchParam = {
	masterData: {
		account_id: "account_id",
	},
	detailDataMap: {
		"prodPkgKey1": {
			"is_giveaway": "1",  //价格政策赠品
			"parent_gift_key": "prod_pkg_key_4",
		},
		"prodPkgKey2": {
			"is_giveaway": "2",  //返利品
			"rebate_coupon_id": "rebate_product"
		},
		"prodPkgKey3": {
			"parent_rowId": "bomParent",  //bom子件
		},
		"prodPkgKey4": {
			"product_id": "normal_product",  //普通产品
		}
	},
	modifyInfo: {
		"modifyFields": {
			"SalesOrderProductObj": ["quantity"],
		},
		"modifyIndex": ["prodPkgKey1", "prodPkgKey2", "prodPkgKey3", "prodPkgKey4"],
		"triggerPolicy": true
	},
	changeInfo: {
		masterUpdate: {},
		mdUpdate: {},
		mdAdd: [],
		mdDel: []
	},
};
//从对象价格类政策
export const mockDetailPricePolicy: ResMatchFace = {
	masterData: {},
	detailDataMap: {
		'prodPkgKey4': {
			rowId: 'prodPkgKey4',
			dataIndex: 'prodPkgKey4',
			prod_pkg_key: 'prodPkgKey4',
			price_policy_id: 'policy1',
			price_policy_rule_ids: ['rule1'],
			group_key: null,
			policy_dynamic_amount: -50,
			gift_map: null,
			master_policy_id: null,
		},
	},
	masterPricePolicy: {},
	detailPricePolicyMap: {
		'prodPkgKey4': {
			'policy1': ['rule1'],
		},
	},
	groupMap: {},
	pricePolicies: [
		{
			id: 'policy1',
			modifyType: 'detail',
			name: '从对象减价政策',
			rules: [
				{
					id: 'rule1',
					ruleType: 'pricing',
					name: '小计减50',
					amortizeType: 'detail',
					executeResult: '-50',
				},
			],
			executionFieldMap: {},
		},
	],
	amortizeInfoMap: {
		'policy1_rule1_prodPkgKey4': {
			policy_dynamic_amount: -50,
		},
	},
};
//
export const mockGiftRule: GiftRuleFace = {
	type: "sample_type",
	object_api_name: "sample_object_api_name",
	gift_total_num: 20,
	gift_kind_upper_limit: 3,
	cycle_count: 2,
	hold_chose: "sample_hold_choice",
	cacheInit: false,
	gift_list: [
		{
			is_this_product: true,
			max_value: 10,
			min_value: 1,
			required: true,
			isActive: false,
			product_id: "product_1",
			product_id__s: "product_1_s",
			price: 100,
			quantity: 0,
			prod_pkg_key: "pkg_1",
			unit: "kg",
			is_multiple_unit: false,
			price_book_id: "pricebook_1"
		},
		{
			is_this_product: false,
			max_value: 5,
			min_value: 2,
			required: false,
			isActive: false,
			product_id: "product_2",
			product_id__s: "product_2_s",
			price: 200,
			quantity: 0,
			prod_pkg_key: "pkg_2",
			unit: "litre",
			is_multiple_unit: true,
			price_book_id: "pricebook_2"
		},
		{
			is_this_product: false,
			max_value: Infinity,
			min_value: 11,
			required: true,
			isActive: false,
			product_id: "product_3",
			product_id__s: "product_3_s",
			price: 150,
			quantity: 0,
			prod_pkg_key: "pkg_3",
			unit: "pcs",
			is_multiple_unit: false,
			price_book_id: "pricebook_3"
		}
	],
	maxKind: 0,
	maxNum: 0
};

//rule:[gift_total_num,gift_kind_upper_limit,cycle_count],
//giftList:Array<[max_value,min_value,required]>
//expect:Array<[quantity,isActive]>  //每个赠品分配到的数量和是否选中
export const mockGiftRuleScene = [
    {
        rule: [null, null, 1], 
        giftList: [[null, null, true], [null, null, true], [null, null, true]],
        expect: [[1, true], [1, true], [1, true]]
    },
    {
        rule: [null, null, 1], 
        giftList: [[null, null, true], [null, null, false], [null, null, false]],
        expect: [[1, true], [1, true], [1, true]]
    },
    {
        rule: [5, null, 1], 
        giftList: [[null, null, true], [null, null, true], [null, null, true]],
        expect: [[3, true], [1, true], [1, true]]
    },
    {
        rule: [null, 2, 1], 
        giftList: [[null, null, true], [null, null, true], [null, null, true]],
        expect: [[1, true], [1, true], [0, false]]
    },
    {
        rule: [5, null, 1], 
        giftList: [[null, null, true], [null, null, true], [null, null, false]],
        expect: [[4, true], [1, true], [0, false]]
    },
    {
        rule: [null, 2, 1], 
        giftList: [[null, null, true], [null, null, false], [null, null, false]],
        expect: [[1, true], [1, true], [0, false]]
    },
    {
        rule: [5, null, 1], 
        giftList: [[null, null, false], [null, null, false], [null, null, false]],
        expect: [[5, true], [0, false], [0, false]]
    },
    {
        rule: [null, 2, 1], 
        giftList: [[null, null, false], [null, null, false], [null, null, false]],
        expect: [[1, true], [1, true], [0, false]]
    },
    {
        rule: [null, null, 1], 
        giftList: [[5, 2, true], [null, null, false], [null, null, false]],
        expect: [[5, true], [1, true], [1, true]]
    },
    {
        rule: [5, 2, 1], 
        giftList: [[5, 2, true], [3, 1, false], [null, null, false]],
        expect: [[5, true], [0, false], [0, false]]
    },
	{
        rule: [null, null, 1], 
        giftList: [[null, 2, false], [null, 2, false], [null, 2, false]],
        expect: [[2, true], [2, true], [2, true]]
    },
];

export const param: executePolicyFace = {
	detailDataMap: {
		prod1: {
			prod_pkg_key: 'prod1',
			group_key: null,
			parent_gift_key: null,
			is_giveaway: '0',
		},
		prod2: {
			prod_pkg_key: 'prod2',
			group_key: 'group1',
			parent_gift_key: null,
			is_giveaway: '0',
		},
		masterGift1: {
			prod_pkg_key: 'masterGift1',
			group_key: null,
			parent_gift_key: 'master',
			is_giveaway: '1',
		},
		prod3: {
			prod_pkg_key: 'prod3',
			group_key: 'group1',
			parent_gift_key: null,
			is_giveaway: '0',
		},
		gift1: {
			prod_pkg_key: 'gift1',
			group_key: null,
			parent_gift_key: 'prod1',
			is_giveaway: '1',
		},
		groupGift1: {
			prod_pkg_key: 'groupGift1',
			group_key: 'group1',
			parent_gift_key: 'prod2',
			is_giveaway: '1',
		},
	},
	changeInfo: {
		mdAdd: [
			{
				prod_pkg_key: 'prod4',
				group_key: 'group1',
				parent_gift_key: null,
				is_giveaway: '0',
			},
			{
				prod_pkg_key: 'prod5',
				group_key: 'group1',
				parent_gift_key: null,
				is_giveaway: '0',
			},
		],
		mdDel: ['groupGift1', 'prod5'],
		masterUpdate: {
			gift_map: ''
		},
		mdUpdate: undefined,
	},
	masterData: {
		account_id: '',
		gift_map: {
			
		}
	},
	modifyInfo: {
		modifyFields: {},
		modifyIndex: [],
	},
	policyInfo: undefined,
	matchArgs: undefined,
	matchRes: undefined,
	matchFrom: '',
};
