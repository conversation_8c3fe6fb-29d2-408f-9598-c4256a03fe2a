import { FieldMapFace } from '../src/package/data_interface';
import PolicyExecute from '../src/package/execute';
import PolicyGift from '../src/package/gift';

const fieldMapData = <FieldMapFace>{
	"id": "id",
	"rowId": "rowId",
	"record_type": "record_type",
	"product_id": "product_id",
	"product_id__r": "product_id__r",
	"product_price": "product_price",
	"discount": "discount",
	"quantity": "quantity",
	"unit": "unit",
	"unit__v": "unit__v",
	"unit__r": "unit__r",
	"is_giveaway": "is_giveaway",
	"is_package": "is_package",
	"is_package__v": "is_package__v",
	"parent_rowId": "parent_rowId",
	"is_multiple_unit": "is_multiple_unit",
	"is_multiple_unit__v": "is_multiple_unit__v",
	"actual_unit": "actual_unit",
	"conversion_ratio": "conversion_ratio",
	"base_unit_count": "base_unit_count",
	"stat_unit_count": "stat_unit_count",
	"price_book_id": "price_book_id",
	"price_book_id__r": "price_book_id__r",
	"price_book_discount": "price_book_discount",
	"price_book_price": "price_book_price",
	"price_book_product_id": "price_book_product_id",
	"price_book_product_id__r": "price_book_product_id__r",
	"data_index": "data_index",
	"prod_pkg_key": "prod_pkg_key",
	"master_policy_id": "master_policy_id",
	"price_policy_id": "price_policy_id",
	"price_policy_id__r": "price_policy_id__r",
	"price_policy_rule_ids": "price_policy_rule_ids",
	"policy_dynamic_amount": "policy_dynamic_amount",
	"group_key": "group_key",
	"parent_gift_key": "parent_gift_key",
	"gift_amortize_price": "gift_amortize_price",
	"stand_price": "stand_price"
}; // 模拟字段映射对象

export function createPolicyExecuteInstance() {
	// 替换为实际参数
	const requestId = "813e0c7876e4484c8e0fcff5d7cbc04e";
	const masterApiName = "SalesOrderObj";
	const detailApiName = "SalesOrderProductObj";
	const fromType = 'add';
	const request = jest.fn().mockImplementation(() => Promise.resolve({
		Result: { StatusCode: 0 },
		Value: 'mocked request result'
	})); // 模拟请求对象
	const triggerCal =  jest.fn() ; // 模拟触发计算对象
	const masterFieldMap = {
		"_id": "_id",
		"account_id": "account_id",
		"partner_id": "partner_id",
		"mcCurrency": "mcCurrency",
		"price_policy_id": "price_policy_id",
		"price_policy_id__r": "price_policy_id__r",
		"price_policy_rule_ids": "price_policy_rule__ids",
		"policy_dynamic_amount": "policy_dynamic_amount",
		"price_book_amount": "price_book_amount",
		"product_amount": "product_amount"
	}; // 模拟主字段映射对象
	const fieldMap = fieldMapData; // 模拟字段映射对象

	const decimalMap = {  // 模拟小数映射对象
		"quantity": 2,
		"gift_amortize_price": 2,
		"product_price": 2,
		"price_book_price": 2,
		"policy_dynamic_amount": 2
	};
	const fields = {}; // 模拟字段对象
	const recordType = 'default__C';

	const policyExecuteInstance = new PolicyExecute(
		requestId,
		masterApiName,
		detailApiName,
		fromType,
		request,
		triggerCal,
		masterFieldMap,
		fieldMap,
		decimalMap,
		fields,
		recordType,
		'1',
		false,
		false
	);

	policyExecuteInstance.policyConfig = {
		accountId: "6205ccf446d0730001276e46",
		conditionFieldMap: {
			[masterApiName]: ["virtual_amount", "policy_after_total", "product_amount"],
			[detailApiName]: ["virtual_subtotal", "quantity", "price_book_subtotal", "product_id", "price_book_price", "product_price"],
		},
		hasPricePolicy: true,
		masterAmortizeRuleIdList: ["63d60cf2505a340001ecc03a"],
		masterNoAmortizeRuleIdList: [],
		pricePolicies: []
	};

	return policyExecuteInstance;
}

export function createGiftInstance(){
	// 替换为实际参数
	const requestId = "813e0c7876e4484c8e0fcff5d7cbc04e";
	const masterApiName = "SalesOrderObj";
	const detailApiName = "SalesOrderProductObj";
	const fromType = 'add';
	const request = jest.fn().mockImplementation(() => Promise.resolve({
		Result: { StatusCode: 0 },
		Value: 'mocked request result'
	})); // 模拟请求对象
	const getRowBasicData =  jest.fn() ; 
	const triggerCal =  jest.fn() ; // 模拟触发计算对象
	const masterFieldMap = {
		"_id": "_id",
		"account_id": "account_id",
		"partner_id": "partner_id",
		"mcCurrency": "mcCurrency",
		"price_policy_id": "price_policy_id",
		"price_policy_id__r": "price_policy_id__r",
		"price_policy_rule_ids": "price_policy_rule__ids",
		"policy_dynamic_amount": "policy_dynamic_amount",
	}; // 模拟主字段映射对象
	const fieldMap = fieldMapData; // 模拟字段映射对象

	const decimalMap = {  // 模拟小数映射对象
		"quantity": 2,
		"gift_amortize_price": 2,
		"product_price": 2,
		"price_book_price": 2,
		"policy_dynamic_amount": 2
	};
	const fields = {
		"SalesOrderProductObj": {
			product_id: {
				is_open_display_name: true
			}
		}
	}; // 模拟字段对象
	const recordType = 'default__C';
	const giftAmortizeBasis="product";
	const  i18n=jest.fn() ; 
	const detailDesc={};

	const policyGiftInstance = new PolicyGift(
		masterApiName,
		detailApiName,
		fromType,
		"default__C",
		request,
		getRowBasicData,
		triggerCal,
		masterFieldMap,
		fieldMap,
		decimalMap,
		fields,
		giftAmortizeBasis,
		i18n,
		detailDesc
	);


	return policyGiftInstance;
}

export { fieldMapData };