 // __mocks__/ppm.js
const PPM = {
    ajax: jest.fn(),
    pipeAsync :(...fns) => {
		return async (result) => {
			for (let fn of fns) {
				result = await fn(result)
			}
			return result;
		}
	},
    composeAsync: (...fns) => {
		return async (result) => {
			let list = [...fns];
			while (list.length > 0) {
				result = await list.pop()(result)
			}
			return result;
		}
	},
    curry:(arity,fn)=> {
		return (function nextCurried(prevArgs){
			return function curried(nextArg){
				var args = [ ...prevArgs, nextArg ];
	
				if (args.length >= arity) {
					return fn( ...args );
				}
				else {
					return nextCurried( args );
				}
			};
		})( [] );
	},
    collectCalFields:()=>{
        return {};
    },
	multiplicational :(value, multiplier) =>{
		return 42//value * multiplier;
	}, 
  };
  
export default PPM;
  
