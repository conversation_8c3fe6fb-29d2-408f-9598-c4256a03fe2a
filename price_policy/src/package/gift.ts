/**
 * 收集赠品:
 * 1.收集赠品=>转化为赠品明细并插入正确的位置
 * 2.更新数据的gift_map
 * 3.在giftInfo里返回更新信息
 */

import PPM from 'plugin_public_methods';
import PolicyUtil from './policy_util';

import {
	GiftFace,
	GiftMapFace,
	GiftRuleFace,
	masterFieldMapFace,
	FieldMapFace,
	DecimalMapFace,
	GiftProductFace,
	DetailDataMapFace,
	GetUnitGiftFace,
	UnitResFace
} from './data_interface';

import {
	GiftMapItemFace,
	GiftListItemFace,
	MasterDataFace,
	DetailDataFace,
	ExecuteFace
} from './new_data_interface';

export default class PolicyGift {
	public masterData: any;
	public processLog: Array<any> = [];
	private requestMap: any;
	constructor(
		public masterApiName: string,
		public detailApiName: string,
		public fromType: string,
		public recordType: string,
		public request: any,
		public getRowBasicData: any,
		public triggerCal: any,
		public masterFieldMap: masterFieldMapFace,
		public fieldMap: FieldMapFace,
		public decimalMap: DecimalMapFace,
		public fields: {
			[api: string]: any;
		},
		public giftAmortizeBasis: string,
		public i18n: any,
		public detailDesc: any,
		public allowedRange: boolean,
		public grayNewCalGiftFun: boolean
	) {
		this.initRequestMap();
	}

	private refreshProcessLog() {
		this.processLog = [];
	}
	//计算赠品
	public async calGifts(param: ExecuteFace): Promise<ExecuteFace | null> {
		if (!param) {
			return null;
		}
		this.masterData = param.masterData;
		this.refreshProcessLog();

		//新方法：1.先做部分数据补全、取价和分配
		await PPM.pipeAsync(
			this.collectGiftMapList.bind(this),
			PPM.partial(this.requestGiftInfos.bind(this), param,["calPriceBook"]).bind(this),
			PPM.curry(2, this.allocateQuantities.bind(this))(param) //分配数量
		)(param);

		// 获取不在可售范围内的赠品
		let giftNotInRange: Array<string> = [];
		if (param.matchFrom !== 'fullyUpdate') {
			giftNotInRange = await this.getAllowGiftDetails(
				param.masterData,
				param.detailDataMap
			);
			await this.cacheGiftHandle(param, giftNotInRange);
		}
		const { holdGifts = [], newGifts = [] } = this.collectGift(
			param,
			giftNotInRange
		);

		// 处理保留的赠品
		// 新增的赠品需要初始化计算
		await this.generateNewGift(param, newGifts);

		const curDel = Object.keys(param.detailDataMap).filter(
			(key: string) => {
				const data = param.detailDataMap[key];

				if (
					PolicyUtil.isPolicySystemGift(data, this.fieldMap) &&
					!holdGifts.includes(key)
				) {
					return key;
				} else {
					return false;
				}
			}
		);
		param.changeInfo.mdDel = param.changeInfo.mdDel.concat(curDel);
		const delByRange: string[] = curDel.filter((d) =>
			giftNotInRange.includes(d)
		);
		param.policyInfo.rangeDelInfo = delByRange.map(
			(key) => param.detailDataMap[key]
		);
		param.promoteProcessLog?.details?.push(...this.processLog);
		return param;
	}

	//收集全部gift_list数据
	private async collectGiftMapList(param: ExecuteFace) {
		const { masterData, detailDataMap } = param;
		const {
			data_index,
			prod_pkg_key,
			price_policy_rule_ids,
			product_id,
			price_book_id,
			actual_unit
		} = this.fieldMap;
		const giftData: Array<any> = [];

		const collectGifts = (data: MasterDataFace | DetailDataFace) => {
			const dataKey = data[prod_pkg_key] || 'master',
				commonProps = this.getCommonProps(data, dataKey);

			Object.entries(data.gift_map || {}).forEach(
				([ruleId, giftItem]) => {
					if (giftItem.hold_chose !== '1' || giftItem.cacheInit) {
						const groupKeyInfo = giftItem.group_key
							? { group_key: giftItem.group_key }
							: {};
						giftItem.gift_list.forEach((g: any) => {
							const uniqueCode = PPM.uniqueCode();
							//需要更新到gift_list里的信息
							Object.assign(g, commonProps, groupKeyInfo, {
								rowId: uniqueCode,
								[data_index]: uniqueCode,
								[prod_pkg_key]: uniqueCode,
								[price_policy_rule_ids]: [ruleId],
								[product_id]: g.product_id,
								[actual_unit]: g.actual_unit || g.unit,
								[price_book_id]: g.is_this_product
									? data[price_book_id]
									: g.price_book_id || ''
							});

							giftData.push(g);
						});
					}
				}
			);
		};
		collectGifts(masterData);
		Object.values(detailDataMap).forEach((detail) => {
			collectGifts(detail);
		});
		return giftData;
	}

	//gift_map里gift_list的默认数据
	private getCommonProps(
		data: MasterDataFace | DetailDataFace,
		parentKey: string
	) {
		const { record_type, price_policy_id, parent_gift_key } = this.fieldMap;
		const recordType =
			parentKey == 'master' ? this.recordType : data.record_type;
		const commonProps = {
			[record_type]: recordType,
			[parent_gift_key]: parentKey,
			[price_policy_id]: data[price_policy_id],
			icon_fake_val: true,
			gift_type: 'system'
		};
		return commonProps;
	}

	//新方法处理赠品：先取价后分配数量
	private async generateNewGift(
		param: ExecuteFace,
		giftData: Array<any> = []
	) {
		if (!giftData.length) {
			return;
		}

		let giftProducts: Array<GiftProductFace> = [];
		if(this.grayNewCalGiftFun){
			giftProducts = await PPM.pipeAsync(
				this.updateByLocal.bind(this), //补全基础数据
				PPM.partial(this.requestGiftInfos.bind(this), param,["calMultiUnit","calPeriodData"]).bind(this),
				PPM.curry(2, this.calSelfGiftPrice.bind(this))(param), //计算赠本品价目表价格
				PPM.curry(2, this.calGiftAmortize.bind(this))(param), //计算赠品分摊价
			)(giftData);
		}else{
			giftProducts = await PPM.pipeAsync(
				this.updateByLocal.bind(this), //补全基础数据
				PPM.partial(this.requestGiftInfos.bind(this), param,["calMultiUnit","calPeriodData"]).bind(this),
				PPM.curry(2, this.calSelfGiftPrice.bind(this))(param), //计算赠本品价目表价格
				PPM.curry(2, this.calGiftAmortize.bind(this))(param), //计算赠品分摊价
				PPM.curry(2, this.calBatch.bind(this))(param)
			)(giftData);
		}
		
		param.changeInfo.mdAdd = param.changeInfo.mdAdd.concat(giftProducts);
	}

	//计算赠品
	public async modifyGifts(param: ExecuteFace) {
		if(this.grayNewCalGiftFun){
			return this.modifyGiftsNew(param);
		}
		const { mdAdd } = param.changeInfo,
			{ modifyIndex = [], modifyFields = {} } = param.modifyInfo;

		if (mdAdd.length >= 1) {
			param.changeInfo.mdAdd = await PPM.composeAsync(
				PPM.curry(2, this.calBatch.bind(this))(param),
				PPM.curry(2, this.calGiftAmortize.bind(this))(param),
				PPM.curry(2, this.calSelfGiftPrice.bind(this))(param),
				PPM.partial(this.requestGiftInfos.bind(this), param,null).bind(this),
				this.updateByLocal.bind(this), //补全基础数据
				this.updateBySelf.bind(this)
			)(mdAdd);
		}
		if (modifyIndex.length >= 1) {
			const updateGifts = modifyIndex.map((key: string) => {
				return param.detailDataMap[key];
			});
			param = await PPM.composeAsync(
				PPM.curry(2, this.updateCalResForModify.bind(this))(param),
				this.triggerCal.bind(this),
				PPM.curry(2, this.parseCalArgs.bind(this))(modifyFields),
				PPM.curry(2, this.calGiftAmortize.bind(this))(param),
				PPM.curry(2, this.calSelfGiftPrice.bind(this))(param),
				PPM.partial(this.requestGiftInfos.bind(this), param,["calMultiUnit"]).bind(this),
			)(updateGifts);
		}
		return param;
	}

	//计算赠品
	public async modifyGiftsNew(param: ExecuteFace) {
		const { mdAdd } = param.changeInfo,
			{ modifyIndex = [], modifyFields = {} } = param.modifyInfo;

		let addGifts = mdAdd||[];
		let updateGifts = modifyIndex.map((key: string) => {
			return param.detailDataMap[key];
		});
		if(addGifts.length >= 1){
			addGifts = await PPM.composeAsync(
				this.updateByLocal.bind(this), //补全基础数据
				this.updateBySelf.bind(this)
			)(mdAdd);
		}
		const gifts = await PPM.composeAsync(
			PPM.curry(2, this.calGiftAmortize.bind(this))(param),
			PPM.curry(2, this.calSelfGiftPrice.bind(this))(param),
			PPM.partial(this.requestGiftInfos.bind(this), param,null).bind(this),
		)([...addGifts,...updateGifts]);

		const addGiftsKey= addGifts.map((g:any)=>g.prod_pkg_key);
		if(addGiftsKey.length > 0){
			param.changeInfo.mdAdd = gifts.filter((g:any)=>addGiftsKey.includes(g.prod_pkg_key));
		}
		return param;

	}

	//编辑/购物车等非完全更新数据的场景，用历史赠品更新一次gift_map
	async cacheGiftHandle(param: ExecuteFace, giftNotInRange: Array<string>) {
		const initArr: any = [],
			masterData = param.changeInfo.masterUpdate,
			detailDataMap = param.changeInfo.mdUpdate,
			detailDataArr = Object.keys(param.detailDataMap).reduce(
				(accArr, key) => {
					accArr.push(param.detailDataMap[key]);
					return accArr;
				},
				initArr
			);

		const checkedDetails = new Set();

		const {
			rowId,
			prod_pkg_key,
			data_index,
			quantity,
			actual_unit,
			unit,
			unit__v,
			unit__r,
			parent_gift_key,
			product_id,
			product_id__r,
			price_policy_rule_ids
		} = this.fieldMap;
		const updateGiftList = (
			giftMap: any,
			pKey: string,
			parentKeys: Array<string>,
			detailsArr: Array<any>,
			giftNotInRange: Array<string>,
			mdUpdate: any
		) => {
			for (const key in giftMap) {
				const giftRule = giftMap[key];
				if (giftRule.cacheInit) {
					//gift_list中有的数据
					giftRule.gift_list.forEach((g: any) => {
						let oriGift = this.findOriGift(
							parentKeys,
							detailsArr,
							g,
							key
						);
						//可选赠品里的非必含需要过滤可售范围
						if (
							oriGift &&
							giftNotInRange.includes(oriGift.prod_pkg_key) &&
							!g.required &&
							giftRule.type == 'OPTIONAL'
						) {
							oriGift = null;
						}
						let updateInfo: any = {};
						if (oriGift && !checkedDetails.has(oriGift.rowId)) {
							checkedDetails.add(oriGift.rowId);
							updateInfo = {
								isActive: true,
								[parent_gift_key]: pKey
							};
							const updateKeys = [
								prod_pkg_key,
								data_index,
								rowId,
								quantity,
								unit,
								unit__v,
								unit__r
							];
							updateKeys.forEach((key: string) => {
								updateInfo[key] = oriGift[key];
							});
							if (oriGift[actual_unit]) {
								updateInfo[actual_unit] = oriGift[actual_unit];
							}
							//更新attribute_map信息
							const oriUpdate = this.updateGiftAttr(oriGift, g);
							mdUpdate[oriGift.rowId] = {
								...(mdUpdate[oriGift.rowId] || {}),
								...oriUpdate
							};
						} else {
							updateInfo = {
								isActive: false,
								[quantity]: 0
							};
						}
						g = Object.assign(g, updateInfo);
					});
					delete giftRule.cacheInit;
					giftRule.hold_chose = '1';
				}
			}
		};
		//
		param.changeInfo.mdUpdate = param.changeInfo?.mdUpdate || {};
		masterData.gift_map &&
			updateGiftList(
				masterData.gift_map,
				'master',
				['master'],
				detailDataArr,
				giftNotInRange,
				param.changeInfo.mdUpdate
			);
		//编辑初始化时，gift_map可能挂在组合里其他明细上
		for (let key in detailDataMap) {
			let data = detailDataMap[key],
				parentKeys = data.group_key
					? param.policyInfo.groupMap[data.group_key]
					: [key];
			data.gift_map &&
				updateGiftList(
					data.gift_map,
					key,
					parentKeys,
					detailDataArr,
					giftNotInRange,
					param.changeInfo.mdUpdate
				);
		}
	}

	//
	private findOriGift(
		parentKeys: Array<string>,
		detailsArr: Array<any>,
		g: any,
		ruleId: string
	) {
		const gMin = g.min_value ?? 0,
			gMax = g.max_value ?? Infinity;
		const { parent_gift_key, product_id, quantity, actual_unit } =
			this.fieldMap;
		const oriGift = detailsArr.find((d) => {
			const matchesParentKey = parentKeys.includes(d[parent_gift_key]),
				matchesProductId = d[product_id] === g.product_id,
				dQuantity = Number(d[quantity]) || 0,
				matchesQuantity = dQuantity >= gMin && dQuantity <= gMax,
				matchesUnit = d[actual_unit]
					? d[actual_unit] == (g.actual_unit || g.unit)
					: true,
				matchRule = d.price_policy_rule_ids?.[0] == ruleId;
			return (
				matchesParentKey &&
				matchesProductId &&
				matchesQuantity &&
				matchesUnit &&
				matchRule
			);
		});
		return oriGift;
	}

	//1.收集需要更新的赠品和保留的数据
	public collectGift(param: ExecuteFace, giftNotInRange: Array<string>) {
		let initArr: Array<any> = [],
			holdGifts: Array<string> = [],
			newGifts: Array<GiftFace> = [],
			masterData = param.masterData,
			detailDataMap = param.changeInfo.mdUpdate,
			detailDataArr = Object.keys(param.detailDataMap).reduce(
				(accArr, key) => {
					accArr.push(param.detailDataMap[key]);
					return accArr;
				},
				initArr
			);

		if (masterData) {
			if (masterData['gift_map']) {
				({ holdGifts, newGifts } = this.separateGifts(
					masterData,
					holdGifts,
					newGifts,
					detailDataArr,
					param.matchFrom,
					giftNotInRange
				));
			}
		}
		for (let key in detailDataMap) {
			let data = param.detailDataMap[key];
			if (data && data['gift_map']) {
				({ holdGifts, newGifts } = this.separateGifts(
					data,
					holdGifts,
					newGifts,
					detailDataArr,
					param.matchFrom,
					giftNotInRange
				));
			}
		}
		return {
			holdGifts: holdGifts,
			newGifts: newGifts
		};
	}
	//分离新赠品和保留的历史赠品,补全一些赠品和本品关联的数据
	private separateGifts(
		data: any,
		holdGifts: Array<string>,
		newGifts: Array<GiftFace>,
		detailDataArr: Array<any>,
		matchFrom: string,
		giftNotInRange: Array<string>
	) {
		const {
			data_index,
			parent_gift_key,
			prod_pkg_key,
			price_policy_id,
			price_policy_rule_ids,
			price_book_id
		} = this.fieldMap;
		let giftMap: GiftMapFace = data.gift_map,
			dataKey = data[prod_pkg_key] || 'master';

		for (let key in giftMap) {
			let giftRule: GiftRuleFace = giftMap[key];
			if (giftRule.hold_chose === '1') {
				let curGifts: Array<any> = detailDataArr.filter(
					(d) =>
						d[parent_gift_key] == dataKey &&
						(d[price_policy_rule_ids] &&
							d[price_policy_rule_ids][0]) == key
				);

				let tempCurGifts = curGifts.map((g) => g[prod_pkg_key]);
				//如果当前规则是组合规则，组合政策的保留赠品，需要在根据group_key查找一遍
				const curPolicyRule = `${data[price_policy_id]}_${key}`;

				if (data.group_key && data.group_key == curPolicyRule) {
					const holdGiftsSet = new Set(
						...[...holdGifts, ...tempCurGifts]
					); // 使用Set进行快速查找

					detailDataArr.forEach((d) => {
						const ruleId =
							d[price_policy_rule_ids] &&
							d[price_policy_rule_ids][0];
						const groupKey = `${d[price_policy_id]}_${ruleId}`;

						if (
							d[parent_gift_key] &&
							!holdGiftsSet.has(d[prod_pkg_key]) &&
							data.group_key === groupKey &&
							ruleId === key
						) {
							holdGiftsSet.add(d[prod_pkg_key]);
							tempCurGifts.push(d[prod_pkg_key]);
						}
					});
				}

				//再对holdGifts做一遍过滤 过滤掉在giftNotInRange里且非required数据
				if (giftNotInRange.length && giftRule.type === 'OPTIONAL') {
					tempCurGifts = tempCurGifts.filter((key) => {
						// 检查是否在giftNotInRange中且不是必需的
						const giftItem = giftRule.gift_list.find(
							(g) => g.prod_pkg_key == key
						);
						const isRequired = giftItem?.required === true;
						const isNotInRange = giftNotInRange.includes(key);

						// 如果在giftNotInRange中且非required，返回false（过滤掉）
						return !(isNotInRange && !isRequired);
					});
				}
				holdGifts = holdGifts.concat(tempCurGifts);
			} else {
				let giftMapGifts: Array<GiftFace> = giftRule.gift_list.filter(
					(g) => g.isActive
				);
				newGifts = newGifts.concat(giftMapGifts);
			}
		}

		return {
			holdGifts: holdGifts,
			newGifts: newGifts
		};
	}

	//2.用本地数据补全赠品数据,包括处理映射字段

	// 编辑赠品时，自身基础数据映射
	private updateBySelf(gifts: Array<any>) {
		const { product_id, product_price, actual_unit } = this.fieldMap;
		return gifts.map((g) => {
			return {
				...g,
				[product_id]: g.product_id,
				[product_price]: g.price,
				[actual_unit]: g.actual_unit || g.unit
			};
		});
	}

	//用本地数据补全赠品数据
	private updateByLocal(gifts: Array<any>) {
		const basicData = this.getRowBasicData(),
			{
				id,
				product_id,
				product_id__r,
				discount,
				product_price,
				gift_amortize_price,
				actual_unit,
				unit,
				unit__v,
				unit__r,
				is_multiple_unit,
				is_multiple_unit__v,
				is_giveaway
			} = this.fieldMap;

		const productField = this.fields[this.detailApiName][product_id],
			productNameConfig = productField.is_open_display_name;

		return gifts.map((g) => {
			const name =
				(productNameConfig ? g.display_name : g.product_id__s) ||
				g.product_id__s;
			let item = Object.assign({}, basicData, g, {
				[id]: g.product_id,
				[product_id__r]: name,
				[discount]: '100',
				[gift_amortize_price]: PolicyUtil.parseNumByDecimal(
					g.price,
					this.decimalMap[gift_amortize_price],
					g.price,
					true
				), //赠品分摊价格
				[unit]: g.unit,
				[unit__v]: g.unit,
				[unit__r]: g.unit_name,
				[is_multiple_unit]: g.is_multiple_unit,
				[is_multiple_unit__v]: g.is_multiple_unit || false,
				[is_giveaway]: '1',
				product_id__s: name
			});

			this.updateGiftAttr(item);
			delete item.attribute_map;

			return item;
		});
	}

	//展开属性信息、周期性信息
	private updateGiftAttr(data: any, attrData?: any) {
		if (!data) return {};

		const source = attrData ?? data;
		const {
			pricing_period,
			pricing_mode,
			pricing_cycle,
			pricing_rate,
			service_start_time,
			whole_period_sale,
			settlement_mode,
			settlement_cycle,
			settlement_rate
		} = this.fieldMap;
		const { attribute_map = {}, periodic_map = {} } = source;
		let mergedAttributes: any = {};
		if (Object.keys(attribute_map).length > 0) {
			mergedAttributes = {
				...mergedAttributes,
				...attribute_map,
				giftAttrFields: Object.keys(attribute_map)
			};
		}

		// 合并周期性属性（如果存在）
		if (Object.keys(periodic_map).length > 0) {
			mergedAttributes = {
				...mergedAttributes,
				[pricing_cycle]: periodic_map.pricing_cycle,
				[pricing_rate]: periodic_map.pricing_frequency,
				[pricing_mode]: periodic_map.pricing_mode,
				[service_start_time]: periodic_map.service_start_time,
				[settlement_cycle]: periodic_map.settlement_cycle,
				[settlement_rate]: periodic_map.settlement_frequency,
				[settlement_mode]: periodic_map.settlement_mode,
				[whole_period_sale]: periodic_map.whole_period_sale
			};
		}
		if (pricing_period) {
			mergedAttributes[pricing_period] = data.pricing_period ?? 1;
		}

		Object.assign(data, mergedAttributes);
		return mergedAttributes;
	}

	//计算本品价格,取本品的价目表信息和价目表价格，如果多单位需要换算
	public calSelfGiftPrice(param: ExecuteFace, gifts: Array<GiftProductFace>) {
		const me = this;
		gifts.forEach((gift) => {
			if (!gift.is_this_product) {
				return;
			}
			const {
					parent_gift_key,
					is_multiple_unit__v,
					actual_unit,
					conversion_ratio,
					price_book_discount,
					price_book_price,
					price_book_id,
					price_book_id__r,
					price_book_product_id,
					price_book_product_id__r
				} = this.fieldMap,
				parent = param.detailDataMap[gift[parent_gift_key]],
				priceBookFields = [
					price_book_price,
					price_book_discount,
					price_book_id,
					price_book_id__r,
					price_book_product_id,
					price_book_product_id__r
				];
			const item :any = {};
			priceBookFields.forEach((f) => {
				gift[f] = parent[f];
				item[f] = parent[f];
			});
			if (
				parent[is_multiple_unit__v] &&
				gift[actual_unit] !== parent[actual_unit]
			) {
				const a = PPM.multiplicational(
						parent[price_book_price],
						gift[conversion_ratio]
					),
					b = PPM.division(a, parent[conversion_ratio]);
				gift[price_book_price] = PolicyUtil.parseNumByDecimal(
					b,
					me.decimalMap[price_book_price],
					b,
					true
				);
				item[price_book_price] = gift[price_book_price];
			}
			param && me.collectChangInfo(param, gift.prod_pkg_key, item);
		});
		return gifts;
	}
	//计算分摊
	public calGiftAmortize(param: ExecuteFace, gifts: Array<GiftProductFace>) {
		const me = this,
			{
				prod_pkg_key,
				policy_dynamic_amount,
				gift_amortize_price,
				quantity,
				price_book_price,
				pricing_period
			} = this.fieldMap;
		gifts.forEach((gift: any) => {
			//计算赠品的促销优惠额
			//根据分摊配置，设置"赠品分摊价格"
			const countValTemp =
				-1 *
				PPM.multiplicational(gift[quantity], gift[price_book_price]);
			const countVal = countValTemp * (gift[pricing_period] ?? 1);
			const item = {
				[policy_dynamic_amount]: PolicyUtil.parseNumByDecimal(
					countVal,
					me.decimalMap[policy_dynamic_amount],
					countVal,
					true
				),
				[gift_amortize_price]:
					me.giftAmortizeBasis === 'price_book_price'
						? PolicyUtil.parseNumByDecimal(
								gift[price_book_price],
								me.decimalMap[gift_amortize_price],
								gift[price_book_price],
								true
						  )
						: gift[gift_amortize_price]
			};
			gift = Object.assign(gift, item);
			param && me.collectChangInfo(param, gift[prod_pkg_key], item);
		});
		return gifts;
	}

	private collectChangInfo(
		param: ExecuteFace,
		key: string,
		updateItem: object
	) {
		param.changeInfo.mdUpdate[key] = Object.assign(
			param.changeInfo.mdUpdate[key] || {},
			updateItem
		);
	}
	
	//6.调用计算接口
	public async calBatch(param: ExecuteFace,gifts: Array<GiftProductFace>) {
		const startTime = PolicyUtil.getTime();
		const dataFormPolicy = await PPM.composeAsync(
			PPM.partial(this.updateCalRes.bind(this), param,gifts).bind(this),
			this.triggerCal.bind(this),
			PPM.curry(2, this.parseCalArgs.bind(this))(null)
		)(gifts);
		this.processLog.push({
			action: 'calBatch',
			request: '/calculate/service/batchCalculate',
			responseStatus: '200 OK',
			durationMs: PolicyUtil.getTime() - startTime,
			extraInfo: {}
		});
		return dataFormPolicy;
	}

	public parseCalArgs(
		modifyFields: { [api: string]: Array<string> } | null,
		gifts: Array<GiftProductFace>
	) {
		const isInit = modifyFields == null;
		const {
			prod_pkg_key,
			product_price,
			price_book_price,
			price_book_discount,
			policy_dynamic_amount,
			quantity,
			pricing_period = 'pricing_period',
			service_start_time = 'service_start_time',
			service_end_time = 'service_end_time'
		} = this.fieldMap;
		let calFieldsObj: any = {};
		if (modifyFields) {
			calFieldsObj = PPM.collectCalFields(this.fields, modifyFields, [
				this.detailApiName
			]);
		} else {
			calFieldsObj = PPM.getCalFields(
				this.fields[this.detailApiName],
				true
			);
		}
		//计算赠品配置属性相关的计算字段
		if (isInit) {
			calFieldsObj = this.collectAttrCalFields(calFieldsObj, gifts);
		}
		//计算赠品不可计算字段
		const noCalculateFields = [
			product_price,
			price_book_price,
			price_book_discount,
			policy_dynamic_amount,
			quantity,
			pricing_period,
			service_start_time,
			service_end_time
		];
		calFieldsObj[this.detailApiName] = calFieldsObj[
			this.detailApiName
		].filter((f: string) => !noCalculateFields.includes(f));

		const args = PolicyUtil.getCalArgs(
			{
				masterData: this.masterData,
				modifyInfo: {
					modifyFields: modifyFields || {},
					modifyIndex: gifts.map((g) => g[prod_pkg_key])
				}
			},
			this.masterApiName,
			this.detailApiName,
			calFieldsObj,
			'mdEdit',
			[],
			gifts,
			{
				[this.detailApiName]: noCalculateFields
			}
		);

		return {
			...args,
			// 临时兼容triggerCal实例和bom parseParam影响
			parseParam: (p: any) => {
				//非编辑赠品
				if (isInit) {
					const excludedInfo: any = {};
					gifts.forEach((item) => {
						if (item.giftAttrFields?.length > 0) {
							excludedInfo[item.rowId] = item.giftAttrFields.map(
								(key: string) => ({
									fieldName: key,
									order: 1
								})
							);
						}
					});
					p.excludedDetailCalculateFields = {};
					p.excludedDetailCalculateFields[this.detailApiName] =
						excludedInfo;
				}
				return p;
			}
		};
	}

	private collectAttrCalFields(
		calFieldsObj: { [api: string]: Array<string> },
		gifts: Array<GiftProductFace>
	) {
		const attrFieldsSet: Set<string> = new Set();
		gifts.forEach((g) => {
			(g.giftAttrFields || []).forEach((attr: string) =>
				attrFieldsSet.add(attr)
			);
		});
		const attrCalFieldsObj = PPM.collectCalFields(
			this.fields,
			{
				[this.detailApiName]: Array.from(attrFieldsSet)
			},
			[this.detailApiName]
		);
		return PPM.mergeArrayObj(calFieldsObj, attrCalFieldsObj);
	}
	//更新计算结果 只需要赠品明细到更新数据
	public updateCalRes(param: ExecuteFace,gifts: Array<GiftProductFace>, result: any) {
		if (!result) {
			return gifts;
		}
		const calRes = result.Value.calculateResult,
			detailRes = calRes[this.detailApiName],
			{ is_package__v, is_package, prod_pkg_key } = this.fieldMap;
		gifts = gifts.map((gift) => {
			let item = detailRes[gift.rowId] || {};
			//bom赠品去除包信息，按单独产品赠
			if (item[is_package__v]) {
				delete item[is_package__v];
				delete item[is_package];
			}
			param && this.collectChangInfo(param, gift[prod_pkg_key], item);
			return Object.assign(gift, item || {});
		});
		return gifts;
	}
	//修改赠品数量的更新
	updateCalResForModify(param: ExecuteFace, result: any) {
		if (!result) {
			return param;
		}
		let iKeyMap: any = {},
			iDetailMap: any = {};
		const calRes = result.Value.calculateResult || {},
			detailsMap = calRes[this.detailApiName] || {},
			idKeyMap = Object.keys(param.detailDataMap).reduce(
				(accMap, key) => {
					const data = param.detailDataMap[key];
					accMap[data['rowId']] = key;
					return accMap;
				},
				iKeyMap
			),
			resDetail = Object.keys(detailsMap).reduce((accRes, id) => {
				accRes[idKeyMap[id]] = detailsMap[id];
				return accRes;
			}, iDetailMap);

		Object.keys(resDetail).forEach((key: string) => {
			param.detailDataMap[key] = Object.assign(
				param.detailDataMap[key],
				resDetail[key]
			);
			param.changeInfo.mdUpdate[key] = Object.assign(
				param.changeInfo.mdUpdate[key],
				resDetail[key]
			);
		});
		return param;
	}
	//
	public async giftPriceByPriceBook(param: ExecuteFace,gifts: Array<any>){
		const result  = await this.requestGiftInfos(param,["calPriceBook"],gifts);
		return result;
	}
	// 根据单位类型获取赠品
	public async getGiftByUnit(gifts: Array<any>, unitType: string) {
		let getUnitParam: GetUnitGiftFace = {
			gifts: gifts,
			unitType: unitType,
			info: null
		};
		const result = await PPM.composeAsync(
			PPM.curry(2, this.parseUnitGifts.bind(this))(getUnitParam),
			this.requestUnit.bind(this)
		)(getUnitParam);
		return result;
	}

	requestUnit(param: GetUnitGiftFace) {
		let initialObj: any = {};

		const url =
				'FHH/EM1HNCRM/API/v1/object/price_policy/service/checkGiftUnit',
			args = {
				arg: param.gifts.reduce((argsObj: any, item: any) => {
					argsObj[item.product_id] = [param.unitType];
					return argsObj;
				}, initialObj)
			};
		return PPM.ajax(this.request, url, args, 'result');
	}

	parseUnitGifts(param: GetUnitGiftFace, res: UnitResFace) {
		const {
			product_id,
			product_id__r,
			actual_unit,
			unit,
			unit__v,
			unit__r
		} = this.fieldMap;
		//更新赠品，可能有赠品无法选中
		let usableGift: Array<any> = [],
			disableGift: Array<any> = [];
		//接口返回异常，按无可用符合单位的赠品处理
		if (!res) {
			disableGift = param.gifts.map((g) => g.product_id__s);
			param.gifts = [];
		} else {
			param.gifts.forEach((gift) => {
				let unitData = res[gift.product_id];
				if (unitData) {
					const item = unitData[param.unitType];
					gift.actual_unit = item.unit_id;
					gift.unit_name = item.unit__s;
					usableGift.push(gift);
				} else {
					disableGift.push(gift.product_id__s);
				}
			});
			param.gifts = usableGift;
		}

		if (disableGift.length >= 1) {
			param.info = disableGift.reduce((message, name) => {
				message += `"${name}",`;
				return message;
			}, '');
		}
		return param;
	}

	private allocateQuantities(
		param: ExecuteFace,
		giftData: Array<GiftProductFace>
	) {
		const { quantity } = this.fieldMap,
			qDecimal = this.decimalMap[quantity];

		// 分配主对象赠品
		if (param.masterData.gift_map) {
			this.allocateGifts(param.masterData.gift_map, qDecimal);
		}

		// 分配从对象赠品
		Object.values(param.detailDataMap).forEach((detail) => {
			if (detail.gift_map) {
				this.allocateGifts(detail.gift_map, qDecimal);
			}
		});
	}

	//对新匹配或cacheInit（编辑等初始化）数据分配&补全gift_map信息
	private allocateGifts(
		giftMap: { [ruleId: string]: GiftMapItemFace },
		qDecimal: number
	) {
		Object.entries(giftMap || {}).forEach(([ruleId, giftItem]) => {
			if (giftItem.hold_chose !== '1' || giftItem.cacheInit) {
				this.getGiftParser(giftItem.type)(ruleId, giftItem, qDecimal);
			}
		});
	}

	private getGiftParser(type: string): Function {
		const parsers: {
			[type: string]: any;
		} = {
			FIX: this.parseFixGift.bind(this),
			OPTIONAL: this.parseOptionalGift.bind(this)
		};
		return parsers[type] || (() => {}); // 默认为空函数，如果type不匹配
	}

	//初始化固定赠品
	private parseFixGift(
		ruleId: string,
		giftItem: GiftMapItemFace,
		qDecimal: number
	) {
		const { prod_pkg_key, quantity, price_book_price, pricing_period } =
				this.fieldMap,
			{ gift_basis = 'quantity', cycle_count = 1 } = giftItem;

		(giftItem.gift_list || []).forEach((gift: GiftListItemFace) => {
			gift.max_value = PPM.multiplicational(gift.max_value, cycle_count);
			const tempNum =
				gift_basis == 'amount'
					? this.getNumByPrice(
							gift.max_value,
							gift[price_book_price],
							gift[pricing_period]
					  )
					: gift.max_value;

			gift[quantity] = PolicyUtil.parseNumByDecimal(tempNum, qDecimal);
			gift.isActive = gift[quantity] > 0;
		});
	}

	//初始化可选赠品
	private parseOptionalGift(
		ruleId: string,
		giftItem: GiftMapItemFace,
		qDecimal: number
	) {
		const { prod_pkg_key, price_book_price, quantity } = this.fieldMap,
			{ cycle_count: cycleCount = 1, gift_basis = 'quantity' } = giftItem,
			maxKind = PolicyUtil.getDefaultValue(
				giftItem.gift_kind_upper_limit
			),
			totalNum = PolicyUtil.getDefaultValue(giftItem.gift_total_num),
			maxNum = PPM.multiplicational(totalNum, cycleCount) ?? Infinity;

		let activeKind: number = 0,
			activeNum: number = 0;

		for (let stage = 1; stage <= 3; stage++) {
			giftItem.gift_list.forEach((gift) => {
				const isRequiredStage =
					(stage === 1 || stage === 2) && gift.required;
				const isSequenceStage = stage === 3;

				//第一轮数据处理：价目表相关信息更新到gift_map上&计算实际最大/小值
				if (stage === 1) {
					const { max_value, min_value } = gift;
					const maxValue =
						(typeof max_value === 'string' && max_value === '') ||
						max_value == null
							? Infinity
							: Number(max_value);
					gift[quantity] = 0;
					gift.isActive = false;
					gift.max_value =
						maxValue >= 0
							? PPM.multiplicational(maxValue, cycleCount) ??
							  Infinity
							: Infinity;
					gift.min_value = min_value
						? PPM.multiplicational(min_value, cycleCount) ?? 0
						: 0;

					//按数量精度控制最大/小值
					if (gift_basis === 'quantity') {
						gift.max_value =
							gift.max_value === Infinity
								? Infinity
								: PolicyUtil.parseNumByDecimal(
										gift.max_value,
										qDecimal
								  );
						gift.min_value = PolicyUtil.parseNumByDecimal(
							gift.min_value,
							qDecimal
						);
					}
				}

				if (isRequiredStage || isSequenceStage) {
					const { quantityNum, usedKind, usedNum } =
						this.calQuantityInfo(
							gift,
							activeKind,
							maxKind,
							activeNum,
							maxNum,
							qDecimal,
							stage,
							gift_basis
						);
					gift[quantity] = quantityNum;
					gift.isActive = gift[quantity] > 0;
					activeKind = usedKind;
					activeNum = usedNum;
				}
				return gift;
			});
		}
		giftItem.gift_kind_upper_limit = maxKind;
		giftItem.gift_total_num = maxNum;
	}

	//分配可选赠品数量,gift_basis：限制金额/数量
	private calQuantityInfo(
		gift: GiftListItemFace,
		activeKind: number,
		maxKind: number,
		activeNum: number,
		maxNum: number,
		qDecimal: number,
		stage: number,
		gift_basis: string
	) {
		const { quantity, price_book_price, pricing_period } = this.fieldMap;
		if (activeNum >= maxNum) {
			return {
				quantityNum: gift[quantity],
				usedKind: activeKind,
				usedNum: activeNum
			};
		}

		const {
			required,
			max_value,
			min_value,
			isActive = false,
			[quantity]: giftQuantity = 0,
			[price_book_price]: giftPrice,
			[pricing_period]: pricingPeriod = 1
		} = gift;

		const isBaseAmount = gift_basis === 'amount',
			totalRemain = maxNum - activeNum,
			giftRemain = isBaseAmount
				? PPM.accSub(
						max_value,
						PPM.multiplicational(
							PPM.multiplicational(giftQuantity, giftPrice),
							pricingPeriod
						)
				  )
				: PPM.accSub(max_value, giftQuantity);

		let toAllocate = 0;

		switch (stage) {
			case 1:
				//第一轮分配必选品（按下限）&不能超过上限
				if (required) {
					const minQuantity = isBaseAmount
						? this.getNumByPrice(
								min_value,
								giftPrice,
								pricingPeriod
						  )
						: min_value;
					const defaultMin = Math.min(
						isBaseAmount
							? PPM.division(
									PPM.division(totalRemain, giftPrice),
									pricingPeriod
							  )
							: totalRemain,
						1
					);
					const formatQuantity = PolicyUtil.formatNumberByDecimal(
						minQuantity || defaultMin,
						qDecimal,
						1,
						'ceil'
					);
					const tempTotal = isBaseAmount
						? PPM.multiplicational(formatQuantity, giftPrice)
						: formatQuantity;
					const maxQuantity =
						max_value == Infinity
							? Infinity
							: isBaseAmount
							? PolicyUtil.formatNumberByDecimal(
									this.getNumByPrice(
										max_value,
										giftPrice,
										pricingPeriod
									),
									qDecimal
							  )
							: max_value;

					toAllocate =
						totalRemain >= tempTotal
							? Math.min(formatQuantity, maxQuantity)
							: 0;
				}
				break;
			case 2:
				//第二轮分配必选品（按上限，赠品本身上限余量和总量余量的较小值）
				if (required && max_value !== Infinity) {
					const tempRemain = Math.min(totalRemain, giftRemain),
						maxQuantity = isBaseAmount
							? this.getNumByPrice(
									tempRemain,
									giftPrice,
									pricingPeriod
							  )
							: tempRemain;
					toAllocate = PolicyUtil.formatNumberByDecimal(
						maxQuantity,
						qDecimal
					);
				}
				break;
			case 3:
				//第三轮按顺序分配所有赠品（按上限，赠品本身上限余量和总量的较小值）
				const useable = Math.min(totalRemain, giftRemain);
				toAllocate =
					useable == Infinity
						? isActive
							? 0
							: min_value || 1
						: isBaseAmount
						? this.getNumByPrice(useable, giftPrice, pricingPeriod)
						: useable;
				toAllocate = PolicyUtil.formatNumberByDecimal(
					toAllocate,
					qDecimal
				);
				break;
			default:
				throw new Error(`Unknown stage: ${stage}`);
		}

		const tempQuantity = giftQuantity + toAllocate * 1;
		const tempKind = isActive ? activeKind : activeKind + 1;
		const checkVal = isBaseAmount
			? PPM.multiplicational(
					PPM.multiplicational(tempQuantity, giftPrice),
					pricingPeriod
			  )
			: tempQuantity;

		if (toAllocate > 0 && checkVal >= min_value && tempKind <= maxKind) {
			gift[quantity] = tempQuantity;
			activeNum += isBaseAmount
				? PPM.multiplicational(
						PPM.multiplicational(toAllocate, giftPrice),
						pricingPeriod
				  )
				: toAllocate;
			if (!gift.isActive) {
				activeKind++;
				gift.isActive = true;
			}
		}

		return {
			quantityNum: gift[quantity],
			usedKind: activeKind,
			usedNum: activeNum
		};
	}

	//根据价目表价格算数量
	private getNumByPrice(
		amount: number,
		price: number,
		pricingPeriod: number = 1
	) {
		if (!price || price == 0) {
			return 1;
		}
		// 考虑周期数进行计算
		return PPM.division(PPM.division(amount, price), pricingPeriod || 1);
	}

	private async getAllowGiftDetails(
		masterData: object,
		detailDataMap: DetailDataMapFace
	) {
		let removeKeys: string[] = [];

		if (this.allowedRange) {
			const giftMap: DetailDataMapFace = {};
			Object.values(detailDataMap).forEach((item) => {
				if (item.parent_gift_key) {
					giftMap[item.prod_pkg_key] = item;
				}
			});
			removeKeys = await this.filterByRange(masterData, giftMap);
		}
		return removeKeys;
	}

	// 赠品过可售范围
	private async filterByRange(
		masterData: object,
		detailMap: DetailDataMapFace
	) {
		const url =
			'FHH/EM1HNCRM/API/v1/object/price_policy/service/gift_range_shelves';
		const args = {
			master: masterData,
			details: detailMap
		};
		const startTime = PolicyUtil.getTime();
		const result = await PPM.ajax(this.request, url, args);
		this.processLog.push({
			action: 'filterByRange',
			request: '/price_policy/service/gift_range_shelves',
			responseStatus: '200 OK',
			durationMs: PolicyUtil.getTime() - startTime,
			extraInfo: {}
		});
		return result?.remove_data_index || [];
	}

	/**************************** 并行请求接口映射 *****************************/
	// 初始化可并行请求接口映射
	private initRequestMap() {
		const {
			product_id,
			price_book_product_id,
			actual_unit,
			quantity,
			pricing_mode,
			pricing_cycle,
			pricing_rate,
			whole_period_sale,
			pricing_period,
			service_start_time,
			settlement_mode,
			settlement_cycle,
			settlement_rate,
			rowId,
			price_book_id
		} = this.fieldMap;

		const { account_id, partner_id, mcCurrency } = this.masterFieldMap;

		this.requestMap = {
			calMultiUnit: {
				url: 'FHH/EM1HNCRM/API/v1/object/mutipleUnit/service/calcPriceByUnit',
				argsFn: (param: ExecuteFace, gifts: Array<GiftProductFace>) => {
					// 筛选出多单位的赠品
					const unitGifts = gifts.filter(g => g[this.fieldMap.is_multiple_unit__v]);

					return {
						params: unitGifts.map((gift: GiftProductFace) => ({
							productId: gift[product_id],
							priceBookProductId: gift[price_book_product_id] || '',
							unitId: gift[actual_unit],
							count: gift[quantity],
							rowId: gift.rowId
						})),
						describeApiName: this.detailApiName,
						mcCurrency: this.masterData[mcCurrency]
					};
				}
			},
			calPriceBook: {
				url: 'FHH/EM1HNCRM/API/v1/object/available_range/service/get_real_price',
				argsFn: (param: ExecuteFace, gifts: Array<GiftProductFace>) => {
			
					const masterData = this.masterData;
					const oDetails = {
						[this.detailApiName]: Object.values(param.detailDataMap)
					};
					const details = (PPM as any).parsePriceBookDataRangeDetails(
						oDetails,
						this.detailDesc?.fields?.price_book_id
					);
					
					return {
						accountId: masterData[account_id],
						partnerId: masterData[partner_id] || '',
						mcCurrency: masterData[mcCurrency],
						fullProductList: gifts.map((gift) => ({
							rowId: gift.rowId,
							productId: gift[product_id],
							unit: gift[actual_unit],
							priceBookId: (gift.is_this_product
								? gift[price_book_id]
								: masterData[price_book_id]) || ''
						})),
						productIdList: gifts.map((gift) => gift[product_id]),
						object_data: masterData,
						details
					};
				}
			},
			calPeriodData: {
				url: 'FHH/EM1HNCRM/API/v1/object/periodic_product/service/calculate',
				argsFn: (param: ExecuteFace, gifts: Array<GiftProductFace>) => {
					// 筛选出周期性赠品
					const periodGifts = gifts
						.filter((gift) => pricing_mode && gift[pricing_mode] === 'cycle')
						.map((gift) => ({
							rowId: gift.rowId,
							pricingModel: gift[pricing_mode],
							pricingCycle: gift[pricing_cycle],
							pricingRate: gift[pricing_rate],
							wholePeriodSale: gift[whole_period_sale],
							pricingPeriod: gift[pricing_period],
							serviceStartTime: gift[service_start_time],
							settlementMode: gift[settlement_mode],
							settlementCycle: gift[settlement_cycle],
							settlementRate: gift[settlement_rate],
							calculateField: 'serviceEndTime'
						}));
					return {
						dataList: periodGifts
					};
				}
			}
		};
	}

	// 1. 获取可并行获取的赠品相关信息
	private async requestGiftInfos(
		param: ExecuteFace,
		specifiedKeys: string[],
		gifts: Array<GiftProductFace>
	) {
		const keysToCall: string[] = [];
		const { is_multiple_unit__v, pricing_mode = 'pricing_mod' } = this.fieldMap;
		if (gifts.some((g) => g[is_multiple_unit__v])) {
			keysToCall.push('calMultiUnit');
		}
		if (gifts.some((g) => g[pricing_mode] === 'cycle')) {
			keysToCall.push('calPeriodData');
		}
		if (gifts.length > 0) {
			keysToCall.push('calPriceBook');
		}
		if (keysToCall.length === 0) {
			return gifts;
		}

		let finalNeededKeys: string[];
		if (specifiedKeys && specifiedKeys.length > 0) {
			finalNeededKeys = keysToCall.filter((k) => specifiedKeys.includes(k));
		} else {
			finalNeededKeys = keysToCall;
		}
		const dataForReq = await this.runFetchesInParallel(
			finalNeededKeys,
			param,
			gifts
		);
		return this.handleCalculationResults(dataForReq, param, gifts,specifiedKeys);
	}

	// 2. 并行执行请求
	private async runFetchesInParallel(
		keys: string[],
		param: ExecuteFace,
		gifts: Array<GiftProductFace>
	) {
		const tasks = keys.map((key) => {
			const { url, argsFn } = this.requestMap[key];
			const args = argsFn(param, gifts);
			return PPM.ajax(this.request, url, args).then((res) => ({
				key,
				res
			}));
		});
		const startTime = PolicyUtil.getTime();
		const results = await Promise.all(tasks);
		this.processLog.push({
			action: 'requestGiftInfos',
			request: keys.join(','),
			responseStatus: '200 OK',
			durationMs: PolicyUtil.getTime() - startTime,
			extraInfo: {}
		});
		return results;
	}

	// 3. 处理计算结果
	private handleCalculationResults(
		results: Array<{ key: string; res: any }>,
		param: ExecuteFace,
		gifts: Array<GiftProductFace>,
		specifiedKeys: string[]
	) {
		
		const formattedData = this.formatResponseData(results);
		const updatedGifts = this.updateGiftsWithFormattedData(
			gifts,
			formattedData,
			specifiedKeys,
			param
		);
		return updatedGifts;
	}

	// 格式化接口返回数据并创建哈希表（Map）
	private formatResponseData(results: Array<{ key: string; res: any }>): {
		[key: string]: Map<any, any>;
	} {
		const maps: { [key: string]: Map<any, any> } = {};

		results.forEach(({ key, res }) => {
			switch (key) {
				case 'calMultiUnit':
					maps.calMultiUnit = new Map(
						(res?.caclResult || []).map((item: any) => [
							item.rowId,
							item
						])
					);
					break;
				case 'calPeriodData':
					maps.calPeriodData = new Map(
						(res?.dataList || []).map((item: any) => [
							item.rowId,
							item
						])
					);
					break;
				case 'calPriceBook':
					maps.calPriceBook = new Map(
						(res?.newRst || []).map((item: any) => [
							item.rowId,
							item
						])
					);
					break;
				default:
					break;
			}
		});

		return maps;
	}
	// 统一更新 gifts 数据
	private updateGiftsWithFormattedData(
		gifts: Array<GiftProductFace>,
		maps: { [key: string]: Map<any, any> },
		specifiedKeys: string[],
		param?: ExecuteFace,
	): Array<GiftProductFace> {
		const { prod_pkg_key } = this.fieldMap;
		// 直接遍历并修改原始的gifts对象
		for (let i = 0; i < gifts.length; i++) {
			const gift = gifts[i];
			let updatedData: any = {};

			// 1. 更新多单位数据
			if(!specifiedKeys ||specifiedKeys.includes('calMultiUnit')){
				this.updateMultiUnitData(gift, maps, updatedData);
			}

			// 2. 更新周期性数据
			if(!specifiedKeys || specifiedKeys.includes('calPeriodData')){
				this.updatePeriodData(gift, maps, updatedData);
			}
			
			// 3. 更新价格数据
			if(!specifiedKeys || specifiedKeys.includes('calPriceBook')){
				this.updatePriceData(gift, maps, updatedData);
			}

			// 如果需要更新 changeInfo 并且有更新数据
			if ( param && gift[prod_pkg_key] && Object.keys(updatedData).length > 0 ) {
				this.collectChangInfo(param, gift[prod_pkg_key], updatedData);
			}
		}

		// 返回修改后的原始gifts对象
		return gifts;
	}

	private updateMultiUnitData(gift: GiftProductFace, maps: { [key: string]: Map<any, any> }, updatedData: any) {
		const {
			product_price,
			price_book_price,
			conversion_ratio,
			base_unit_count,
			stat_unit_count,
			is_multiple_unit__v,
			gift_amortize_price,
			quantity
		} = this.fieldMap;
		if (maps.calMultiUnit && maps.calMultiUnit.has(gift.rowId)) {
			const unitData = maps.calMultiUnit.get(gift.rowId);
			const unitUpdates = {
				[product_price]: unitData.price,
				[price_book_price]: unitData.priceBookPrice,
				[conversion_ratio]: unitData.conversion_ratio,
				[base_unit_count]: unitData.base_unit_count,
				[stat_unit_count]: unitData.stat_unit_count,
				other_unit: unitData.other_unit,
				other_unit_quantity: unitData.other_unit_quantity
			};

			// 如果有初始价格，计算单位转换后的价格
			if (gift.price) {
				const priceByUnit = PPM.multiplicational(
					gift.price,
					unitData.conversion_ratio
				);
				unitUpdates[gift_amortize_price] =
					PolicyUtil.parseNumByDecimal(
						priceByUnit,
						this.decimalMap[gift_amortize_price],
						gift[gift_amortize_price],
						true
					);
			}

			// 直接修改原始gift对象
			Object.assign(gift, unitUpdates);
			Object.assign(updatedData, unitUpdates);
		} else {
			// 如果非多单位或没有找到计算结果，使用默认值
			const defaultUnitData = {
				[conversion_ratio]: '1',
				[base_unit_count]: gift[quantity],
				[stat_unit_count]: gift[quantity] + (gift.unit__r || gift.unit__s || '')
			};

			// 直接修改原始gift对象
			Object.assign(gift, defaultUnitData);
			Object.assign(updatedData, defaultUnitData);
		}
	}

	private updatePeriodData(gift: GiftProductFace, maps: { [key: string]: Map<any, any> },updatedData: any) {
		const {
			service_start_time,
			service_end_time,
			pricing_period,
			pricing_mode,
			pricing_cycle,
			pricing_rate,
			whole_period_sale,
			settlement_mode,
			settlement_cycle,
			settlement_rate,
			settlement_period
		} = this.fieldMap;
		if ( maps.calPeriodData && gift[pricing_mode] === 'cycle' && maps.calPeriodData.has(gift.rowId)) {
			const periodData = maps.calPeriodData.get(gift.rowId);
			const periodUpdates = {
				[service_start_time]: periodData.serviceStartTime,
                [service_end_time]: periodData.serviceEndTime,
                [pricing_period]: periodData.pricingPeriod,
				[pricing_mode]: periodData.pricingModel,
				[pricing_cycle]: periodData.pricingCycle,
				[pricing_rate]: periodData.pricingRate,
				[whole_period_sale]: periodData.wholePeriodSale,
				[settlement_mode]: periodData.settlementMode,
				[settlement_cycle]: periodData.settlementCycle,
				[settlement_rate]: periodData.settlementRate
			}
			if (periodData.settlementPeriod) {
				periodUpdates[settlement_period] = periodData.settlementPeriod;
			}
			// 直接修改原始gift对象
			Object.assign(gift, periodUpdates);
			Object.assign(updatedData, periodUpdates);
		}
	}

	private updatePriceData(gift: GiftProductFace, maps: { [key: string]: Map<any, any> },updatedData: any) {
		const {
			product_price,
			price_book_price,
			price_book_discount,
			price_book_id,	
			price_book_id__r,
			price_book_product_id,
			price_book_product_id__r
		} = this.fieldMap;
		if ( maps.calPriceBook && maps.calPriceBook.has(gift.rowId)) {
			const priceData = maps.calPriceBook.get(gift.rowId);
			const priceUpdates = {
				[product_price]: PolicyUtil.parseNumByDecimal(
					priceData.selling_price,
					this.decimalMap[product_price],
					priceData.selling_price,
					true
				),
				[price_book_discount]: priceData.discount,
				[price_book_price]: PolicyUtil.parseNumByDecimal(
					priceData.pricebook_price,
					this.decimalMap[price_book_price],
					priceData.pricebook_price,
					true
				),
				[price_book_id]: priceData.pricebook_id,
				[price_book_id__r]: priceData.pricebook_id__r,
				[price_book_product_id]: priceData._id,
				[price_book_product_id__r]: priceData.name
			};

			// 直接修改原始gift对象
			Object.assign(gift, priceUpdates);
			Object.assign(updatedData, priceUpdates);
		}else{
			const priceUpdates = {
				[price_book_price]: 0, //价目表价格
				priceBookPriceFlag:"not_found"
			}
			Object.assign(gift, priceUpdates);
			Object.assign(updatedData, priceUpdates);
		}
	}
}
