import {
	DetailDataMapFace,
	configFace,
	executePolicyFace,
	changeInfoFace,
	modifyInfoFace,
	reqMatchFace,
	resMatchFace,
	policyInfoFace,
	FieldMapFace,
	GiftFace,
	GiftRuleFace
} from './data_interface';
import PPM from 'plugin_public_methods';

import {
	ExecuteFace,
	PolicyDataFace,
	DetailDataFace,
	MasterDataFace,
	GiftMapItemFace,
	PolicyFace,
	PromoteLogDetail
} from './new_data_interface';

export default class PolicyUtil {
	//数组重合
	static overlapArr(arr1: Array<any>, arr2: Array<any>): boolean {
		if (!arr1 || arr1.length == 0 || !arr2 || arr2.length == 0) {
			return false;
		}
		let i = 0,
			overlap = false;
		while (i < arr1.length && !overlap) {
			overlap = arr2.findIndex((a) => a == arr1[i]) >= 0;
			i++;
		}
		return overlap;
	}
	//按对象收集字段
	static collectChangeFields(param: any) {
		let initialVal: Array<string> = [];
		const { masterApi, detailApi, masterUpdate, mdUpdate, modifyFields } =
				param,
			changeFields = {
				[masterApi]: PPM.mergeArr(
					Object.keys(masterUpdate || {}),
					modifyFields[masterApi]
				),
				[detailApi]: PPM.mergeArr(
					Object.keys(mdUpdate || {}).reduce((acc, key) => {
						let fields = Object.keys(mdUpdate[key]);
						acc = PPM.mergeArr(acc, fields);
						return acc;
					}, initialVal),
					modifyFields[detailApi]
				)
			};
		return changeFields;
	}
	//判断数组1是否有字段在数组2中
	static fieldsInCondition(
		changeFields: any,
		conditionFields: any,
		specialCondition: Array<string>
	): boolean {
		let i = 0,
			inCondition = false,
			changeFieldsArr = Object.keys(changeFields || {});

		if (!changeFields) {
			return inCondition;
		}
		while (!inCondition && i < changeFieldsArr.length) {
			const key = changeFieldsArr[i];
			inCondition =
				PolicyUtil.overlapArr(specialCondition, changeFields[key]) ||
				PolicyUtil.overlapArr(conditionFields[key], changeFields[key]);
			i++;
		}
		return inCondition;
	}
	//校验&补全match接口数据
	static verifyPolicyData(param: {
		type: string;
		matchFrom: string; //'fullyUpdate'-正常match，"cartUpdate"-购物车初始化,"noUpdate"-转换，”reviseUpdate“-编辑初始化
		nData: PolicyDataFace;
		oData: DetailDataFace | MasterDataFace;
		fieldMap: any;
		notInRes?: boolean;
		modifyIndex?: Array<string>;
		detailChangeKeys?: Array<string>;
		groupChangeData?: Array<string>;
	}) {
		const {
			type,
			matchFrom,
			oData,
			fieldMap,
			notInRes = false,
			modifyIndex = [],
			detailChangeKeys = [],
			groupChangeData = []
		} = param;
		let nData: PolicyDataFace = param.nData || {};
		const {
			prod_pkg_key,
			price_policy_id,
			master_policy_id = 'master_policy_id',
			price_policy_id__r,
			price_policy_rule_ids,
			policy_dynamic_amount
		} = fieldMap;

		// 对返回数据预处理
		const dataKeys = Object.keys(nData),
			hasPolicyId =
				dataKeys.includes(price_policy_id) ||
				dataKeys.includes(master_policy_id),
			noSelfPolicy =
				!dataKeys.includes(price_policy_id) ||
				nData[price_policy_id] == '' ||
				!nData[price_policy_id];

		const isMaster = type === "master" && !oData.prod_pkg_key; //主对象match master数据

		//返回数据为空{}，或者数据里没有政策id/分摊主对象政策id => 没有价格政策,前端补全清空参数
		if (!notInRes && (dataKeys.length <= 0 || !hasPolicyId)) {
			nData = Object.assign(nData || {}, {
				[price_policy_id]: null,
				[price_policy_rule_ids]: null,
				[policy_dynamic_amount]: 0,
				gift_map : null
			});
		}
		/**
		 * ProcessInitUpdate
		 * 购物车转订单/订单编辑的初始化更新
		 */
		if (matchFrom !== 'fullyUpdate') {
			const oPolicy = isMaster?(oData[price_policy_id] || oData.cachePrePolicyId):oData[price_policy_id]
			if (
				nData[price_policy_id] === oPolicy &&
				PolicyUtil.hasGiftPolicy(nData) &&
				!groupChangeData.includes(oData[prod_pkg_key])
			) {
				nData.gift_map = PolicyUtil.verifyGiftMap(
					matchFrom,
					nData.gift_map,
					oData,
					isMaster
				);
			} else if (!nData[price_policy_id] && oData[price_policy_id__r]) {
				nData[price_policy_id__r] = null;
			}else if(type == "detail" && PolicyUtil.hasGiftPolicy(oData) && !PolicyUtil.hasGiftPolicy(nData)){
				//购物车数据进订单初数据有gift_map,匹配价格政策未匹配上赠品政策时不反回gift_map,导致数据清不掉。只针对match detail处理
				nData.gift_map = null;
			}
			return nData;
		}

		/**
		 * ProcessNotInRes
		 * notInRes只有明细数据可能为true,针对match detail的特殊逻辑
		 * 接口未更新的原始数据里如果有gift_map,补全参数hold_chose,避免后续被更新重算
		 * 只有match master未通过match detail更新过从对象数据的场景下，没有hold_chose会导致已有赠品丢失
		 */
		if (notInRes) {
			const isDetailType = type === 'detail',
				isMasterAndNotChanged =
					type !== 'detail' &&
					!detailChangeKeys.includes(oData[prod_pkg_key]);
			if (isDetailType || isMasterAndNotChanged) {
				nData.gift_map = PolicyUtil.completeHoldChoose(oData);
			}
			return nData;
		}

		// ProcessNormal
		if (noSelfPolicy || !PolicyUtil.hasGiftPolicy(nData)) {
			//场景:主从都有赠品政策，取消从政策，只调用了matchMaster,主不再匹配政策但也不返回gift_map,特殊处理:
			nData.gift_map = null;
		} else if (
			PolicyUtil.hasGiftPolicy(nData) &&
			PolicyUtil.hasGiftPolicy(oData)
		) {
			nData.gift_map = PolicyUtil.recombineGiftMap(nData, oData);
		}
		return nData;
	}

	//是否有赠品政策
	static hasGiftPolicy(data: PolicyDataFace | null) {
		if (data?.gift_map && Object.keys(data.gift_map || {}).length !== 0) {
			return true;
		} else {
			return false;
		}
	}

	//补全gift_map上的cacheInit
	static verifyGiftMap(
		matchFrom: string,
		giftMap: {
			[ruleId: string]: GiftMapItemFace;
		} | null,
		oData: DetailDataFace | MasterDataFace,
		isMaster:boolean
	) {
		if (giftMap === null) {
			return null;
		}
		let parsedGiftMap: {
			[ruleId: string]: GiftMapItemFace;
		} = {};
		for (let ruleId in giftMap) {
			let item: GiftMapItemFace = giftMap[ruleId],
				oRules = (isMaster ? (oData.price_policy_rule_ids||oData.cachePrePolicyRules): oData.price_policy_rule_ids )|| [];
			if (matchFrom === 'cartUpdate') {
				item.gift_kind_upper_limit =
					item.gift_kind_upper_limit == null
						? Infinity
						: item.gift_kind_upper_limit;
				item.gift_total_num =
					item.gift_total_num == null
						? Infinity
						: item.gift_total_num;
			}
			if (item.hold_chose == '1' || oRules.includes(ruleId)) {
				item.cacheInit = true;
			}
			parsedGiftMap[ruleId] = item;
		}
		return parsedGiftMap;
	}

	//本地补全hold_choose逻辑
	static completeHoldChoose(data: DetailDataFace | MasterDataFace) {
		if (!PolicyUtil.hasGiftPolicy(data)) {
			return null;
		} else {
			let giftMap: {
				[ruleId: string]: GiftMapItemFace;
			} = {};
			for (let rule in data.gift_map) {
				giftMap[rule] = Object.assign(
					{
						hold_chose: '1'
					},
					data.gift_map[rule]
				);
			}
			return giftMap;
		}
	}

	static recombineGiftMap(
		nData: PolicyDataFace,
		oData: DetailDataFace | MasterDataFace
	) {
		let giftMap: {
				[ruleId: string]: GiftMapItemFace;
			} = {},
			oriGiftMap = oData.gift_map || {};
		for (let ruleId in nData.gift_map) {
			let item = oriGiftMap[ruleId],
				nItem = nData.gift_map[ruleId];
			if (item && nItem.hold_chose == '1') {
				giftMap[ruleId] = Object.assign({}, item, {
					hold_chose: '1'
				});
			} else {
				giftMap[ruleId] = nItem;
			}
		}
		return giftMap;
	}

	//从明细中过滤赠品
	static filterGifts(
		dataMap: DetailDataMapFace,
		fieldMap: FieldMapFace
	): DetailDataMapFace {
		let initialVal: DetailDataMapFace = {};

		const dataFilterGift = (Object.keys(dataMap) || [])
			.filter((key) => {
				const isBom =
					dataMap[key][fieldMap.parent_rowId || 'parent_rowId'];
				return (
					!PolicyUtil.isPolicyGift(dataMap[key], fieldMap) && !isBom
				);
			})
			.reduce((acc, key) => {
				acc[key] = dataMap[key];
				return acc;
			}, initialVal);
		return dataFilterGift;
	}

	//更新从对象dataMap类型数据
	static updateDataMap(oDataMap: any, nDataMap: any): any {
		let initVal: any = {};
		return Object.keys(oDataMap).reduce(
			(dataMap: DetailDataMapFace, key: string) => {
				let updateItem = nDataMap[key] || {};
				dataMap[key] = Object.assign(oDataMap[key], updateItem);
				return dataMap;
			},
			initVal
		);
	}

	static collectDataMapFields(dDataMap: any = {}) {
		let dFields: Array<string> = [];
		Object.keys(dDataMap).forEach((key: string) => {
			Object.keys(dDataMap[key] || {}).forEach((f) => {
				!dFields.includes(f) && dFields.push(f);
			});
		});
		return dFields;
	}

	static incrementalUpdate(oldObj: any = {}, newObj: any): any {
		//根据指定属性合并数组
		for (let key in newObj) {
			switch (key) {
				case 'pricePolicies':
					let oldPolicies = (oldObj && oldObj.pricePolicies) || [],
						newPolicies = (newObj && newObj.pricePolicies) || [];
					newPolicies.forEach((newP: any) => {
						let policy = oldPolicies.find(
							(op: any) => op.id == newP.id
						);
						if (policy) {
							newP.rules.forEach((newItem: any) => {
								let itemExist =
									policy.rules.findIndex(
										(oldItem: any) =>
											oldItem.id == newItem.id
									) >= 0;
								if (!itemExist) {
									policy.rules.push(newItem);
								}
							});
						} else {
							oldPolicies.push(newP);
						}
					});
					oldObj.pricePolicies = oldPolicies;
					break;
				case 'masterData':
				case 'detailDataMap':
					break;
				default:
					oldObj[key] = Object.assign(
						oldObj[key] || {},
						newObj[key] || {}
					);
			}
		}
		return oldObj;
	}

	//按小数位格式化数值
	static parseNumByDecimal(
		val: any,
		decimal: number = 0,
		defaultVal?: number,
		isToFix?: boolean
	): number {
		let parsedVal = parseFloat(val);
		if (!isNaN(parsedVal)) {
			let valStr = val.toString(),
				dotPos = valStr.indexOf('.');
			if (dotPos >= 0) {
				if (isToFix) {
					valStr = Number(valStr).toFixed(decimal);
				} else {
					if (dotPos == 0) {
						valStr = `0${valStr}`;
						dotPos = 1;
					}
					valStr = valStr.substring(0, dotPos + decimal * 1 + 1 * 1);
				}
			}
			return Number(valStr);
		}

		return defaultVal !== undefined ? defaultVal : val;
	}

	// 按小数位格式化数值
	// type: 'truncate'（默认截断）, 'round'（四舍五入）, 'ceil'（向上取整）
	static formatNumberByDecimal(
		val: any,
		decimal: number = 0,
		defaultVal: number = 0,
		type: string = 'truncate'
	): number {
		let parsedVal = parseFloat(val);
		if (isNaN(parsedVal)) {
			return defaultVal;
		}

		const factor = Math.pow(10, decimal);
		switch (type) {
			case 'round':
				// 四舍五入
				return Math.round(parsedVal * factor) / factor;
			case 'ceil':
				// 向上取整
				return Math.ceil(parsedVal * factor) / factor;
			default:
				// 截断
				return Math.floor(parsedVal * factor) / factor;
		}
	}

	//收集计算接口入参
	static getCalArgs(
		param: any,
		masterApi: string,
		detailApi: string,
		calFieldsObj: any,
		opType: string,
		calIndex?: Array<string>,
		detailsArr?: Array<any>,
		filterFields?: any
	) {
		detailsArr = Array.isArray(detailsArr) && detailsArr.length > 0
				? detailsArr
				: Object.values(param.detailDataMap);
				
		const keyIdMap = detailsArr.reduce((accMap, item) => {
			accMap[item.prod_pkg_key] = item.rowId;
			return accMap;
		}, {});
		const modifyRowIds = (calIndex || param.modifyInfo.modifyIndex || [])
			.map((prodKey: string) => keyIdMap[prodKey])
			.filter((rowId: string) => rowId);

		return {
			noMerge: true,
			noLoading: true,
			noRetry: true,
			changeFields: [],
			filterFields: filterFields || {},
			extraFields: calFieldsObj,
			operateType: opType,
			dataIndex: modifyRowIds,
			objApiName: detailApi,
			masterData: param.masterData,
			details: {
				[detailApi]: detailsArr
			}
		};
	}

	static async checkRequestRes(param:any,key:string,sendLog:any){
		return new Promise((resolve,reject)=>{
			if(param[key]){
				resolve(param);
			}else{
				sendLog({
					eventId: 'fs-crm-sfa-pricePolicyPlugin-error',
                	eventType: 'PROD',
                    eventName: 'pv',
                	// 对象ApiName
                	apiName:"price_policy",
					data:{
						res:param[key],
						data:param.promoteProcessLog
					}
				})
			}
		})
	}

	//判断计算接口返回状态，失败时阻断流程
	static async updateCalResAsync(
		param: executePolicyFace | ExecuteFace,
		result: any,
		masterApi: string,
		detailApi: string,
		specialMasterFields?: Array<string>,
		callBack?: any
	) {
		return new Promise((resolve) => {
			if (result?.StatusCode || result?.Result?.StatusCode) {
				callBack && callBack(result);
			} else {
				const data = PolicyUtil.updateCalRes(
					param,
					result,
					masterApi,
					detailApi,
					specialMasterFields
				);
				resolve(data);
			}
		});
	}

	//标准格式化计算接口返回参数
	static updateCalRes(
		param: executePolicyFace | ExecuteFace,
		result: any,
		masterApi: string,
		detailApi: string,
		specialMasterFields?: Array<string>
	) {
		const calRes = result && result.Value && result.Value.calculateResult;
		if (!calRes) {
			return param;
		}
		let iKeyMap: any = {},
			iDetailMap: any = {};
		const resMaster = calRes[masterApi][0],
			detailsMap = calRes[detailApi] || {},
			idKeyMap = Object.keys(param.detailDataMap).reduce(
				(accMap, key) => {
					const data = param.detailDataMap[key];
					accMap[data['rowId']] = key;
					return accMap;
				},
				iKeyMap
			),
			resDetail = Object.keys(detailsMap).reduce((accRes, id) => {
				accRes[idKeyMap[id]] = detailsMap[id];
				return accRes;
			}, iDetailMap);

		//特殊逻辑，如果specialMasterFields（针对matchDetail后那次batch计算，如果price_book_amount, product_amount两个字段值未变，就从修改字段里删除）
		const modifyFields = param.calArgs?.extraFields || {};
		if (modifyFields[masterApi]?.length && specialMasterFields?.length) {
			modifyFields[masterApi] = modifyFields[masterApi].filter(
				(field: string) => {
					// 如果字段不在 specialMasterFields 列表中，保留它
					if (!specialMasterFields.includes(field)) return true;

					// 如果字段在 specialMasterFields 列表中，但在 resMaster 和 masterData 中的值不同，保留它
					if (resMaster[field] !== param.masterData[field])
						return true;

					// 否则，过滤掉这个字段
					return false;
				}
			);
		}

		return {
			masterData: Object.assign(param.masterData, resMaster),
			detailDataMap: PolicyUtil.updateDataMap(
				param.detailDataMap,
				resDetail
			),
			changeInfo: PPM.updateChangeInfo(
				param.changeInfo,
				resMaster,
				resDetail
			),
			//modifyInfo: PPM.generateModifyInfo(resMaster, resDetail, masterApi, detailApi),
			modifyInfo: param.calArgs
				? {
						modifyFields: modifyFields,
						modifyIndex: (param.calArgs?.dataIndex || []).map(
							(id: string) => idKeyMap[id]
						)
				  }
				: PPM.generateModifyInfo(
						resMaster,
						resDetail,
						masterApi,
						detailApi
				  ),
			policyInfo: param.policyInfo,
			matchArgs: param.matchArgs,
			matchRes: param.matchRes
		};
	}

	//价格政策系统赠品
	static isPolicySystemGift(data: any, fieldMap: FieldMapFace) {
		const { is_giveaway, parent_gift_key } = fieldMap,
			systemGift = data[is_giveaway] === '1' && data[parent_gift_key];

		return systemGift;
	}

	//价格政策全部赠品：系统赠品&临时赠品、返利品
	static isPolicyGift(data: any, fieldMap: FieldMapFace) {
		const { is_giveaway, parent_gift_key } = fieldMap,
			systemGift =
				['1', '2'].includes(data[is_giveaway]) &&
				(data[parent_gift_key] || data.rebate_coupon_id),
			manualGift = data.gift_type == 'manual';
		return systemGift || manualGift;
	}

	//
	static filterDataMap(
		filterOuts: Array<string>,
		dataMap: { [keys: string]: any } = {}
	) {
		let initialVal: any = {};
		return Object.keys(dataMap).reduce((acc: any, key: string) => {
			if (!filterOuts.includes(key)) {
				acc[key] = dataMap[key];
			}
			return acc;
		}, {});
	}

	//主对象数据当前规则里是否有分摊/不分摊规则
	static hasRuleByType(
		rules: Array<string>,
		config: configFace | null,
		amortize: boolean
	): boolean {
		const api = amortize
				? 'masterAmortizeRuleIdList'
				: 'masterNoAmortizeRuleIdList',
			targetRules = config ? config[api] : [],
			hasTargetType = PolicyUtil.overlapArr(rules, targetRules);
		return hasTargetType;
	}

	//获取配置里有主对象无分摊/不分摊规则
	static getMasterRule(
		config: configFace | null,
		amortize: boolean
	): boolean {
		const api = amortize
				? 'masterAmortizeRuleIdList'
				: 'masterNoAmortizeRuleIdList',
			rules = config ? config[api] : [];
		return rules.length >= 1;
	}

	//
	static collectGroupMap(dataMap: DetailDataMapFace) {
		const groupMap: {
			[group_key: string]: Array<string>;
		} = {};
		Object.entries(dataMap).forEach(([key, item]) => {
			if (item.group_key) {
				groupMap[item.group_key] = groupMap[item.group_key] || [];
				groupMap[item.group_key].push(key);
			}
		});
		return groupMap;
	}

	static collectGroupChangeData(
		oMap: { [group_key: string]: Array<string> } = {},
		nMap: { [group_key: string]: Array<string> } = {}
	) {
		const changeKeys: Array<string> = [];
		Object.entries(oMap).forEach(([key, dataKeys]) => {
			if (!nMap[key] || !PolicyUtil.isSameArray(dataKeys, nMap[key])) {
				changeKeys.push(...dataKeys);
			}
		});
		return changeKeys;
	}

	static isSameArray(arr1: Array<string> = [], arr2: Array<string> = []) {
		return (
			arr1.length === arr2.length &&
			arr1.every((a: string) => arr2.includes(a))
		);
	}

	//获取当前时间，时间戳
	static getTime(){
		return new Date().getTime();
	}

	//生成log信息
	static createLogDetail(param:ExecuteFace,timeSpan:number,logInfo:PromoteLogDetail){
		const item:PromoteLogDetail = {
			...logInfo,
    		durationMs: this.getTime() - timeSpan,
		};
		param?.promoteProcessLog?.details.push(item);
	}

	static getDefaultValue(data:any) {
		if (data === "" || data === null || data === undefined) {
		  return Infinity;
		} 
		const numericData = Number(data);
		return isNaN(numericData) ? Infinity : numericData;
	  }  
}
