/**
 * 价格政策限额限量
 */
import { LimitFace, LimitResFace, LimitRequestFace, executePolicyFace, FieldMapFace } from "./data_interface";
import {
    ExecuteFace
} from "./new_data_interface";
import PPM from "plugin_public_methods";

export default class PolicyLimit {
    public limits: Array<any>;            //当前有的限额限量政策
    public overLimitKey: Array<string>;   //超过限额限量政策的明细prod_pkg_key
    public hasLimitKey: Array<string>;    //有限额限量政策的明细prod_pkg_key
    private processLog: Array<any> = [];
    constructor(
        public masterApiName: string,
        public detailApiName: string,
        public fromType: string,       //'Add','Edit'
        public request: any,
        public fieldMap: FieldMapFace
    ) {
        this.limits = [];
        this.overLimitKey = [];
        this.hasLimitKey = [];
    }
    private refreshProcessLog() {
        this.processLog = [];
    }
    //获取限额限量信息
    public async getLimitInfo(requestLimit: boolean, param: ExecuteFace): Promise<LimitResFace> {

        this.refreshProcessLog();
        if (requestLimit) {
            const startTime = Date.now();
            this.limits = await this.reqLimit(param);
            const endTime = Date.now();
            this.processLog.push({
                action: "getLimitInfo",
                durationMs: endTime - startTime,
                request: '/policy_occupy/service/getPolicyOccupyBeyond',
                responseStatus: '200 OK',
                extraInfo: {}
            });
        }
        const groupMap = this.collectGroupInfo(param);

        this.limits = this.calculateLimitData(this.limits, param);
        this.overLimitKey = this.getOverLimitData(this.limits, groupMap) || [];
        this.hasLimitKey = this.getHasLimitData(this.limits, groupMap) || [];
        param.promoteProcessLog?.details?.push(...this.processLog);
        return {
            limits: this.limits,
            overLimitKey: this.overLimitKey,
            hasLimitKey: this.hasLimitKey
        }
    }

    //获取指定数据的限额限量信息
    public getLimitByData(data: any): LimitResFace {
        return {
            limits: this.limits.filter(l => this.dosePolicyMatch(data.price_policy_id, l.condition)),
            overLimitKey: this.overLimitKey,
            hasLimitKey: this.hasLimitKey
        }
    }
    //通过限额限量接口获取信息
    private reqLimit(param: ExecuteFace) {
        const args: LimitRequestFace = {
            actionCode: this.fromType == "edit" ? "Edit" : "Add",
            object_data: param.masterData,
            detailApiName: this.detailApiName,
            details: {
                [this.detailApiName]: Object.keys(param.detailDataMap).map(key => {
                    let item = Object.assign({}, param.detailDataMap[key]);
                    if ('gift_map' in item) {
                        delete (item as any)['gift_map'];
                    }
                    return item;
                })
            }
        },
            url = "FHH/EM1HNCRM/API/v1/object/policy_occupy/service/getPolicyOccupyBeyond";
        return PPM.ajax(this.request, url, args, 'policyBeyond');
    }
    /**
      * 计算当前limit详细信息数据 :已用额度，约束数据
      * 如果没有订单明细，不计算
      */
    private calculateLimitData(limits: Array<any> = [], param: ExecuteFace) {
        const amortizeInfoMap = param.policyInfo && param.policyInfo.amortizeInfoMap || {},
            { masterData = {}, detailDataMap = {} } = param;

        return limits.map((limit) => {
            const limitInfo = this.calLimitInfo(limit, masterData, detailDataMap, amortizeInfoMap);
            return Object.assign({}, limit, limitInfo);
        });
    }

    private calLimitInfo(limit: any, masterData: any, detailDataMap: { [key: string]: any }, amortizeInfoMap: { [key: string]: any }) {
        const isLimitMaster = this.dosePolicyMatch(masterData.price_policy_id, limit.condition);
        if (!this.checkMasterMap(limit.condition, masterData)) {
            return {
                limitData: [],
                usedAmount: 0,
                isLimitMaster: isLimitMaster
            }
        }
        const detailData = Object.keys(detailDataMap || {}).map(key => detailDataMap[key]),
            conditionMap = this.parseLimitConditionMap(limit.condition);

        let limitInfo = [];
        switch (limit.limit_type) {
            case 'GIFT_QUANTITY':
            case 'GIFT_AMOUNT':
                const type = limit.limit_type == 'GIFT_QUANTITY' ? 'quantity' : 'amount'
                limitInfo = this.calGiftLimits(conditionMap, detailData, type);
                break;
            case 'ORIGINAL_QUANTITY':
                limitInfo = this.calOriginalQuantity(limit, conditionMap, detailData, isLimitMaster)
                break;
            case 'ORIGINAL_AMOUNT':
                limitInfo = this.calOriginalAmount(limit, conditionMap, detailData, isLimitMaster, masterData, amortizeInfoMap)
                break;
        }
        return {
            limitData: limitInfo[0],
            usedAmount: limitInfo[1],
            isLimitMaster: isLimitMaster
        }
    }

    //如果有针对主对象上字段限量，校验当前订单是否符合条件
    private checkMasterMap(c: any, m: any) {
        if (c.aggregateObjApiName && c.aggregateObjApiName == this.masterApiName) {
            //客户等额不用判断
            if (c.aggregateFieldApiName == "account_id" && !c.limitFieldValue) {
                return true;
            } else {
                return this.isEqual(c.limitFieldValue, m[c.aggregateFieldApiName]);
            }
        }
        return true;
    }

    private isEqual(value1: any, value2: any): boolean {
        value1 = value1 ?? "";
        value2 = value2 ?? "";
        const parseJSON = (value: any) => {
            if (typeof value === 'string') {
                try {
                    return JSON.parse(value);
                } catch (e) {
                    return value;
                }
            }
            return value;
        };
        const arrayToString = (arr: any[]) => arr.sort().join(",");

        if (Array.isArray(value2)) {
            const parsedValue1 = parseJSON(value1);
            if (Array.isArray(parsedValue1)) {
                return arrayToString(parsedValue1) === arrayToString(value2);
            } else {
                return false;
            }
        } else {
            return value1.toString() === value2.toString();
        }
    }

    //针对从对象限量条件
    private parseLimitConditionMap(c: any) {
        let conditionMap: any = {
            is_giveaway: c.dimension == "gift",
        };
        if (c.range == "MULTI_POLICY") {
            conditionMap.policy_ids = c.policy_ids
        } else {
            conditionMap.price_policy_id = c.policy_id
        }
        if (c.range == "RULE") {
            conditionMap.price_policy_rule_ids = c.policy_rule_id;
        }
        if (c.aggregateObjApiName == this.detailApiName) {
            conditionMap[c.aggregateFieldApiName] = c.limitFieldValue;
        }
        if (c.product_id) {
            conditionMap.product_id = c.product_id;
        }
        return conditionMap;
    }
    //检查限额限量条件，判断数据是否满足
    private isMeetCondition(d: any, conditionMap: { [field: string]: any }) {
        let i = 0,
            flag = true;
        const keys = Object.keys(conditionMap);
        while (flag && i < keys.length) {
            const key = keys[i];
            //多政策根据key特殊判断
            if (key == "policy_ids") {
                flag = conditionMap[key].includes(d.price_policy_id);
            } else if (typeof conditionMap[key] == "boolean") {
                flag = (d[key] == "1") == conditionMap[key];
            } else if (Array.isArray(d[key])) {
                flag = d[key].includes(conditionMap[key]);
            } else {
                flag = d[key] == conditionMap[key];
            }
            i++;
        }
        return flag;
    };


    private dosePolicyMatch(policyId: string, condition: any) {
        if (condition.range == "MULTI_POLICY") {
            return condition.policy_ids.includes(policyId);
        } else {
            return condition.policy_id == policyId;
        }
    }

    //限赠品(数量/金额):找到匹配的赠品明细
    private calGiftLimits(conditionMap: { [field: string]: any }, detailData: Array<any>, type: string) {
        let initialVal: number = 0;
        const dataMeetLimit = detailData.filter(d => {
            if (this.isMeetCondition(d, conditionMap)) {
                return d;
            }
        }),
            used = dataMeetLimit.reduce((total: number, cur: any) => {
                if (type == 'quantity') {
                    total = PPM.accAdd(total, cur.quantity);
                } else {
                    const curAmount = PPM.multiplicational(cur.gift_amortize_price, cur.quantity);
                    total = PPM.accAdd(total, curAmount);
                }
                return total;
            }, initialVal);
        return [dataMeetLimit, used]
    }
    //限本品数量:主对象政策-全部明细(不包括赠品)数量；从对象政策-匹配的明细数量
    private calOriginalQuantity(limit: any, conditionMap: { [field: string]: any }, detailData: Array<any>, isLimitMaster: boolean) {
        let initialVal: number = 0,
            dataMeetLimit = detailData.filter(d => !this.isFilterData(d));

        if (!isLimitMaster) {
            dataMeetLimit = dataMeetLimit.filter(d => this.isMeetCondition(d, conditionMap));
        }
        const used = dataMeetLimit.reduce((total, cur) => {
            total = PPM.accAdd(total, cur.quantity ?? 0);
            return total;
        }, initialVal);

        const actualLimitDetails = this.getActualLimitDetails(isLimitMaster, limit.condition.range, dataMeetLimit, conditionMap);

        return [actualLimitDetails, used];
    }

    private getActualLimitDetails(isLimitMaster: boolean, range: string, dataMeetLimit: Array<any>, conditionMap: { [field: string]: any },) {
        let actualLimitDetails = isLimitMaster ? [] : dataMeetLimit;
        if (isLimitMaster && range === "MULTI_POLICY") {
            actualLimitDetails = dataMeetLimit.filter(d => this.isMeetCondition(d, conditionMap));
        }
        return actualLimitDetails;
    }


    //限本品优惠金额
    private calOriginalAmount(limit: any, conditionMap: { [field: string]: any }, detailData: Array<any>, isLimitMaster: boolean, masterData: any, amortizeInfoMap: { [key: string]: any },) {

        const { condition } = limit;
        const { range, policy_id, policy_rule_id, policy_ids } = condition;
        let key = range === "MULTI_POLICY" ? null : policy_id;
        let dataMeetLimit = detailData.filter(d => !this.isFilterData(d));

        if (range == 'RULE') {
            key = `${key}_${condition.policy_rule_id}`
        }
        //主对象政策的限本品，本品指全部明细；非主对象政策，要筛选具体符合条件的明细
        if (!isLimitMaster) {
            dataMeetLimit = dataMeetLimit.filter(d => this.isMeetCondition(d, conditionMap))
        }

        const used = dataMeetLimit.reduce((total, cur) => {
            let targetRules = (isLimitMaster ? masterData : cur)["price_policy_rule_ids"];
            let curAmount = 0;

            if (condition.range === "MULTI_POLICY") {
                //多政策限量，可能当前数据的政策和整单的政策都满足
                //计算整单分摊
                if (isLimitMaster) {
                    targetRules = masterData.price_policy_rule_ids;
                    key = masterData.price_policy_id;
                    curAmount += this.calAmortize(key, cur.prod_pkg_key, range, amortizeInfoMap, targetRules, isLimitMaster);
                }
                //计算单品分摊
                if (policy_ids.includes(cur.price_policy_id)) {
                    targetRules = cur.price_policy_rule_ids;
                    key = cur.price_policy_id;
                    curAmount += this.calAmortize(key, cur.prod_pkg_key, range, amortizeInfoMap, targetRules, isLimitMaster);
                }
            } else {
                curAmount = this.calAmortize(key, cur.prod_pkg_key, range, amortizeInfoMap, targetRules, isLimitMaster);
            }
            total = PPM.accAdd(total, curAmount);
            return total;
        }, 0);
        const actualLimitDetails = this.getActualLimitDetails(isLimitMaster, range, dataMeetLimit, conditionMap);
        return [actualLimitDetails, used];
    }
    //获取分摊额
    private calAmortize(
        key: string,
        dataKey: string,
        range: string,
        amortizeInfoMap: { [key: string]: any },
        targetRules: Array<string>,
        isLimitMaster: boolean
    ) {
        const getAmortize = (policy_rule_key: string, targetKey: string) => {
            let amortizeKey = `${policy_rule_key}_${targetKey}`,
                amortizeMasterKey = `${policy_rule_key}_MASTER`,
                curAmortize = isLimitMaster ? (amortizeInfoMap[amortizeMasterKey] || amortizeInfoMap[amortizeKey]) : amortizeInfoMap[amortizeKey],
                amount = (curAmortize && curAmortize.policy_dynamic_amount || 0) * -1;
            return amount;
        };

        switch (range) {
            case 'RULE':
                return getAmortize(key, dataKey);
            default:
                let initialVal: number = 0;
                return targetRules.reduce((total, ruleId) => {
                    return PPM.accAdd(total, getAmortize(`${key}_${ruleId}`, dataKey));
                }, initialVal);
        }
    }
    //过滤价格政策赠品和bom子件
    private isFilterData(data: any) {
        const { parent_gift_key, parent_rowId } = this.fieldMap;
        return data[parent_gift_key] || data[parent_rowId];
    }
    //获取超额限额限量政策的明细prod_pkg_key
    public getOverLimitData(limits: Array<any> = [], groupMap: Map<string, Array<any>>): Array<string> {
        let initialArr: Array<any> = [];
        const overLimitKeys = limits.filter((l: any) => l.usedAmount > l.usable)
            .reduce((keyArr, limit) => {
                keyArr = this.collectLimitKeys(keyArr, limit, groupMap);
                return keyArr;
            }, initialArr);
        return overLimitKeys;
    }
    //获取有限额限量政策的明细prod_pkg_key
    public getHasLimitData(limits: Array<any> = [], groupMap: Map<string, Array<any>>): Array<string> {
        let initialArr: Array<any> = [];
        const hasLimitKeys = (limits || [])
            .reduce((keyArr, limit) => {
                keyArr = this.collectLimitKeys(keyArr, limit, groupMap);
                return keyArr;
            }, initialArr);
        return hasLimitKeys;
    }
    /**
     * 收集明细的key:prod_pkg_key
     * 如果是限制主对象政策，key:'master'
     * 如果是限制赠品，key:赠品所属的本品明细的prod_pkg_key
     */
    private collectLimitKeys(keys: Array<string>, limit: any, groupMap: Map<string, Array<any>>): Array<string> {
        if (limit.isLimitMaster) {
            !keys.includes('master') && keys.push('master');
        }
        if (!limit.limitData || limit.limitData.length <= 0) {
            return keys;
        }
        const targetKey = ["ORIGINAL_AMOUNT", "ORIGINAL_QUANTITY"].includes(limit.limit_type) ? "prod_pkg_key" : "parent_gift_key";
        keys = limit.limitData.reduce((keyArr: Array<string>, data: any) => {
            if (targetKey == "parent_gift_key" && data.group_key) {
                const groupKeys = groupMap.get(data.group_key) || [];
                groupKeys.forEach(k => {
                    !keys.includes(k) && keys.push(k);
                })
            } else if (data[targetKey]) {
                !keys.includes(data[targetKey]) && keys.push(data[targetKey]);
            }
            return keys;
        }, keys)
        return keys;
    }

    private collectGroupInfo(param: ExecuteFace) {
        const groupMap = new Map<string, string[]>();
        const { detailDataMap = {} } = param;

        Object.entries(detailDataMap).forEach(([key, data]) => {
            // 确保仅对非系统赠品的分组进行操作
            if (data.group_key && !data.parent_gift_key) {
                // 直接在检测到 group_key 不存在时初始化数组，并添加当前项
                groupMap.set(data.group_key, [...(groupMap.get(data.group_key) || []), data.prod_pkg_key]);
            }
        });
        return groupMap;
    }

}
