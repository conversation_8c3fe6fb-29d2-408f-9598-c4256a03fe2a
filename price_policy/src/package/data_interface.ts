//export namespace PolicyInterface {

//映射字段
export interface fieldMap {

}
//小数位数字段
export interface decimalMap {

}
export interface configFace {
	accountId: string;
	conditionFieldMap: {
		[objectApi: string]: Array<string>;
	};
	masterAmortizeRuleIdList: Array<string>;
	masterNoAmortizeRuleIdList: Array<string>;
	hasPricePolicy: boolean;
	pricePolicies: Array<any>;
}
export interface requestInfoFace {
	url: string;
	args: any;
	result: any | null;
}
export interface contextFace {
	requestId: string;
	masterApiName: string;
	detailApiName: string;
	fromType: string;
	recordType: string;
	request: any;
	getRowBasicData: any;
	triggerCal: any;
	triggerUIEvent: any;
	policyConfig: configFace | null;
}
export interface changeInfoFace {
	masterUpdate: any;
	mdUpdate: any;
	mdAdd: Array<any>;
	mdDel: Array<any>;
	[props: string]: any;
}
export interface modifyInfoFace {
	modifyFields: {
		[objApi: string]: Array<string>;
	};
	modifyIndex: Array<string>;
	triggerPolicy?: boolean;
}
interface DetailDataFace {
	prod_pkg_key: string;
	[props: string]: any;
}
export interface DetailDataMapFace {
	[prod_pkg_key: string]: DetailDataFace;
}
export interface policyInfoFace {
	amortizeInfoMap: {
		[key: string]: object
	};
	detailPricePolicyMap: {
		[key: string]: {
			[policyId: string]: Array<string>;
		};
	};
	groupMap: {
		[groupKey: string]: Array<string>;
	};
	masterPricePolicy: {
		[key: string]: Array<string>;
	};
	pricePolicies: Array<any>;
	limit?: Array<any>;
	initChangeInfo?:any;
}
export interface executePolicyFace {
	masterData: {
		account_id: string;
		[props: string]: any;
	};

	detailDataMap: DetailDataMapFace;
	changeInfo: changeInfoFace;
	modifyInfo: modifyInfoFace;
	policyInfo: policyInfoFace|any;
	matchArgs: reqMatchFace;
	matchRes: resMatchFace;
	matchFrom: string;       //区分editInit和普通match等
	oriUiValue?:any;
	calArgs?:any;    
	allDetailsInfo?:object;
	scene?:string; //单据场景：新建、编辑、转化等
	triggerCal?:any;
	triggerCalAndUIEvent?:any;
}
export interface modifyPolicyFace {
	masterData: {
		account_id: string;
		[props: string]: any;
	};

	detailDataMap: DetailDataMapFace;
	type: string;
	dataKey: string;
	cancelArgs: any;
	cancelInfo: any;
	cancelRes: any;
	policyInfo?: policyInfoFace;
	targetPolicy?: string;
	promoteProcessLog?:any
	allDetailsInfo?:object;
}
export interface reqMatchFace {
	batchNo: string;
	requestId: string;
	matchType: string;
	masterObjectApiName: string;
	accountId: string;
	masterData: {
		account_id: string;
		[props: string]: any;
	};
	detailDataMap: DetailDataMapFace;
	modifiedDataIndexList: Array<string>;
	modifiedFieldApiNames: {
		[api: string]: Array<string>;
	};
	removeGroupKeySet: Array<string>;
	exactlyMatchModifiedData: boolean;
	pricePolicyId?: string;
	requestSource:string;
}

export interface resMatchFace extends policyInfoFace {
	masterData: {
		account_id: string;
		[props: string]: any;
	};

	detailDataMap: DetailDataMapFace;

}

export interface reqCalFace {
	noMerge: boolean;
	noLoading: boolean;
	noRetry: boolean;
	changeFields: Array<string>;
	filterFields: {
		[api: string]: Array<string>;
	};
	extraFields: { [api: string]: Array<string> };
	operateType: string;
	dataIndex: Array<string>;
	objApiName: string;
	masterData: object;
	details: {
		[api: string]: Array<any>;
	};
	parseParam?:any;
}


export interface resCalFace {
	[apiName: string]: {
		[index: string]: Object;
	} | {
		0: Object;
	};
}
export interface masterFieldMapFace {
	/*产品相关字段*/
	_id:string;
	account_id: string;
	partner_id:string;
	mcCurrency:string;
	/*价格政策相关字段*/
	price_policy_id: string;
	price_policy_id__r:string;      //server不下发，初始化生成
	price_policy_rule_ids:string;
	policy_dynamic_amount: string;
	price_book_amount:string;
	product_amount:string;
}
export interface FieldMapFace extends masterFieldMapFace{

	/*产品相关字段*/
	id:string;
	rowId:string;                    //
	record_type:string;
	product_id:string;
	product_id__r:string;             //产品名称，server不下发，初始化生成
	product_price:string;             //价格
	discount:string;                  //折扣
	quantity:string;                  //数量
	unit:string;
	unit__v:string;                   //server不下发，初始化生成
	unit__r:string;                   //server不下发，初始化生成
	is_giveaway: string;  
	is_package:string;                //是否bom产品 
	is_package__v:string;             //server不下发，初始化生成
	parent_rowId:string;              //判断bom子件

	/*多单位相关字段*/
	is_multiple_unit:string;
	is_multiple_unit__v:string;       //server不下发，初始化生成
	actual_unit:string;
	conversion_ratio:string;
	base_unit_count:string;
	stat_unit_count:string;

	/*价目表相关字段*/
	price_book_id:string;                // 价目表id
	price_book_id__r:string;             // 价目表名称，server不下发，初始化生成
	price_book_discount: string;         //价目表折扣
	price_book_price: string;            //价目表价格
	price_book_product_id: string;     // 价目表产品id
	price_book_product_id__r: string;  // 价目表明细，server不下发，初始化生成
	
	/*价格政策相关字段*/
	data_index:string;
	prod_pkg_key:string;
	master_policy_id: string;
	price_policy_id: string;          //价格政策
	price_policy_id__r:string;        // 价格政策名称，server不下发，初始化生成
	price_policy_rule_ids:string;     //价格政策规则
	policy_dynamic_amount: string;    //促销优惠额
	group_key: string;
	parent_gift_key: string;
	gift_amortize_price: string;      //赠品分摊价格
	amortize_amount:string;

	stand_price:string;
	dynamic_amount:string;

	rebate_coupon_id:string;

	pricing_period:string;
	pricing_mode:string;
	pricing_cycle:string;
	pricing_rate:string;
	whole_period_sale:string;
	settlement_mode:string;
	settlement_cycle:string;
	settlement_rate:string;
	service_start_time:string;
	service_end_time:string;
	settlement_period:string;
}

export interface DecimalMapFace {
	quantity: number;
	gift_amortize_price: number;
	product_price: number;
	price_book_price: number;
	policy_dynamic_amount: number;
	[field:string]:number;
}
/*********************************************/
/****************赠品相关数据格式****************/
/*********************************************/

export interface GiftFace {
	/*价格政策赠品相关字段*/
	is_this_product:boolean;
	max_value: number;
	min_value: number;
	required: boolean;
	isActive: boolean;

    /*产品字段*/
	[productField:string]:any;
	product_id: string;
	product_id__s: string;
	price: number;
	quantity: number;
	prod_pkg_key: string;
	unit: string; //FIXME:查看时unit还是unit_id
	is_multiple_unit: boolean;
	price_book_id:string;
}

export interface GiftProductFace extends GiftFace {
	id: string;
	product_price: string;
	discount: string;
	actual_unit: string;
	gift_amortize_price: number; //赠品分摊价格
	price_book_price: string;
	unit__r: string;
	price_book_product_id: string;
	[prop: string]: any;
}

export interface GiftMapFace {
	[ruleId: string]: GiftRuleFace
}
export interface GiftRuleFace {
	type: string;
	object_api_name: string;
	gift_total_num: number|string;
	gift_kind_upper_limit: number;
	cycle_count: number;
	gift_list: Array<GiftFace>;
	hold_chose: string;
	maxKind: number;
	maxNum: number;
	cacheInit: boolean;
	group_key?:string;
}
//校验方法入参
export interface ValidFace {
	args: {
		objectDataId: string;   //主对象_id
		objectDescribeApiName: string;   //主对象apiname
	}
	policyIds: Array<string>
}

//失效政策信息
export interface InvalidFace {
	updateMaster: boolean;
	modifyIndex: Array<string>
}

export interface GetUnitGiftFace {
	gifts: Array<any>;
	unitType: string;
	info: null | string;
}

export interface UnitResFace {
	[product_id: string]: {
		[unitType: string]: {
			unit_id: string;
			unit__s: string;
		}
	}
}
export interface LimitFace {
	id: string;  // 限量规则id
	limit_type: string;  // 限制类型  GIFT_QUANTITY:限赠品数量；GIFT_AMOUNT:限赠品金额；ORIGINAL_AMOUNT：限本品优惠金额；ORIGINAL_QUANTITY：限本品数量
	usable: number;    // 剩余量
	condition: {
		policy_id: string; //价格政策id
		range: string;    // 限量控制范围 POLICY:价格政策； RULE ： 价格规则
		dimension: string;   //限量维度  gift：赠品；self：促销本品
		policy_rule_id?: string;// 价格规则id
		product_id?: string | null; // 赠品id
		aggregateObjApiName?: string;  // 聚合订单对象  主从
		aggregateFieldApiName?: string;   // 聚合订单对象具体字段
		limitFieldValue?: string;   // 限制对象的具体值

	};
	limitData?: Array<any>;
	usedAmount?: number;
	isLimitMaster?: boolean;
}
export interface LimitResFace {
	limits: Array<LimitFace>;            //当前有的限额限量政策
	overLimitKey: Array<string>;   //超过限额限量政策的明细prod_pkg_key
	hasLimitKey: Array<string>;
}
export interface LimitRequestFace {
	actionCode: string;
	object_data: object; //主对象数据
	detailApiName:string;
	details: {
		[detailApiName: string]: Array<object>
	}
}


  
//}
