import PPM from "plugin_public_methods";
import PolicyUtil from "./policy_util";

import { reqCalFace, executePolicyFace, changeInfoFace, modifyInfoFace, GiftFace, GiftMapFace, GiftRuleFace, FieldMapFace, DecimalMapFace, GiftProductFace, DetailDataMapFace } from "./data_interface";

import {
	ExecuteFace
} from "./new_data_interface";

export default class PolicyAfterHandle {
	private processLog: Array<any> = [];
	constructor(
		public masterApiName: string,
		public detailApiName: string,
		public fromType: string,
		public recordType: string,
		public request: any,
		public triggerCal: any,
		public triggerUIEvent: any,
		public fieldMap: FieldMapFace,
		public decimalMap: DecimalMapFace,
		public fields: {
			[api: string]: object
		},
		public events: {
			[type: string]: any
		},
		public grayNewCalGiftFun: boolean
	) { }

	private refreshProcessLog(){
		this.processLog = [];
	}

	public async afterHandle(
		param: ExecuteFace, 
		uiInfo:  {
			"trigger":boolean;
			"dataKey"?:string;
			"allDetailsInfo"?:object;
		}) {
		if (!param) {
			return null;
		}
		this.refreshProcessLog();
		const data = await PPM.composeAsync(
			PPM.curry(2, this.calUiEvent.bind(this))(uiInfo),
			this.calBatch.bind(this),
			this.transferGifts.bind(this)
		)(param);
		param.promoteProcessLog?.details?.push(...this.processLog);
		return data;
	}

	//赠品转换为明细&删除失效赠品
	private transferGifts(param: executePolicyFace) {
		let initialVal: any = {},
			{ mdAdd = [], mdDel = [] } = param.changeInfo;

		param.detailDataMap = Object.keys(param.detailDataMap || {})
			.reduce((acc: any, key: string) => {
				if (!mdDel.includes(key)) {
					acc[key] = param.detailDataMap[key]
				}
				return acc;
			}, initialVal);
		param.changeInfo.mdAdd = mdAdd.filter((data: any) => !mdDel.includes(data.prod_pkg_key));
		param.changeInfo.mdAdd.forEach((data: any) => {
			param.detailDataMap[data.prod_pkg_key] = data;
		})
		return param;
	}
	//调用计算接口
	public async calBatch(param: executePolicyFace) {
		const needCal =  this.shouldTriggerCalculation(param);
		if(!needCal){
			return param;
		}
		const startTime = PolicyUtil.getTime();
		const dataFormPolicy = await PPM.composeAsync(
			PPM.curry(2, this.updateCalRes.bind(this))(param),
			this.triggerCal.bind(this),
			this.parseCalArgs.bind(this),
		)(param);
		this.processLog.push({
			action: 'calBatch',
			request: '/calculate/service/batchCalculate',
			responseStatus: '200 OK',
			durationMs: PolicyUtil.getTime() - startTime,
			extraInfo: {}
		});
		return dataFormPolicy;
	}

	private shouldTriggerCalculation(param: executePolicyFace) {
		const {changeInfo,modifyInfo}=param;
		const {mdAdd=[],mdDel=[]}=changeInfo||{};
		
		const modifyMaster=modifyInfo?.modifyFields?.[this.masterApiName]||[],
			modifyDetail=modifyInfo?.modifyFields?.[this.detailApiName]||[],
			checkTarget=[mdAdd,mdDel,modifyMaster,modifyDetail];

		return checkTarget.some(value => Array.isArray(value) && value.length > 0);
	}

	//调用UI事件
	public async calUiEvent(eventInfo: {
		"trigger":boolean;
		"dataKey"?:string;
		"allDetailsInfo"?:object;
	}, param: executePolicyFace) {
		const {price_policy_id}=this.fieldMap;
		/**
		 * 传了dataKey,需要查看指定数据当前是否有政策:
		 * 有-价格政策后UI事件
		 * 无-价格政策取消事件(可能是手动取消，也可能是未匹配上)
		 */
		let eventName="policy_event";
		if(eventInfo.dataKey){
			let data=eventInfo.dataKey=="master"?param.masterData:param.detailDataMap[eventInfo.dataKey];
			if(!data[price_policy_id]){
				eventName="policy_cancel_even";
			}
		}
		if(!eventInfo.trigger||!this.events[eventName]){
			this.processLog.push({
				action: 'calUiEvent',
				request: 'no event',
				responseStatus: eventInfo.trigger?'no event':'no trigger',
				durationMs: 0,
				extraInfo: {
					dataKey: eventInfo.dataKey,
					eventName,	
					events: Object.keys(this.events||{})
				}
			});
			return param;
		}
		const startTime = PolicyUtil.getTime();
		const dataFromPolicy = await PPM.composeAsync(
			PPM.curry(2, this.updateUiRes.bind(this))(param),
			this.triggerUIEvent.bind(this),
			PPM.curry(2, this.parseUiArgs.bind(this))({
				eventName,
				"allDetailsInfo":eventInfo.allDetailsInfo
			}),
		)(param);
		this.processLog.push({
			action: 'calUiEvent',
			request: '/action/TriggerEvent',
			responseStatus: '200 OK',
			durationMs: PolicyUtil.getTime() - startTime,
			extraInfo: {
				eventName,
				dataKey: eventInfo.dataKey
			}
		});
		return dataFromPolicy;
	}

	//格式化计算接口入参
	private parseCalArgs(param: executePolicyFace): reqCalFace {
		if(this.grayNewCalGiftFun){
			return this.parseCalArgsNew(param);
		}
		let initialVal:any = {};
		const {parent_rowId}=this.fieldMap;
		const dataMap = param.detailDataMap,
			detailFilterBom = (Object.keys(dataMap) || [])
				.filter((key) => !dataMap[key][parent_rowId])
				.reduce((acc, key) => {
					acc[key] = dataMap[key];
					return acc;
				}, initialVal);
		
		//收集主从修改字段相关的计算字段
		let args = PolicyUtil.getCalArgs(
			param,
			this.masterApiName,
			this.detailApiName,
			PPM.collectCalFields(this.fields, param.modifyInfo.modifyFields,[this.detailApiName]),
			"mdEdit");
		args.extraFields[this.detailApiName] = (args.extraFields[this.detailApiName] || []).filter((f: any) => !["product_price"].includes(f))
		return args;
	}

	//格式化计算接口入参
	private parseCalArgsNew(param: executePolicyFace): reqCalFace {
		let initialVal:any = {};
		const {parent_rowId}=this.fieldMap;
		const dataMap = param.detailDataMap,
			detailFilterBom = (Object.keys(dataMap) || [])
				.filter((key) => !dataMap[key][parent_rowId])
				.reduce((acc, key) => {
					acc[key] = dataMap[key];
					return acc;
				}, initialVal);
		const {calFieldsObj,calIndex,excludedInfo}=this.gatherCalInfo(param);	
		//收集主从修改字段相关的计算字段
		let args = PolicyUtil.getCalArgs(
			param,
			this.masterApiName,
			this.detailApiName,
			calFieldsObj,
			"mdEdit",
			calIndex
		);
		args.extraFields[this.detailApiName] = (args.extraFields[this.detailApiName] || []).filter((f: any) => !["product_price"].includes(f))
		
		return {
			...args,
			parseParam: (p: any) => {
				if(excludedInfo){
					p.excludedDetailCalculateFields = {};
					p.excludedDetailCalculateFields[this.detailApiName] = excludedInfo;
				}
				return p;
			}
		}
	}

	private gatherCalInfo(param: executePolicyFace){
		const {mdAdd=[]}=param.changeInfo||{};
		const addedKeys = mdAdd.map((item: any) => item.prod_pkg_key) || [];
		const { modifyIndex = [], modifyFields = {} } = param.modifyInfo || {};
		
		// 1. 收集新增赠品需要计算的字段
		const addCalInfo : any = this.gatherAddCalFields(addedKeys);

		// 2. 收集修改赠品需要计算的字段（包括属性字段）
		const modifyCalInfo : any= this.gatherModifyCalFields(modifyIndex, modifyFields);

		// 3. 收集需要排除的计算字段信息
		const excludedInfo = this.gatherExclusions(
			param,
			addedKeys,
			modifyIndex,
			addCalInfo[this.detailApiName]||[],
			modifyCalInfo[this.detailApiName]||[]
		);

		// 4. 合并计算字段
		const mergedCalc = PPM.mergeArrayObj(addCalInfo, modifyCalInfo);

		// 5. 返回综合信息
		return {
			calFieldsObj: mergedCalc,
			calIndex: [...addedKeys, ...modifyIndex],
			excludedInfo
		}
	}

	//收集新增数据计算字段
	private gatherAddCalFields(addedKeys: Array<string>){
		if (addedKeys.length === 0) return {};
  		return PPM.getCalFields(this.fields[this.detailApiName], true);
	}

	//收集修改数据计算字段
	private gatherModifyCalFields(modifyIndex: Array<string>, modifyFields: any){
		if (modifyIndex.length === 0) return {};
  		return PPM.collectCalFields(
    		this.fields,
    		modifyFields,
    		[this.detailApiName]
  		);
	}

	//收集排除信息
	private gatherExclusions(
		param: executePolicyFace, 
		addedKeys: string[],
		modifyIndex: string[],
		addCalFields: string[],
		modifyCalFields: string[],
	){
		const {
			prod_pkg_key,
			product_price,
			price_book_price,
			price_book_discount,
			policy_dynamic_amount,
			quantity,
			pricing_period = 'pricing_period',
			service_start_time = 'service_start_time',
			service_end_time = 'service_end_time'
		} = this.fieldMap;
		
		// 计算赠品不可计算字段
		const noCalcFields = [
			product_price,
			price_book_price,
			price_book_discount,
			policy_dynamic_amount,
			quantity,
			pricing_period,
			service_start_time,
			service_end_time
		];
		
		const { detailDataMap } = param;
		const excludeInfo: Record<string, { fieldName: string; order: number }[]> = {};
		
		// 计算差异字段（需要排除的）
		const addExcludeFields = modifyCalFields.filter((f:string) => !addCalFields.includes(f));
		const modifyExcludeFields = addCalFields.filter((f:string) => !modifyCalFields.includes(f));
		
		// 处理每个数据的排除字段
		[...addedKeys, ...modifyIndex].forEach((key: string) => {
			const data = detailDataMap[key];
			if (!data) return;
			
			const excludes = new Set<string>();
			
			// 赠品都不可计算字段排除
			if (data.parent_gift_key) {
				noCalcFields.forEach((f: string) => excludes.add(f));
			}
			
			// 新增&编辑数据，互相排除对方计算且自己不需要算的内容
			if (addedKeys.includes(key)) {
				// 新增赠品排除修改赠品特有的字段
				addExcludeFields.forEach((f: string) => excludes.add(f));
				
				// 新增赠品属性相关字段排除计算
				if (data.giftAttrFields) {
					data.giftAttrFields.forEach((f: string) => excludes.add(f));
				}
			} else {
				// 修改赠品排除新增赠品特有的字段
				modifyExcludeFields.forEach((f: string) => excludes.add(f));
			}
			
			// 构建排除字段信息
			if (excludes.size) {
				excludeInfo[data.rowId] = Array.from<string>(excludes).map((f: string) => ({
					fieldName: f, 
					order: 1
				}));
			}
		});
		
		return excludeInfo;
	}

	private async updateCalRes(param: executePolicyFace, result: any) {
		const data =  await PolicyUtil.updateCalResAsync(
			param,
			result,
			this.masterApiName,
			this.detailApiName
		);
		return data;
	}

	private parseUiArgs({ eventName, allDetailsInfo }: { eventName: string, allDetailsInfo?: object }, param: executePolicyFace) {

		const args = {
			noLoading: true,
			noRetry: true,
			noMerge: true,
			event: this.events[eventName],
			masterData: param.masterData,
			details: {
				...(allDetailsInfo||{}),
				[this.detailApiName]: Object.keys(param.detailDataMap).map((key: string) => param.detailDataMap[key])
			},
			objApiName: this.masterApiName
		}
		return args;
	}

	public updateUiRes(param: executePolicyFace, result: any) {
		const uiRes = result && result.Value && result.Value.data,
			oriUiValue=result&&result.Value&&Object.assign({},result.Value);
		if (!uiRes) {
			return Object.assign(param,{
				oriUiValue:oriUiValue
			});
		}

		param.changeInfo.mdAdd = param.changeInfo.mdAdd || [];
		param.changeInfo.mdDel = param.changeInfo.mdDel || [];

		let iKeyMap: any = {},
			iDetailMap: any = {};

		const {rowId,prod_pkg_key,data_index}=this.fieldMap;
		const resMaster = uiRes[this.masterApiName]  || {},
			resDetail = uiRes[this.detailApiName] || {},
			//原始明细，以rowId为索引
			oriDetailKeys = Object.keys(param.detailDataMap),
			//当前返回明细，以rowId/index为索引
			curResIds:Array<string> = Object.keys(resDetail.u || {});

		let idKeyMap:{[key:string]:string}={};
		oriDetailKeys.forEach((key:string,index:number)=>{
			const idx:string=index+"",
				data = param.detailDataMap[key],
				dataRowId:string=data[rowId];
			if(curResIds.includes(dataRowId)){
				idKeyMap[dataRowId]=key;
			}else if(curResIds.includes(idx)){
				idKeyMap[idx]=key;
			}else{
				param.changeInfo.mdDel.push(key)
			}
			
		});
		const resDetail_u = curResIds.reduce((accRes, id) => {
			const item=resDetail.u[id]||{};
			if(Object.keys(item).length){
				accRes[idKeyMap[id]] = resDetail.u[id]
			}
			return accRes;
		}, iDetailMap);

		const modifyIndex:Array<string>=[];
		//更新UI事件新增数据
		if (resDetail.a && resDetail.a.length >= 1) {
			resDetail.a.forEach((a: any) => {
				const uniqueCode = PPM.uniqueCode();
				a = Object.assign(a, {
					[prod_pkg_key]: uniqueCode,
					[data_index]: uniqueCode,
					[rowId]: uniqueCode
				});
				modifyIndex.push(uniqueCode);
				param.changeInfo.mdAdd.push(a);
			})
		}
		const modifyInfo=PPM.generateModifyInfo(resMaster, resDetail_u, this.masterApiName, this.detailApiName);

		return {
			masterData: Object.assign(param.masterData, resMaster),
			detailDataMap: PolicyUtil.updateDataMap(param.detailDataMap, resDetail_u),
			changeInfo: PPM.updateChangeInfo(param.changeInfo, resMaster, resDetail_u),
			modifyInfo: {
				"modifyFields":modifyInfo.modifyFields,
				"modifyIndex": [...modifyInfo.modifyIndex,...modifyIndex]
			},
			policyInfo: param.policyInfo,
			matchArgs: param.matchArgs,
			matchRes: param.matchRes,
			oriUiValue:oriUiValue
		};

	}
}
