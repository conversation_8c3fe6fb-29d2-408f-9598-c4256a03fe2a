/**
 * 政策相关逻辑处理
 * 获取政策配置、校验信息
 * 处理政策的匹配(detail,master)，match返回结果的更新;
 * 处理政策的取消,cancel返回结果的更新
 */
import PPM from 'plugin_public_methods';
import PolicyUtil from './policy_util';
import {
	DetailDataMapFace,
	configFace,
	changeInfoFace,
	modifyInfoFace,
	reqMatchFace,
	resMatchFace,
	policyInfoFace,
	reqCalFace,
	resCalFace,
	masterFieldMapFace,
	FieldMapFace,
	DecimalMapFace,
	InvalidFace,
	modifyPolicyFace
} from './data_interface';

import { ExecuteFace, ResMatchFace } from './new_data_interface';

export default class PolicyExecute {
	public policyConfig: configFace | null;
	constructor(
		public requestId: string,
		public masterApiName: string,
		public detailApiName: string,
		public fromType: string,
		public request: any,
		public triggerCal: any,
		public masterFieldMap: masterFieldMapFace,
		public fieldMap: FieldMapFace,
		public decimalMap: DecimalMapFace,
		public fields: {
			[api: string]: object;
		},
		public recordType: string,
		public notMatchGroupOtherRow: string,
		public manualMatchMode: boolean,
		public sendLog:any
	) {
		this.policyConfig = null;
	}
	/***********************************************************/
	/*********************匹配价格政策业务流程*********************/
	/***********************************************************/
	//获取客户政策配置状态
	public async getConfigStatus(account: string): Promise<configFace | null> {
		const accountInit = !this.policyConfig && account,
			accountChange =
				this.policyConfig && this.policyConfig.accountId !== account;
		if (accountInit || accountChange) {
			const config = await this.reqConfig(account);
			if (config) {
				this.policyConfig = Object.assign(
					{
						accountId: account
					},
					config
				);
			}
		}
		return this.policyConfig;
	}
	//接口请求客户政策配置状态
	private reqConfig(account: string) {
		const url =
				'FHH/EM1HNCRM/API/v1/object/price_policy/service/findPricePolicyConfig',
			reqConfigArgs = {
				masterObjectApiName: this.masterApiName,
				accountId: account,
				requestId: this.requestId
			};
		return PPM.ajax(this.request, url, reqConfigArgs);
	}

	//自动匹配价格政策
	public async executePolicy(param: ExecuteFace) {
		const isTrigger = this.triggerPolicyCheck(
				param.modifyInfo,
				param.detailDataMap
			),
			noAmortizeRules = PolicyUtil.getMasterRule(
				this.policyConfig,
				false
			);
		if (!isTrigger) {
			return null;
		}
		let dataFromPolicy = await this.matchDetail({}, param);
		if (noAmortizeRules) {
			dataFromPolicy = await PPM.composeAsync(
				PPM.curry(2, this.matchMaster.bind(this))({}),
				this.calBatch.bind(this)
			)(dataFromPolicy);
		}
		return dataFromPolicy;
	}

	//匹配整单指定政策
	public async executeMasterPolicy(param: ExecuteFace) {
		const { price_policy_id, price_policy_rule_ids } = this.masterFieldMap,
			masterRules = param.masterData[price_policy_rule_ids] || [],
			hasNoAmortizeRule = PolicyUtil.hasRuleByType(
				masterRules,
				this.policyConfig,
				false
			);

		let dataFromPolicy = null;

		if (hasNoAmortizeRule) {
			dataFromPolicy = await PPM.composeAsync(
				PPM.curry(
					2,
					this.matchMaster.bind(this)
				)({
					pricePolicyId: param.masterData[price_policy_id],
					changPriceMatch: '1'
				}),
				PPM.curry(2, this.parseMatchArgs_d.bind(this))({})
			)(param);
		}
		return dataFromPolicy;
	}

	//校验是否可触发价格政策
	public triggerPolicyCheck(mInfo: modifyInfoFace, detailsMap: any) {
		let { modifyFields, triggerPolicy, modifyIndex } = mInfo;
		if (!triggerPolicy) {
			const { price_book_product_id, product_id } = this.fieldMap,
				specialCondition = [price_book_product_id, product_id],
				conditionMap = this.policyConfig?.conditionFieldMap || {},
				fieldInCondition = (
					apiName: string,
					conditionConfig: any,
					modify: any
				): boolean => {
					const conditions: Array<string> = [
							price_book_product_id,
							product_id
						].concat(conditionConfig[apiName] || []),
						changeFields: Array<string> = modify[apiName] || [];
					return conditions.some((key) => changeFields.includes(key));
				};

			const masterTrigger = fieldInCondition(
					this.masterApiName,
					conditionMap,
					modifyFields
				),
				detailTrigger = fieldInCondition(
					this.detailApiName,
					conditionMap,
					modifyFields
				),
				hasProducts = modifyIndex.some(
					(key) =>
						!PolicyUtil.isPolicyGift(detailsMap[key], this.fieldMap)
				);

			triggerPolicy = masterTrigger || (detailTrigger && hasProducts);
		}
		return triggerPolicy;
	}

	//匹配分摊价格政策
	public async matchDetail(
		extendArgs: any = {},
		param: ExecuteFace
	): Promise<ExecuteFace> {
		const startTime = PolicyUtil.getTime();
		const dataFromPolicy = await PPM.composeAsync(
			PPM.curry(2, this.updateMatchRes.bind(this))('detail'),
			this.reqMatch.bind(this),
			PPM.curry(2, this.parseMatchArgs_d.bind(this))(extendArgs)
		)(param);

		PolicyUtil.createLogDetail(dataFromPolicy, startTime, {
			action: 'matchDetail',
			request: 'price_policy/service/match',
			responseStatus: '200 OK',
			durationMs: 0,
			extraInfo: {
				batchNo:param?.matchArgs?.batchNo
			}
		});
		return dataFromPolicy;
	}

	//匹配不分摊价格政策
	public async matchMaster(extendArgs: any = {}, param: ExecuteFace) {
		const startTime = PolicyUtil.getTime();
		const dataFromPolicy = await PPM.composeAsync(
			PPM.curry(2, this.updateMatchRes.bind(this))('master'),
			this.reqMatch.bind(this),
			PPM.curry(2, this.parseMatchArgs_m.bind(this))(extendArgs)
		)(param);
		PolicyUtil.createLogDetail(dataFromPolicy, startTime, {
			action: 'matchMaster',
			request: 'price_policy/service/match',
			responseStatus: '200 OK',
			durationMs: 0,
			extraInfo: {
				batchNo:param?.matchArgs?.batchNo
			}
		});
		return dataFromPolicy;
	}
	//调用计算接口
	public async calBatch(param: ExecuteFace) {
		const startTime = PolicyUtil.getTime();
		const dataFromPolicy = await PPM.composeAsync(
			PPM.curry(2, this.updateCalRes.bind(this))(param),
			this.triggerCal.bind(this),
			this.parseCalArgs.bind(this)
		)(param);
			PolicyUtil.createLogDetail(dataFromPolicy, startTime, {
				action: 'calBatch',
				request: '/calculate/service/batchCalculate',
				responseStatus: '200 OK',
				durationMs: 0,
				extraInfo: {}
			});
		return dataFromPolicy;
	}

	//清除数据上失效政策信息&计算

	public async handOffPolicy(param: ExecuteFace, invalidRes: any) {
		if (!invalidRes) {
			return param;
		}

		//更新重置的价格政策相关数据并重算
		const dataOffPolicy = await PPM.composeAsync(
			this.calBatch.bind(this),
			PPM.partial(this.updateCalRes.bind(this), param).bind(this)
		)(invalidRes);
		return dataOffPolicy;
	}

	//取消价格政策
	public async cancelPolicy(param: modifyPolicyFace) {
		//取消规则通用逻辑
		const cancelStartTime = PolicyUtil.getTime();

		let cancelParam = this.parseCancelArgs(param),
			dataFromPolicy = await PPM.composeAsync(
				this.updateCancelRes.bind(this),
				this.reqCancel.bind(this)
			)(cancelParam);

		PolicyUtil.createLogDetail(dataFromPolicy, cancelStartTime, {
			action: 'cancel',
			request: 'price_policy/service/cancel',
			responseStatus: '200 OK',
			durationMs: 0,
			extraInfo: {}
		});

		if (param.type == 'master') {
			return dataFromPolicy;
		}
		//取消明细价格政策||取消组合明细 的特殊逻辑
		const { price_policy_id, price_policy_rule_ids } = this.masterFieldMap,
			{ isGroupData, restGroupKeys } = cancelParam.cancelInfo,
			masterRules =
				dataFromPolicy.masterData[price_policy_rule_ids] || [],
			hasNoAmortizeRule = PolicyUtil.hasRuleByType(
				masterRules,
				this.policyConfig,
				false
			),
			hasAmortizeRule = PolicyUtil.hasRuleByType(
				masterRules,
				this.policyConfig,
				true
			),
			/**
			 * notMatchGroupOtherRow:"1" 不对组合中其他数据再匹配
			 * 不存在或不为1时，才对组合里其他数据再匹配
			 */
			hasOtherGroupData =
				isGroupData &&
				(restGroupKeys || []).length >= 1 &&
				this.notMatchGroupOtherRow !== '1';

		//预处理一次match detail类型接口参数
		dataFromPolicy = this.parseMatchArgs_d(
			{
				modifiedDataIndexList: hasOtherGroupData ? restGroupKeys : [],
				exactlyMatchModifiedData: true
			},
			dataFromPolicy
		);
		/**
		 * 如果是组合中的明细 或者 主对象有分摊的规则 match一次 : matchType=detail
		 * modifiedDataIndexList= (组合&&组合中还有数据)?组合其他数据:[]
		 * notMatchGroupOtherRow:"1" 不对组合中其他数据再匹配
		 */
		//不是通过matchDetail，要额外单独记日志
		
		if (hasOtherGroupData || hasAmortizeRule) {
			dataFromPolicy = await this.calBatch(dataFromPolicy);

			const cancelStartTime = PolicyUtil.getTime();

			dataFromPolicy = await PPM.composeAsync(
				PPM.curry(2, this.updateMatchRes.bind(this))('change'),
				this.reqMatch.bind(this)
			)(dataFromPolicy);

			PolicyUtil.createLogDetail(dataFromPolicy, cancelStartTime, {
				action: 'matchDetail',
				request: 'price_policy/service/match',
				responseStatus: '200 OK',
				durationMs: 0,
				extraInfo: {}
			});
		}

		// 如果主对象有不分摊的规则，match一次:matchType=master;pricePolicyId=主对象政策id
		if (hasNoAmortizeRule) {
			let extendArgs = {
				exactlyMatchModifiedData: false,
				pricePolicyId: dataFromPolicy.masterData[price_policy_id]
			};
			dataFromPolicy = await PPM.composeAsync(
				PPM.curry(2, this.matchMaster.bind(this))(extendArgs),
				this.calBatch.bind(this)
			)(dataFromPolicy);	
		}
		return dataFromPolicy;
	}

	//选择政策
	public async selectPolicy(
		param: ExecuteFace,
		type: string,
		targetPolicy: string,
		dataKey: string
	) {
		const { price_policy_id, price_policy_rule_ids } = this.masterFieldMap,
			noAmortizeRules = PolicyUtil.getMasterRule(
				this.policyConfig,
				false
			),
			amortizeRules = PolicyUtil.getMasterRule(this.policyConfig, true),
			masterRules = param.masterData[price_policy_rule_ids] || [],
			hasNoAmortizeRule = PolicyUtil.hasRuleByType(
				masterRules,
				this.policyConfig,
				false
			),
			masterPolicyId = param.masterData[price_policy_id];

		//预处理一次match detail类型接口参数
		let dataFromPolicy = this.parseMatchArgs_d({}, param);

		if (type == 'detail') {
			dataFromPolicy = await this.matchDetail(
				{
					pricePolicyId: targetPolicy,
					modifiedDataIndexList: [dataKey]
				},
				param
			);
			//如果当前数据有主对象不分摊政策，match master一次
			if (hasNoAmortizeRule) {
				dataFromPolicy = await PPM.composeAsync(
					PPM.curry(
						2,
						this.matchMaster.bind(this)
					)({
						pricePolicyId: masterPolicyId
					}),
					this.calBatch.bind(this)
				)(dataFromPolicy);
			}
		} else {
			if (amortizeRules) {
				dataFromPolicy = await this.matchDetail(
					{
						pricePolicyId: targetPolicy,
						modifiedDataIndexList: []
					},
					param
				);
			}
			if (noAmortizeRules) {
				if (amortizeRules) {
					dataFromPolicy = await this.calBatch(dataFromPolicy);
				}
				dataFromPolicy = await this.matchMaster(
					{
						pricePolicyId: targetPolicy
					},
					dataFromPolicy
				);
			}
		}
		return dataFromPolicy;
	}

	/***********************************************************/
	/***********************匹配政策处理方法***********************/
	/***********************************************************/
	//价格政策接口调用参数:match detail
	//过滤掉政策赠品&bom子键，修改的明细参数也要同步更改
	private parseMatchArgs_d(extendArgs: any, param: ExecuteFace): ExecuteFace {
		const { account_id } = this.masterFieldMap,
			{modifyIndex=[],modifyFields={}} = param.modifyInfo,
			detailDataMap = PolicyUtil.filterGifts(
				param.detailDataMap,
				this.fieldMap
			),
			detailDataKeys = Object.keys(detailDataMap);
			
		let modifiedDataIndexList = modifyIndex.filter((key) =>
			detailDataKeys.includes(key)
		);
		//如果是编辑客户，modifiedDataIndexList设置为空
		if(modifyFields[this.masterApiName]?.includes(account_id)){
			modifiedDataIndexList=[];
		}
		const actionCode = this.getActionCode(param);

		param.matchArgs = Object.assign(
			{
				batchNo: PPM.uniqueCode(),
				requestId: this.requestId,
				matchType: 'detail',
				masterObjectApiName: this.masterApiName,
				accountId: param.masterData[account_id],
				masterData: param.masterData,
				detailDataMap:detailDataMap,
				modifiedDataIndexList: modifiedDataIndexList,
				modifiedFieldApiNames: modifyFields,
				removeGroupKeySet: [],
				exactlyMatchModifiedData: false,
				actionCode: actionCode,
				requestSource: 'web',
				onceMatch: this.manualMatchMode,
			},
			extendArgs || {}
		);
		//政策匹配场景
		if(param.scene){
			param.matchArgs.scene = param.scene;
		}

		return param;
	}

	private getActionCode(param: ExecuteFace){
		const isDraftInit =  this.fromType=='draft' && param.matchFrom == "reviseUpdate",
			isEdit = this.fromType == 'edit';
		if(isEdit || isDraftInit){
			return "Edit";
		}
		return "Add";
	}
	//价格政策接口调用参数:match master
	private parseMatchArgs_m(extendArgs: any, param: ExecuteFace): ExecuteFace {
		param.matchArgs = Object.assign(
			param.matchArgs,
			{
				matchType: 'master',
				masterData: param.masterData,
				detailDataMap: PolicyUtil.filterGifts(
					param.detailDataMap,
					this.fieldMap
				),
				modifiedDataIndexList: [], //特殊逻辑，传空数组
				modifiedFieldApiNames: param.modifyInfo.modifyFields
			},
			extendArgs || {}
		);
		return param;
	}

	//价格政策接口调用
	private async reqMatch(param: ExecuteFace): Promise<ExecuteFace> {
		const url = 'FHH/EM1HNCRM/API/v1/object/price_policy/service/match';
		param.matchRes = await PPM.ajax(this.request, url, param.matchArgs);
		return param;
	}

	//价格政策返回结果更新
	private async updateMatchRes(type: string, param: ExecuteFace): Promise<ExecuteFace> {
		await PolicyUtil.checkRequestRes(param,'matchRes',this.sendLog);
		const policyRes = this.verifyPolicy(param, type);
		let filteredDetailDataMap: any = {};
		for (let key in policyRes.detailDataMap) {
			if (
				!(
					Object.keys(policyRes.detailDataMap[key]).length === 1 &&
					'gift_map' in policyRes.detailDataMap[key]
				)
			) {
				filteredDetailDataMap[key] = policyRes.detailDataMap[key];
			}
		}

		return {
			masterData: Object.assign(param.masterData, policyRes.masterData),
			detailDataMap: PolicyUtil.updateDataMap(
				param.detailDataMap,
				policyRes.detailDataMap
			),
			changeInfo: PPM.updateChangeInfo(
				param.changeInfo,
				policyRes.masterData,
				policyRes.detailDataMap
			),
			modifyInfo: PPM.generateModifyInfo(
				policyRes.masterData,
				filteredDetailDataMap,
				this.masterApiName,
				this.detailApiName
			),
			policyInfo: this.updatePolicyInfo(
				policyRes,
				param.policyInfo,
				param.matchArgs.exactlyMatchModifiedData ? 'change' : type
			),
			matchArgs: param.matchArgs,
			matchRes: policyRes,
			matchFrom: param.matchFrom,
			promoteProcessLog: param.promoteProcessLog
		};
	}


	private verifyPolicy(param: ExecuteFace, type: string) {
		const me = this,
			{
				quantity,
				prod_pkg_key,
				price_policy_id,
				rowId,
				product_id__r,
				record_type
			} = this.fieldMap,
			qDecimal = this.decimalMap[quantity];
		let policyRes = param.matchRes,
			resDetailKeys = Object.keys(policyRes.detailDataMap),
			initMapVal: DetailDataMapFace = {},
			detailChangeKeys = Object.keys(param.changeInfo.mdUpdate);

		policyRes.masterData = PPM.compose(PolicyUtil.verifyPolicyData)({
			type: type,
			matchFrom: param.matchFrom,
			nData: policyRes.masterData,
			oData: param.masterData,
			fieldMap: this.masterFieldMap,
			notInRes: false
		});


		//编辑初始化，需要根据组合内数据的变化来强制更新一些数据：
		let groupChangeData: Array<string> = [];
		if (param.matchFrom !== 'fullyUpdate') {
			const oriGroupMap = PolicyUtil.collectGroupMap(
				param.matchArgs.detailDataMap
			);
			groupChangeData = PolicyUtil.collectGroupChangeData(
				oriGroupMap,
				policyRes.groupMap ?? {}
			);
			//缓存一次初始主对象政策信息，防止被match detail覆盖，导致match master无法真实判断变更
			if(type !== "master"){
				param.masterData.cachePrePolicyId = param.masterData.price_policy_id;
				param.masterData.cachePrePolicyRules = param.masterData.price_policy_rule_ids;
			}
		}
		policyRes.detailDataMap = Object.keys(
			param.matchArgs.detailDataMap
		).reduce((acc: any, key: string) => {
			const notInRes = !resDetailKeys.includes(key),
				oriData = param.matchArgs.detailDataMap[key];
			const nData = PPM.compose(PolicyUtil.verifyPolicyData)({
				type: type,
				matchFrom: param.matchFrom,
				nData: policyRes.detailDataMap[key],
				oData: oriData,
				fieldMap: this.fieldMap,
				notInRes: notInRes,
				modifyIndex: param.matchArgs.modifiedDataIndexList,
				detailChangeKeys: detailChangeKeys,
				groupChangeData: groupChangeData
			});
			acc[key] = nData;
			return acc;
		}, initMapVal);
		return policyRes;
	}

	//更新价格政策
	public updatePolicyInfo(
		policyRes: ResMatchFace,
		policyInfo: policyInfoFace,
		type: string
	): policyInfoFace {
		switch (type) {
			case 'master':
				return PolicyUtil.incrementalUpdate(policyInfo, {
					pricePolicies: policyRes.pricePolicies || [],
					masterPricePolicy: policyRes.masterPricePolicy || {}
				});
			default:
				return PolicyUtil.incrementalUpdate(policyInfo, policyRes);
		}
	}

	//格式化计算接口入参
	private parseCalArgs(param: ExecuteFace): reqCalFace {
		const args = PolicyUtil.getCalArgs(
			param,
			this.masterApiName,
			this.detailApiName,
			PPM.collectCalFields(this.fields, param.modifyInfo.modifyFields, [
				this.detailApiName
			]),
			'mdEdit'
		);
		param.calArgs = args; //以入参的修改字段和行为准
		return args;
	}

	private async updateCalRes(param: ExecuteFace, result: any) {
		const {
			price_book_amount = 'price_book_amount',
			product_amount = 'product_amount'
		} = this.fieldMap;
		let data: any = {};
		data = await PolicyUtil.updateCalResAsync(
			param,
			result,
			this.masterApiName,
			this.detailApiName,
			[price_book_amount, product_amount]
		);
		return Object.assign(data, {
			matchFrom: param.matchFrom,
			promoteProcessLog: param.promoteProcessLog
		});
	}

	//价格政策接口调用
	private parseCancelArgs(param: modifyPolicyFace) {
		const { group_key, parent_gift_key } = this.fieldMap;

		let modifyKey = param.dataKey == 'master' ? '' : param.dataKey,
			args: any = {
				requestId: this.requestId,
				modifyType: param.type,
				modifiedDataIndex: modifyKey,
				masterData: param.masterData,
				detailDataMap: param.detailDataMap
			},
			isGroupData: boolean = false,
			restGroupKeys: Array<string> = [];
		if (param.type == 'detail') {
			const detailDataMap = param.detailDataMap,
				modifyGroupKey = detailDataMap[modifyKey][group_key];
			isGroupData = !!modifyGroupKey;

			if (isGroupData) {
				args.cancelGroupKey = modifyGroupKey;
				restGroupKeys = Object.keys(detailDataMap)
					.filter(
						(d) => detailDataMap[d][group_key] == modifyGroupKey
					)
					.filter((key) => key !== modifyKey)
					.filter((d) => !detailDataMap[d][parent_gift_key]);
			}
		}
		param.cancelArgs = args;
		param.cancelInfo = Object.assign(param.cancelInfo || {}, {
			isGroupData: isGroupData,
			restGroupKeys: restGroupKeys
		});
		return param;
	}
	private async reqCancel(param: modifyPolicyFace): Promise<any> {
		const url = 'FHH/EM1HNCRM/API/v1/object/price_policy/service/cancel';
		param.cancelRes = await PPM.ajax(this.request, url, param.cancelArgs);
		return param;
	}

	private async updateCancelRes(param: modifyPolicyFace) {
		await PolicyUtil.checkRequestRes(param,'cancelRes',this.sendLog);
		const { price_policy_id } = this.fieldMap,
			cancelRes = this.verifyCancelRes(param);

		if(cancelRes.abandonedGiftIndex?.length){
			let initialVal:{[key:string]:any}={};
			const filteredData = Object.keys(cancelRes.detailDataMap||{}).reduce((acc, key) => {
				if (!cancelRes.abandonedGiftIndex.includes(key)) {
				  acc[key] = cancelRes.detailDataMap[key];
				}
				return acc;
			  }, initialVal);
			cancelRes.detailDataMap = filteredData;
		}

		/*
		 * 取消价格政策，补全返回结果（gift_map=null;icon_fake_val更新）
		 * 根据要删除的赠品Index数组，从其所属明细的gift_map里删除
		 */
		let targetRes =
			param.type == 'master'
				? cancelRes.masterData
				: cancelRes.detailDataMap[param.cancelArgs.modifiedDataIndex];
		targetRes.gift_map = null;
		if (param.type !== 'master') {
			targetRes.icon_fake_val = targetRes[price_policy_id];
		}

		const changeInfo: changeInfoFace = {
				masterUpdate: {},
				mdUpdate: {},
				mdAdd: [],
				mdDel: cancelRes.abandonedGiftIndex || []
			},
			detailDataMap = PPM.compose(
				PPM.partial(PolicyUtil.filterDataMap, changeInfo.mdDel),
				PPM.partial(PolicyUtil.updateDataMap, param.detailDataMap)
			)(cancelRes.detailDataMap);

		if (
			param.policyInfo &&
			param.policyInfo.groupMap &&
			param.cancelArgs.cancelGroupKey
		) {
			delete param.policyInfo.groupMap[param.cancelArgs.cancelGroupKey];
		}

		return {
			masterData: Object.assign(param.masterData, cancelRes.masterData),
			detailDataMap: detailDataMap,
			changeInfo: PPM.updateChangeInfo(
				changeInfo,
				cancelRes.masterData,
				cancelRes.detailDataMap
			),
			modifyInfo: PPM.generateModifyInfo(
				cancelRes.masterData,
				cancelRes.detailDataMap,
				this.masterApiName,
				this.detailApiName
			),
			matchFrom: 'fullyUpdate',
			policyInfo: Object.assign(param.policyInfo || {}, {
				amortizeInfoMap: cancelRes.amortizeInfoMap
			}),
			promoteProcessLog: param.promoteProcessLog
		};
	}

	//取消后可能没有任何match，原其他明细数据中没有hold_choose会导致赠品丢失，本地补全一下
	private verifyCancelRes(param: modifyPolicyFace) {
		let { type, cancelRes, detailDataMap, cancelArgs } = param;

		let targetKey: Array<string> =
				type == 'master' ? [] : [cancelArgs.modifiedDataIndex],
			filterKeys: Array<string> = targetKey.concat(
				cancelRes.abandonedGiftIndex || []
			);
		Object.keys(detailDataMap).forEach((key: string) => {
			let data = detailDataMap[key];
			if (!filterKeys.includes(key) && !PPM.isEmpty(data['gift_map'])) {
				let initVal: any = {};
				const parsedGiftMap = Object.keys(data['gift_map']).reduce(
					(giftMap: any, rule: string) => {
						giftMap[rule] = Object.assign(
							{
								hold_chose: '1'
							},
							data['gift_map'][rule]
						);
						return giftMap;
					},
					initVal
				);
				cancelRes.detailDataMap[key] = Object.assign(
					cancelRes.detailDataMap[key] || {},
					{
						gift_map: parsedGiftMap
					}
				);
			}
		});
		return cancelRes;
	}
}
