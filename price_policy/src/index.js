import PPM from 'plugin_public_methods';
import Base from 'plugin_base';
import PricePolicyImp from './package/core';

export default class Price_Policy extends Base {
	constructor(pluginService, pluginParam) {
		super(...arguments);
		this.pricePolicy = null;
		this.policyConfig = null; //有无政策、分摊政策、不分摊政策等配置信息&全量当单可用政策数据
		this.policyInfo = null; //主从可用政策、组合政策信息缓存
		this.initSuccess = false;
		this.manualMatchMode = false; //是否手动匹配政策
		this.pluginUniqId = 'pricePolicy' + PPM.uniqueCode();

	}

	/*************************************************************************/
	/******************************* 接受命令函数 *******************************/
	/*************************************************************************/
	//从对象初始化之前，只初始部分参数，不做数据初始化
	async mdRenderBefore(param, configs, plugin) {
		this.initModule(param, configs, plugin);

		const commonProps = {
				decimalMap: this.decimalMap,
				productNameConfig: this.productNameConfig,
				fieldMap: this.fieldMap,
				mdDefRecordType: this.recordType
			};
		return commonProps;
		
	}
	//表单初始化完成，做数据初始化
	async formRenderAfter(param) {
		const result = await this.initMdByPolicy(param);

		return result;
	}

	//主对象数据变化:要从collectChange收集
	async masterDataChange(param) {
		if (!this.initSuccess) {
			return null;
		}
		let modifyIndex = [];
		if (param._hlyMdAdd && param._hlyMdAdd.length) {
			modifyIndex = param._hlyMdAdd.map((d) => d.rowId);
		}
		const changeData = param.collectChange(),
			dataFromPolicy = await this.autoMatchPolicy(param, {
				modifyFields: {
					[this.masterApi]: changeData.blurField
						? [changeData.blurField]
						: []
				},
				modifyIndex: Object.keys(changeData.mdUpdate || {})
			});
		return dataFromPolicy;
	}

	//从对象数据变化
	async mdDataChange(param) {
		const changeData = param.changeData || {},
			changeIds = Object.keys(changeData),
			changeFields = changeIds.reduce((fieldArr, key) => {
				Object.keys(changeData[key]).forEach((field) => {
					if (!fieldArr.includes(field)) {
						fieldArr.push(field);
					}
				});
				return fieldArr;
			}, []);
		const dataFromPolicy = await this.autoMatchPolicy(param, {
			modifyFields: {
				[this.detailApi]: changeFields
			},
			modifyIndex: param.dataIndex || changeIds
		});
		return dataFromPolicy;
	}

	//校验用户动作是否触发价格政策
	checkPromoCalcTriggered(param) {

		const manualMatchMode = true;

		// 检查是否手工改价
		if (this.manuallyChangePriceCheck(param)) {
			return {
				manualMatchMode,
				triggerPolicy: true
			};
		}

		// 如果不是手工改价，判断是否触发价格政策条件
		const { idKeyMap, detailDataMap } = this.getDetailsMapInfo(param),
			changeInfo = param.collectChange(),
			modifyInfo = {
				modifyFields: {}, // 如果这个始终为空，是否需要传递？
				modifyIndex: Object.keys(changeInfo.mdUpdate)
			},
			modifyData = this.parseChangeToModify(
				changeInfo,
				modifyInfo,
				idKeyMap,
				param.pluginApiName,
				param
			);

		// 根据修改数据触发价格政策逻辑
		const triggerPolicy = this.pricePolicy.userEditInManualMode(
			modifyData,
			detailDataMap
		);

		return { manualMatchMode, triggerPolicy };
	}

	//取消价格政策,需要根据当前的policyInfo做增量更新
	async cancelPolicy(param, type, dataKey) {
		const dataFromPolicy = await this.pricePolicy.cancelPolicy({
			masterData: param.dataGetter.getMasterData(),
			detailDataMap: this.getDetailMap(param),
			policyInfo: this.policyInfo,
			type,
			dataKey,
			promoteProcessLog: this.createLog('cancelPolicy'),
			...this.createExtraParam(param)
		}),
			result = await this.parseExecuteRes(param, dataFromPolicy);

		return result;
	}

	//在没有匹配政策时，手动选择价格政策
	async selectPolicy(param, type, dataKey, policyId) {
		const dataFromPolicy = await this.pricePolicy.selectPolicy(
			{
				masterData: param.dataGetter.getMasterData(),
				detailDataMap: this.getDetailMap(param),
				changeInfo: {
					masterUpdate: {},
					mdUpdate: {},
					mdAdd: [],
					mdDel: []
				},
				modifyInfo: {
					modifyFields: {},
					modifyIndex: [dataKey]
				},
				matchFrom: 'fullyUpdate',
				promoteProcessLog: this.createLog('selectPolicy'),
				...this.createExtraParam(param)
			},
			type,
			policyId,
			dataKey
		),
			result = await this.parseExecuteRes(param, dataFromPolicy);
		return result;
	}
	//在有政策的场景下，切换政策
	async changePolicy(param, type, dataKey, policyId) {
		const dataFromCancel = await this.pricePolicy.cancelPolicy(
			{
				masterData: param.dataGetter.getMasterData(),
				detailDataMap: this.getDetailMap(param),
				type,
				dataKey,
				promoteProcessLog: this.createLog('changePolicy'),
				...this.createExtraParam(param)
			},
			true
		),
			dataFromPolicy = await this.pricePolicy.selectPolicy(
				{
					...dataFromCancel,
					...this.createExtraParam(param)
				},
				type,
				policyId,
				dataKey
			),
			result = await this.parseExecuteRes(param, dataFromPolicy);
		return result;
	}
	//修改赠品数量/种类
	async editGifts(param, giftsArr, editDataKey) {
		const {
			group_key,
			prod_pkg_key,
			parent_gift_key,
			quantity,
			price_policy_rule_ids,
			price_policy_id
		} = this.fieldMap;
		const getGroupGiftParentKey = (dMap, dKey, groupKey) => {
			if (!groupKey) {
				return dKey;
			}
			const parentKey = Object.keys(dMap).find(
				(k) => dMap[k][group_key] == groupKey && dMap[k].gift_map
			);
			return parentKey;
		};

		const detailsMap = this.getDetailMap(param),
			groupKey =
				(editDataKey !== 'master' &&
					detailsMap[editDataKey][group_key]) ||
				null,
			dataKey = getGroupGiftParentKey(detailsMap, editDataKey, groupKey),
			giftKeys = giftsArr.map((g) => g[prod_pkg_key]);

		let productKeys = [];
		Object.keys(detailsMap).forEach((dk) => {
			const item = detailsMap[dk],
				itemParentKey = item[parent_gift_key];
			if (itemParentKey == editDataKey) {
				productKeys.push(dk);
			} else if (itemParentKey == dataKey) {
				let giftPolicyRuleId =
					item[price_policy_id] +
					'_' +
					item[price_policy_rule_ids][0];
				if (giftPolicyRuleId == groupKey) {
					productKeys.push(dk);
				}
			}
		});
		let mergeKeys = [].concat(productKeys);
		giftKeys.forEach((gKey) => {
			if (!mergeKeys.includes(gKey)) {
				mergeKeys.push(gKey);
			}
		});
		let mdAdd = [],
			mdDel = [],
			mdUpdate = {};
		mergeKeys.forEach((key) => {
			const gift = giftsArr.find((g) => g[prod_pkg_key] == key);
			if (productKeys.includes(key) && giftKeys.includes(key)) {
				const item = {
					[quantity]: gift[quantity]
				};
				detailsMap[key] = Object.assign(detailsMap[key], item);
				mdUpdate[key] = item;
			} else if (productKeys.includes(key) && !giftKeys.includes(key)) {
				mdDel.push(key);
			} else {
				const isGroupGift =
					`${gift[price_policy_id]}_${gift[price_policy_rule_ids][0]}` ==
					groupKey;

				mdAdd.push(
					Object.assign(gift, {
						icon_fake_val: true,
						[parent_gift_key]: isGroupGift ? dataKey : editDataKey
					})
				);
			}
		});

		const dataFromPolicy = await this.pricePolicy.editPolicyGift({
			masterData: param.dataGetter.getMasterData(),
			detailDataMap: detailsMap,
			changeInfo: {
				masterUpdate: {},
				mdUpdate: mdUpdate,
				mdAdd: mdAdd,
				mdDel: mdDel
			},
			modifyInfo: {
				modifyIndex: Object.keys(mdUpdate),
				modifyFields: {
					[this.detailApi]: [quantity]
				}
			},
			matchFrom: 'fullyUpdate',
			...this.createExtraParam(param)
		}),
			result = await this.parseExecuteRes(param, dataFromPolicy, true);
		return result;
	}

	//根据单位类型获取赠品
	getGiftByUnit(gifts, unitType) {
		return this.pricePolicy.getGiftByUnit(gifts, unitType);
	}

	//获取赠品价格
	getGiftPrice(masterData, gifts) {
		return this.pricePolicy.getGiftPrice(
			{
				masterData: masterData,
				detailDataMap: {},
				changeInfo: {
					masterUpdate: {},
					mdUpdate: {},
					mdAdd: [],
					mdDel: []
				},
				modifyInfo: {
					modifyFields: {},
					modifyIndex: []
				},
				matchFrom: 'fullyUpdate'
			},
			gifts
		);
	}
	//按钮ui事件结束，需要更新结果&触发计算
	async uiEventAfter(param) {
		const { uiEventResult } = param,
			detailDataMap = this.getDetailMap(param),
			policyParam = {
				masterData: param.dataGetter.getMasterData(),
				detailDataMap: detailDataMap,
				changeInfo: {
					masterUpdate: {},
					mdUpdate: {},
					mdAdd: [],
					mdDel: []
				},
				modifyInfo: {
					modifyIndex: [],
					modifyFields: {}
				},
				matchFrom: 'fullyUpdate'
			};
		let result = {
			Value: {
				data: uiEventResult
			}
		},
			dataAfterCal = await this.pricePolicy.updateUiResAndCal(
				policyParam,
				result
			);
		//处理一下dataIndex，给后续的价格政策使用，modifyIndex要转换为rowId
		param.dataIndex = (
			Array.isArray(dataAfterCal?.modifyInfo?.modifyIndex)
				? dataAfterCal.modifyInfo.modifyIndex
				: []
		).map((prodKey) => detailDataMap[prodKey]?.rowId);

		this.updateData(param, dataAfterCal.changeInfo);
	}

	/*************************************************************************/
	/******************************* 初始化相关 *******************************/
	/*************************************************************************/

	initModule(param, configs, plugin) {
		if (!this.initSuccess) {
			this.masterApi = param.masterObjApiName;
			this.detailApi = param.objApiName;
			this.detailDesc = param.dataGetter.getDescribe(this.detailApi);
			this.manualMatchMode = configs.manualMatchMode;
			this.allowDrag = configs.allowDrag;
			this.allowedRange = configs.allowedRange;
			this.grayNewCalGiftFun = configs.grayNewCalGiftFun;
			this.initFieldsInfo(param);
			this.supplementFieldsMap(plugin);
			this.initPricePolicyImp(param);
			this.initSuccess = true;
		}
	}

	initFieldsInfo(param) {

		//主对象映射字段
		this.masterFieldMap = this.getMasterFields();
		//从对象的映射字段
		this.fieldMap = this.getAllFields(this.detailApi);

		//__r和__v字段需要本地补全
		//处理主对象字段
		const me = this,
			{
				price_policy_id,
				product_id,
				unit,
				is_package,
				is_multiple_unit,
				price_book_id,
				price_book_product_id
			} = this.fieldMap;
		this.masterFieldMap[`${price_policy_id}__r`] = `${price_policy_id}__r`;
		//处理从对象字段
		[
			price_policy_id,
			product_id,
			unit,
			price_book_id,
			price_book_product_id
		].forEach((key) => {
			me.fieldMap[`${key}__r`] = `${key}__r`;
		});
		[unit, is_package, is_multiple_unit].forEach((key) => {
			me.fieldMap[`${key}__v`] = `${key}__v`;
		});
		// 临时补全
		this.fieldMap.stand_price = 'stand_price';

		const describeLayout = param.dataGetter.getDescribeLayout(),
			events = describeLayout.layout.events || [],
			detailObj = describeLayout.detailObjectList.find(
				(d) => d.objectApiName == this.detailApi
			),
			detailFields = detailObj.objectDescribe.fields,
			{
				quantity,
				gift_amortize_price,
				product_price,
				price_book_price,
				policy_dynamic_amount
			} = this.fieldMap,
			decimalFields = [
				quantity,
				gift_amortize_price,
				product_price,
				price_book_price,
				policy_dynamic_amount
			],
			productField = detailFields[product_id];

		this.productNameConfig = productField?.is_open_display_name;
		//UI事件相关
		this.events = events.reduce((eventMap, e) => {
			switch (e.type) {
				case 4: //页面加载事件
					eventMap.onload = e;
					break;
				case 2:
				case 5: //价格政策相关事件，6-价格政策后；7-价格政策取消
					const eventId = e.triggers[0];
					if (eventId == 6 || eventId == 7) {
						const eName =
							eventId == 6
								? 'policy_event'
								: 'policy_cancel_even';
						eventMap[eName] = e;
					}
					break;
			}
			return eventMap;
		}, {});
		//第一个表格的业务类型
		const firstAvailableTable = detailObj.layoutList.find(
			(l) => !l.not_match
		);
		this.recordType =
			(firstAvailableTable && firstAvailableTable.record_type) ||
			'default__c';

		//小数位数
		this.decimalMap = decimalFields.reduce((accu, fName) => {
			if (fName) {
				let fDecimal =
					detailFields[fName] && detailFields[fName].decimal_places;
				accu[fName] = typeof fDecimal == 'number' ? fDecimal : 2;
			}
			return accu;
		}, {});
		this.fieldsInfo = {
			[this.masterApi]: describeLayout?.objectDescribe?.fields,
			[this.detailApi]: detailFields
		};
		//this.greyNewGiftAllocate = this.getConfig("price_policy_support_percentile_gift") == '1';
		//是否重新取价开关配置
		const reGetPriceKeyMap = {
			"SalesOrderObj": "get_price_when_copy",
			"QuoteObj": "get_price_when_copy_quote"
		};

		this.reGetPrice = this.getConfig(reGetPriceKeyMap[this.masterApi] || "get_price_when_copy") == '1';
	}

	// 从其他插件补全映射字段
	supplementFieldsMap(plugin) {
		const plugins = plugin?.api?.getPlugins() || [];
		const periodPlugin = plugins.find(p => p.pluginApiName === "period_product");

		if (!periodPlugin) return;

		const targetDetail = periodPlugin.params?.details?.find(d => d.objectApiName == this.detailApi);

		this.fieldMap = {
			...(targetDetail?.fieldMapping || {}),
			...this.fieldMap,
		};
	}

	initPricePolicyImp(param) {
		const requestId = param.dataGetter.getRequestId();
		this.requestId = requestId;
		const adjustedPriceMode =
			this.getConfig('change_price_type') == 'direct';
		param.dataUpdater.updateMaster({
			requestId: requestId
		});

		const initArgs = [
			requestId,
			this.masterApi,
			this.detailApi,
			param.formType,
			this.fieldsInfo,
			this.recordType,
			this.request,
			param.getRowBasicData,
			param.triggerCal,
			param.triggerUIEvent,
			this.getConfig('giftAmortizeBasis'),
			this.getConfig('not_match_group_other_row'),
			this.masterFieldMap,
			this.fieldMap,
			this.decimalMap,
			this.events,
			this.i18n,
			this.detailDesc,
			adjustedPriceMode,
			this.manualMatchMode,
			this.sendLog.bind(this),
			{
				reGetPrice: this.reGetPrice,
				allowedRange: this.allowedRange,
				allowMultipleObj: this.getConfig("multiple_object_price_policy") == "1",
				grayNewCalGiftFun: this.grayNewCalGiftFun
			}
		];

		this.pricePolicy = new PricePolicyImp(...initArgs);
	}

	//初始化md数据,只有获取配置失败时return，否则即使客户当前无政策，也要初始化(清空数据已有的政策信息)
	async initMdByPolicy(param) {
		await this.prePolicyCheck(param);
		if (!this.policyConfig) {
			return;
		}

		const masterData = param.dataGetter.getMasterData(),
			mustResetKet = ['draft', 'mapping', 'clone'].includes(
				param.formType
			),
			mustRestRowId = param.formType == "draft" && !this.reGetPrice,
			detailsMap = this.getDetailMap(param, mustResetKet, mustRestRowId),
			promoteProcessLog = this.createLog('initByPolicy'),
			dataFromPolicy = await this.pricePolicy.initMdByPolicy(
				masterData,
				detailsMap,
				this.policyConfig.hasPricePolicy,
				promoteProcessLog,
				this.createExtraParam(param)
			),
			result = await this.parseExecuteRes(param, dataFromPolicy, false, "init");
		return result;
	}

	//主从数据修改自动执行价格政策
	async autoMatchPolicy(param, modifyInfo) {
		const masterData = param.dataGetter.getMasterData(),
			checkRes = await this.prePolicyCheck(param, masterData);

		const changeInfo = param.collectChange();
		const { idKeyMap, detailDataMap } = this.getDetailsMapInfo(param);

		// 是否客户字段变更且存在政策
		const accountHasPolicy = this.hasAccountChangeWithPolicy(param, modifyInfo, masterData, detailDataMap);

		// 条件：客户无政策 && 非已匹配政策的数据修改客户
		const isSkipPolicyMatch = !checkRes.value && !accountHasPolicy;

		// 客户无政策 && 已匹配政策的数据修改客户
		if (isSkipPolicyMatch) {
			return {
				value: null,
				policyInfo: checkRes.policyInfo
			};
		}

		let dataFromPolicy = null;

		const isPriceChangedManually = this.manuallyChangePriceCheck(param);
		const matchParam = {
			masterData: masterData,
			detailDataMap: detailDataMap,
			changeInfo: {
				//清空
				masterUpdate: {},
				mdUpdate: {},
				mdAdd: [],
				mdDel: []
			},
			modifyInfo: this.parseChangeToModify(
				changeInfo,
				modifyInfo,
				idKeyMap,
				param.pluginApiName,
				param
			),
			matchFrom: 'fullyUpdate',
			promoteProcessLog: this.createLog('autoMatch'),
			...this.createExtraParam(param)
		};

		if (isPriceChangedManually) {
			//来自手工改价，只执行match master计算折上折政策
			dataFromPolicy = await this.pricePolicy.executeMasterPolicy(
				matchParam
			);
		} else {
			dataFromPolicy = await this.pricePolicy.executePolicy(matchParam);
		}
		const result = await this.parseExecuteRes(param, dataFromPolicy);
		return result;
	}

	//主从数据修改手动执行价格政策
	async manualMatchPolicy(param) {
		const masterData = param.dataGetter.getMasterData(),
			{ detailDataMap } = this.getDetailsMapInfo(param),
			checkRes = await this.prePolicyCheck(param, masterData),
			hasCurrentPolicy = this.currentUsingPolicy(masterData, detailDataMap),
			shouldClearPolicy = !checkRes.value && hasCurrentPolicy; // 客户无政策且当前有使用政策，需要清空
		
		// 客户无政策且当前未使用政策，直接返回
		if (!checkRes.value && !hasCurrentPolicy) {
			return {
				value: null,
				msg: checkRes.msg,
				policyInfo: checkRes.policyInfo,
				noUpdatePolicy: true
			};
		}

		const matchParam = {
			masterData: masterData,
			detailDataMap: detailDataMap,
			changeInfo: {
				masterUpdate: {},
				mdUpdate: {},
				mdAdd: [],
				mdDel: []
			},
			modifyInfo: {
				modifyIndex: [],
				modifyFields: {},
				triggerPolicy: true
			},
			matchFrom: 'fullyUpdate',
			promoteProcessLog: this.createLog('manualMatch'),
			...this.createExtraParam(param)
		};

		const dataFromPolicy = await this.pricePolicy.executePolicy(matchParam);
		const result = await this.parseExecuteRes(param, dataFromPolicy);
		return result;
	}

	getDetailsMapInfo(param) {
		const api = this.detailApi,
			{ data_index, prod_pkg_key } = this.fieldMap,
			detailData = param.dataGetter.getDetail(this.detailApi) || [],
			idKeyMap = {},
			//复制转换草稿箱新建必须更新prod_pkg_key;其他场景无prod_pkg_key时补全一下，主要兼容历史数据
			detailDataMap = detailData.reduce((dataMap, d) => {
				if (!d[prod_pkg_key]) {
					const item = {
						[prod_pkg_key]: d.rowId, //复制转换需要更新一下
						[data_index]: d.rowId
					};
					d = Object.assign(d, item);
					param.dataUpdater.updateDetail(api, d.rowId, item);
				}
				if (!d[data_index]) {
					const item = {
						[data_index]: d[prod_pkg_key]
					};
					d = Object.assign(d, item);
					param.dataUpdater.updateDetail(api, d.rowId, item);
				}
				idKeyMap[d.rowId] = d[prod_pkg_key];
				dataMap[d[prod_pkg_key]] = d;
				return dataMap;
			}, {});
		return { idKeyMap, detailDataMap };
	}

	//价格政策前置校验
	async prePolicyCheck(param, masterData) {
		const { account_id } = this.getMasterFields();
		masterData = masterData || param.dataGetter.getMasterData();

		this.policyModuleCheck(param);

		//价格政策允许使用的其他业务逻辑校验
		const { isAllow, msg } = this.isPricePolicyAllow(masterData);
		if (!isAllow) {
			return {
				msg,
				value: false,
				policyInfo: this.policyConfig || {}
			};
		}

		this.policyConfig = await this.pricePolicy.getConfigStatus(
			masterData[account_id] || masterData.account_id
		);

		if (!this.policyConfig || !this.policyConfig.hasPricePolicy) {
			let msg = !this.policyConfig
				? '获取价格政策配置失败，需要刷新重试'
				: '当前客户无政策';
			masterData[account_id] &&
				console.log(`客户id${masterData[account_id]}: ${msg}`);
			return {
				value: false,
				policyInfo: this.policyConfig || {},
			};
		}
		return {
			value: true,
			policyInfo: this.policyConfig
		};
	}

	hasAccountChangeWithPolicy(param, modifyInfo, masterData = {}, detailDataMap = {}) {
		const { account_id, price_policy_id } = this.getMasterFields();
		const { masterUpdate = {} } = param.collectChange() || {};
		const masterUpdateKeys = Object.keys(masterUpdate);

		// 判断客户字段是否修改
		const isAccountChanged =
			masterUpdateKeys.includes(account_id) ||
			modifyInfo?.[this.masterApi]?.includes(account_id);

		// 判断是否存在政策信息
		const hasCurrentPolicy = this.currentUsingPolicy(masterData,detailDataMap);
		
		return isAccountChanged && hasCurrentPolicy;
	}

	//是否使用价格政策
	currentUsingPolicy(masterData = {}, detailDataMap = {}){
		const { price_policy_id } = this.fieldMap;
		// 判断是否存在政策信息
		const hasPolicyInMaster = masterData?.[price_policy_id];
		const hasPolicyInDetails = Object.values(detailDataMap).some(
			(data) => data?.[price_policy_id]
		);
		return hasPolicyInMaster || hasPolicyInDetails;
	}

	//业务逻辑，是否允许使用价格政策
	isPricePolicyAllow(masterData) {
		const useRepelCoupon = this.getUsedRepelCoupon(masterData);
		if (useRepelCoupon) {
			return {
				isAllow: false,
				msg: $t("您已选一张与价格政策互斥的优惠券")
			}
		}
		return {
			isAllow: true
		};
	}

	//禁用价格政策业务逻辑1:使用了与价格政策互斥的优惠券
	getUsedRepelCoupon(masterData) {
		const { coupon = [] } = masterData?.misc_content || {},
			repelCoupon = coupon.find((c) => c.repel_price_policy);
		return repelCoupon;
	}

	//是否是手动改价
	manuallyChangePriceCheck(param) {
		let editedField = null;
		if (param.operateType == 'masterEdit') {
			const changeInfo = param.collectChange();
			editedField = changeInfo?.blurField;
		} else {
			editedField = param.fieldName;
		}
		const { order_amount, sales_price, discount, subtotal } = this.fieldMap,
			watchFields = [order_amount, sales_price, discount, subtotal];

		return watchFields.includes(editedField);
	}

	//价格政策类初始化校验
	policyModuleCheck(param, masterData) {
		!this.pricePolicy && this.initPricePolicyImp(param);
	}

	parseChangeToModify(cInfo, mInfo, idKeyMap, pluginApiName, param) {
		const { mdAdd, mdDel, mdInsert, mdUpdate, masterUpdate } = cInfo,
			mdUpdateFieldObj = Object.keys(mdUpdate).reduce((accObj, key) => {
				accObj = Object.assign(accObj, mdUpdate[key]);
				return accObj;
			}, {}),
			modifyKeys = mInfo.modifyIndex.map((id) => idKeyMap[id]),
			isAddProduct = mdAdd.length >= 1 && pluginApiName !== 'manual_gift',
			//hly临时逻辑
			mustTrigger = param._hlyMdAdd && param._hlyMdAdd.length >= 1;
		return {
			modifyIndex: PPM.mergeArr(
				mdAdd.map((d) => idKeyMap[d.rowId]),
				modifyKeys
			),
			modifyFields: {
				[this.masterApi]: PPM.mergeArr(
					mInfo.modifyFields[this.masterApi],
					Object.keys(masterUpdate || {})
				),
				[this.detailApi]: PPM.mergeArr(
					mInfo.modifyFields[this.detailApi],
					Object.keys(mdUpdateFieldObj)
				)
			},
			triggerPolicy:
				isAddProduct ||
				mdDel.length >= 1 ||
				(mdInsert && mdInsert.length >= 1) ||
				mustTrigger
		};
	}

	getDetailMap(param, mustResetKey, mustRestRowId) {
		const api = this.detailApi,
			{ prod_pkg_key, data_index, price_policy_id, parent_gift_key = "parent_gift_key" } = this.fieldMap,
			detailData = param.dataGetter.getDetail(this.detailApi) || [];

		// 使用 Map 存储所有产品及其 rowId，以便在处理赠品时进行匹配
		const updatedProdPkgMap = new Map();
		/**
		 * 第一次遍历：更新所有产品的 prod_pkg_key
		 * 草稿箱新建&保留政策场景，重置rowId。避免rowId和prodPkg不一致场景，赠品找不到所属本品
		 * 复制转换草稿箱新建必须更新prod_pkg_key;其他场景无prod_pkg_key时补全一下，主要兼容历史数据
		 */
		detailData.forEach(d => {
			if (mustRestRowId) {
				if (d[prod_pkg_key]) {
					param.dataUpdater.updateRowId(d.rowId, d[prod_pkg_key]);
				}
			} else if (mustResetKey || !d[prod_pkg_key]) {
				const oldProdPkgKey = d[prod_pkg_key];
				const item = {
					[prod_pkg_key]: d.rowId, //复制转换需要更新一下
					[data_index]: d.rowId,
					icon_fake_val: d[price_policy_id]
				};
				d = Object.assign(d, item);
				param.dataUpdater.updateDetail(api, d.rowId, item);

				// 记录 prod_pkg_key 发生变化的情况
				if (oldProdPkgKey && oldProdPkgKey !== d.rowId) {
					updatedProdPkgMap.set(oldProdPkgKey, d.rowId);
				}
			}
			if (!d.data_index) {
				const item = {
					[data_index]: d[prod_pkg_key]
				};
				d = Object.assign(d, item);
				param.dataUpdater.updateDetail(api, d.rowId, item);
			}
		});

		// 第二次遍历：更新赠品的 parent_gift_key
		detailData.forEach(d => {
			if (d[parent_gift_key] && updatedProdPkgMap.has(d[parent_gift_key])) {
				// 根据 updatedProdPkgKeyMap 更新 parent_gift_key
				const newParentPkgKey = updatedProdPkgMap.get(d[parent_gift_key]);
				const updateItem = { [parent_gift_key]: newParentPkgKey };
				d = Object.assign(d, updateItem);
				param.dataUpdater.updateDetail(api, d.rowId, updateItem);
			}
		});

		// 返回最终数据结构
		return detailData.reduce((dataMap, d) => {
			dataMap[d[prod_pkg_key]] = d;
			return dataMap;
		}, {});
	}

	mergePolicies(oData, newData) {
		let data = newData || [];
		(oData || []).forEach((item) => {
			if (!data.find((d) => d.id == item.id)) {
				data.push(item);
			}
		});
		return data;
	}
	//noUpdatePolicy:不更新当前的政策信息，默认false
	async parseExecuteRes(param, dataFromPolicy, noUpdatePolicy = false, actionFrom) {
		//有政策更新
		if (dataFromPolicy && this.policyConfig) {
			const updatePolicies =
				(dataFromPolicy.policyInfo &&
					dataFromPolicy.policyInfo.pricePolicies) ||
				[];
			this.policyConfig.pricePolicies = this.mergePolicies(
				this.policyConfig && this.policyConfig.pricePolicies,
				updatePolicies
			);
			this.policyInfo = noUpdatePolicy
				? this.policyInfo
				: (({ initChangeInfo, ...rest }) => rest)(dataFromPolicy.policyInfo);
			//限额限量需要实时更新
			this.policyInfo.limit = dataFromPolicy.policyInfo.limit;
		}

		const policyInfo = {
			...(dataFromPolicy?.policyInfo ?? {}),
			pricePolicies: this.policyConfig.pricePolicies,
			policyCondition: this.policyConfig.conditionFieldMap
		};

		//补全数据的全部政策信息：
		this.completePolicyInfo(policyInfo, dataFromPolicy);

		const result = {
			value: dataFromPolicy ? dataFromPolicy.changeInfo : null,
			policyInfo: policyInfo,
			promoteProcessLog: dataFromPolicy?.promoteProcessLog
		},
			oriUiValue = dataFromPolicy ? dataFromPolicy.oriUiValue : null,
			mdDetailKeys =
				(result.value &&
					this.updateData(param, result.value, oriUiValue, actionFrom)) ||
				[];

		//编辑初始化，匹配政策更新给出提示
		const initChangeInfo = dataFromPolicy?.policyInfo?.initChangeInfo || {},
			changeKeys = Object.keys(initChangeInfo);
		const rangeDelInfo = dataFromPolicy?.policyInfo?.rangeDelInfo || [];

		if (changeKeys.length || rangeDelInfo.length) {
			let str = "";
			if (changeKeys.length) {
				const masterChange = changeKeys.includes('master'),
					detailChange =
						changeKeys.findIndex((key) => key !== 'master') >= 0;

				str += $t(
					'部分匹配的政策有变化，将重新匹配（优先匹配原政策），包括'
				);
				if (masterChange) {
					str += $t('整单政策');
				}
				if (masterChange && detailChange) {
					str += $t('和');
				}
				if (detailChange) {
					str += $t('以下产品政策：');
					str = changeKeys.reduce((accStr, key) => {
						if (key !== 'master') {
							accStr += initChangeInfo[key].productName + ';';
						}
						return accStr;
					}, str);
				}
			}
			if (rangeDelInfo.length) {
				const message = $t("可售范围调整，以下赠品因不在可售范围内而被删除：");
				str += "\n" + message;

				const lastIdx = rangeDelInfo.length - 1;
				rangeDelInfo.forEach((r, idx) => {
					const separator = idx === lastIdx ? "。" : "，";
					str += `${r.product_id__r}${separator}`;
				})
			}
			if (str && str != "") {
				this.alert(str);
			}
		}

		//价格政策结束插件
		await this.runPlugin('pricePolicy.executeAndUpdate.end', {
			modifyIndex: mdDetailKeys, //价格政策修改行索引
			param
		});
		return result;
	}

	// 补全数据的policyDetails信息
	completePolicyInfo(policyInfo, dataFromPolicy = {}) {
		if (!dataFromPolicy) {
			return;
		}

		dataFromPolicy.changeInfo = dataFromPolicy.changeInfo || {};
		dataFromPolicy.changeInfo.mdUpdate = dataFromPolicy.changeInfo.mdUpdate || {};
		dataFromPolicy.changeInfo.masterUpdate = dataFromPolicy.changeInfo.masterUpdate || {};

		const {
			changeInfo: { masterUpdate = {}, mdUpdate = {}, mdAdd = [], },
			masterData = {},
			detailDataMap = {},
			matchFrom,
		} = dataFromPolicy;

		const {
			pricePolicies = [],
			masterPricePolicy = {},
			detailPricePolicyMap = {},
		} = policyInfo;

		const updateKeys = new Set([
			...Object.keys(mdUpdate).filter(key => "price_policy_id" in mdUpdate[key]),
			...mdAdd.map(item => item.prod_pkg_key),
			...(matchFrom !== 'fullyUpdate' ? Object.keys(detailDataMap) : [])
		]);

		const populatePolicyDetails = (curPolicies, data, updateTarget) => {
			const policyDetails = [];
			if (curPolicies) {
				Object.entries(curPolicies).forEach(([policyId, ruleIds]) => {
					const policy = pricePolicies.find(p => p.id === policyId);
					if (policy) {
						policyDetails.push({
							id: policyId,
							name: policy.name,
							rules: policy.rules
								.filter(rule => ruleIds.includes(rule.id))
								.map(rule => (
										{
											id: rule.id,
											name: rule.name ,
											isActive:(data.price_policy_rule_ids||[]).includes(rule.id)
									})),
							isActive: policyId === data.price_policy_id
						});
					}
				});
			}
			return {
				...(updateTarget || {}),
				policyDetails
			};
		};

		//detail
		updateKeys.forEach(key => {
			const curPolicies = detailPricePolicyMap[key];
			const data = detailDataMap[key];
			const updateDRes = populatePolicyDetails(curPolicies, data, dataFromPolicy.changeInfo.mdUpdate[key]);
			dataFromPolicy.changeInfo.mdUpdate[key] = updateDRes;
		})

		//master
		if (("price_policy_id" in masterUpdate) || matchFrom !== 'fullyUpdate') {
			const updateMRes = populatePolicyDetails(masterPricePolicy, masterData, dataFromPolicy.changeInfo.masterUpdate);
			dataFromPolicy.changeInfo.masterUpdate = updateMRes;
		}
	}

	//
	updateData(param, result, oriUiValue, actionFrom) {
		const me = this,
			api = this.detailApi,
			{
				rowId,
				prod_pkg_key,
				parent_gift_key,
				group_key,
				is_package__v,
				price_policy_id,
				price_policy_rule_ids
			} = this.fieldMap,
			{ mdAdd, mdDel, mdUpdate, masterUpdate } = result,
			{ groupMap = {} } = this.policyInfo,
			mdDetailKeys = Object.keys(mdUpdate).filter(
				(key) => !mdDel.includes(key)
			),
			detailsArr = param.dataGetter.getDetail(this.detailApi);

		const giftIdMap = mdAdd.length
			? this.collectGiftIds(detailsArr, mdDel)
			: null;
		//更新主对象数据
		param.dataUpdater.updateMaster(masterUpdate);

		//更新的明细数据
		const mdUpdateFilterGift = [],
			recordType = new Set();
		detailsArr.forEach((d) => {
			if (mdDel.includes(d[prod_pkg_key])) {
				param.dataUpdater.del(api, d.rowId);
			} else {
				recordType.add(d.record_type);
				let uItem = {
					icon_fake_val: PPM.uniqueCode()
				};

				if (mdDetailKeys.includes(d[prod_pkg_key])) {
					const updateItem = mdUpdate[d[prod_pkg_key]]||{};
					const isObjValid = Object.keys(updateItem).some(key => key!="policyDetails");
					uItem = {
						...uItem,
						...updateItem
					};
					if (!d[parent_gift_key] && isObjValid) {
						mdUpdateFilterGift.push(d.rowId);
					}
				}
				param.dataUpdater.updateDetail(api, d.rowId, uItem);
			}
		});


		//更新新增数据
		mdAdd.forEach((d, index) => {
			recordType.add(d.record_type);
			const prodKey = d[prod_pkg_key];
			if (mdUpdate[prodKey]) {
				Object.assign(d, mdUpdate[d[prod_pkg_key]]);
			}
			if (d[parent_gift_key] && d.is_giveaway == '1') {
				const key = this.createGiftIdentifier(d);
				if (giftIdMap?.has(key)) {
					const idSet = giftIdMap.get(key);
					const firstId = idSet.values().next().value;
					d._id = firstId;
					idSet.delete(firstId);
					if (idSet.size === 0) {
						giftIdMap.delete(key);
					}
				}
			}

		});

		if (actionFrom === "init") {
			//插入再排序 ： 编辑时页面会有抖动
			param.dataUpdater.add(mdAdd);
			this.sortDetails(param, recordType);
			//新方法，920启用
			//this.sortAndMoveProducts(param, mdAdd)
		} else {
			if (this.allowDrag) {
				//拖拽模式：用户手动排序
				param.dataUpdater.add(mdAdd);
			} else {
				//价格政策排序模式：新增数据插入&排序
				this.addAndSortData(param, mdAdd);
				//新方法，920启用
				//this.sortAndMoveProducts(param, mdAdd)
			}
		}

		//设置不可编辑字段
		this.setReadOnlyFields(param, oriUiValue);

		return mdUpdateFilterGift;
	}

	//插入新增数据&数据排序
	addAndSortData(param, mdAdd) {
		const api = this.detailApi,
			details = param.dataGetter.getDetail(this.detailApi),
			{ dataUpdater, dataGetter, operateType, newDataIndexs } = param,
			newKeySet = new Set([...mdAdd.map((d) => d.rowId)]),
			totalDetails = [...details, ...mdAdd];

		//排序后的数组
		const sortedDetails = PPM.sortPricePolicyDetails(totalDetails);

		//实时更新的数组
		const updateOrder = totalDetails.map((d) => d.rowId);

		//缓存操作过移动过的数据rowId
		const positionChangedRowId = new Set();

		// 遍历排序后的对象数组，逐个移动数据
		for (let i = 0; i < sortedDetails.length; i++) {
			const item = sortedDetails[i],
				rowId = item.rowId,
				updateItem = updateOrder[i];

			let insertRowId = null;

			if (updateItem !== rowId) {
				const updateIdx = updateOrder.findIndex((u) => u == rowId);
				if (i == 0) {
					if (details.length) {
						//插入到当前第一个元素前面
						const firstItem = details.find(d => d.record_type == item.record_type);
						if (firstItem) {
							dataUpdater.insert(api, firstItem.rowId, [item], true);
						} else {
							dataUpdater.add([item]);
						}
					} else {
						dataUpdater.add([item]);
					}
				} else {
					let prevItem = sortedDetails[i - 1];
					insertRowId = prevItem.rowId;

					const isNew = dataGetter.getDataIsNew(rowId),
						isCopy =
							operateType == 'mdCopy' &&
							(newDataIndexs || []).includes(rowId);
					if (isNew && !isCopy) {
						dataUpdater.insert(api, insertRowId, [item]);
					} else {
						dataUpdater.moveTo(insertRowId, [item]);
					}
				}
				updateOrder.splice(updateIdx, 1);
				updateOrder.splice(i, 0, rowId);
				positionChangedRowId.add(item.rowId);
			} else if (newKeySet.has(rowId)) {
				//新数据总要添加一下
				let prevItem = (i >= 1) ? sortedDetails[i - 1] : null;
				if (prevItem && prevItem.record_type == item.record_type) {
					insertRowId = prevItem.rowId;
					dataUpdater.insert(api, insertRowId, [item]);
				} else {
					dataUpdater.add([item]);
				}
			} else if (
				item.parent_rowId &&
				positionChangedRowId.has(item.parent_rowId)
			) {
				//bom子件，如果母键移动过，子也必须移动一下
				let prevItem = sortedDetails[i - 1];
				insertRowId = prevItem.rowId;
				dataUpdater.moveTo(insertRowId, [item]);
			}
		}
	}

	//移动数据
	sortAndMoveProducts(param, mdAdd) {
		const { dataUpdater, dataGetter } = param;
		const details = dataGetter.getDetail(this.detailApi);

		// 分别处理组合数据和赠品数据
		this.handleGroupProducts(details, mdAdd, dataUpdater, this.detailApi);
		this.handleGifts(details, mdAdd, dataUpdater, this.detailApi);
	}

	//处理组合数据和组合赠品
	handleGroupProducts(products, gifts, dataUpdater, api) {
		const groupMap = new Map();
		const productMap = new Map();

		products.forEach(product => {
			const { group_key, prod_pkg_key, parent_gift_key } = product;
			if (group_key) {
				if (!groupMap.has(group_key)) {
					groupMap.set(group_key, []);
				}
				groupMap.get(group_key).push(product);
			} else if (parent_gift_key) {
				//构建 productMap，记录每个产品的赠品（非组合赠品）
				if (!productMap.has(parent_gift_key)) {
					productMap.set(parent_gift_key, []);
				}
				productMap.get(parent_gift_key).push(product);
			}
		});

		groupMap.forEach(groupProducts => {
			const group_key = groupProducts[0].group_key;
			const groupGifts = gifts.filter(gift => gift.group_key === group_key);

			//移动组合内产品&产品原有赠品
			for (let i = groupProducts.length - 1; i > 0; i--) {
				const item = groupProducts[i];
				dataUpdater.nodeMoveTo(groupProducts[0].rowId, item.rowId);

				const itemGifts = productMap.get(item.prod_pkg_key) || [];
				if (itemGifts.length) {
					dataUpdater.moveTo(item.rowId, itemGifts);
				}
			}

			// 处理组合的赠品
			if (groupGifts.length > 0) {
				const lastProduct = groupProducts[groupProducts.length - 1];
				//如果组合数据最后一条要考虑其可能有自己的赠品，要找到他的赠品最后一条，作为组合赠品移动的目标位置
				const lastPosition = this.findLastGiftProduct(lastProduct, productMap);
				dataUpdater.insert(api, lastPosition.rowId, groupGifts);
			}
		});
	}

	findLastGiftProduct(product, productMap) {
		const gifts = productMap.get(product.prod_pkg_key) || [];
		if (gifts.length > 0) {
			return gifts[gifts.length - 1];
		}
		return product;
	}

	//处理普通赠品
	handleGifts(products, gifts, dataUpdater, api) {
		const giftsMap = new Map();

		gifts.forEach(gift => {
			const { parent_gift_key, group_key } = gift;
			if (parent_gift_key && !group_key) {
				if (!giftsMap.has(parent_gift_key)) {
					giftsMap.set(parent_gift_key, []);
				}
				giftsMap.get(parent_gift_key).push(gift);
			}
		});
		// 批量插入赠品
		giftsMap.forEach((giftList, key) => {
			if (key == "master") {
				if (products.length) {
					const insertRowId = products[0].rowId;
					dataUpdater.insert(api, insertRowId, giftList, true);
				} else {
					dataUpdater.add(giftList);
				}
			} else {
				const lastProductIndex = products.lastIndexOf(p => p.prod_pkg_key === key || p.parent_gift_key === key);
				const lastProduct = products[lastProductIndex];

				dataUpdater.insert(api, lastProduct.rowId, giftList);
			}
		});
	}

	//赠品/组合产品排序
	sortDetails(param, rtSet) {
		const me = this;
		rtSet.forEach((rt) => {
			param.dataUpdater.sort(this.detailApi, rt, (details) => {
				return PPM.sortPricePolicyDetails(details);
			});
		});
	}

	//设置不可编辑字段
	setReadOnlyFields(param, oriUiValue) {
		//通过底层方法更新UI事件其他数据
		if (oriUiValue) {
			if (oriUiValue.data) {
				oriUiValue.data[this.masterApi] && delete oriUiValue.data[this.masterApi];
				oriUiValue.data[this.detailApi] && delete oriUiValue.data[this.detailApi];
			}
			param.dataUpdater.updateByUIEventResult(oriUiValue);
		}
		const me = this,
			{ parent_gift_key, price_policy_id, pricing_mode, service_start_time, service_end_time,settlement_rate,settlement_cycle,settlement_period,settlement_amount_per_period } = this.fieldMap,
			fields = param.dataGetter.getDescribe(this.detailApi).fields,
			packageFields = Object.keys(fields).filter(
				(name) => fields[name].define_type == 'package'
			),
			masterData = param.dataGetter.getMasterData(),
			details = param.dataGetter.getDetail(this.detailApi),
			policies =
				(this.policyConfig && this.policyConfig.pricePolicies) || [],
			gifts = details.filter((d) => d[parent_gift_key]);

		//设置从对象有政策非赠品的明细里修改量字段不可编辑
		//根据政策归档明细
		const detailPolicyMap = details.reduce((accMap, item) => {
			const policyId = item[price_policy_id];
			if (policyId && !item[parent_gift_key]) {
				accMap[policyId] = accMap[policyId] || [];
				accMap[policyId].push(item.rowId);
			}
			return accMap;
		}, {});

		//所有数据先重置一下不可编辑字段，否则切换政策的数据不可编辑字段无法恢复
		param.dataUpdater.setReadOnly({
			dataIndex: details.map((d) => d.rowId),
			fieldName: [],
			status: false,
			operateId: me.pluginUniqId
		});

		Object.keys(detailPolicyMap).forEach((pId) => {
			if (pId) {
				const policy = policies.find((p) => p.id == pId),
					executions_d =
						(policy &&
							policy.executionFieldMap &&
							policy.executionFieldMap[me.detailApi]) ||
						null;
				executions_d &&
					param.dataUpdater.setReadOnly({
						dataIndex: detailPolicyMap[pId],
						fieldName: executions_d,
						status: true,
						operateId: me.pluginUniqId
					});
			}
		});


		const periodGiftIds = [];
		const normalGiftIds = [];

		// 分类赠品
		gifts.forEach(g => {
			if (pricing_mode &&g[pricing_mode] === "cycle") {
				periodGiftIds.push(g.rowId);
			} else {
				normalGiftIds.push(g.rowId);
			}
		});

		//设置非周期性赠品，所有预制字段不可编辑
		param.dataUpdater.setReadOnly({
			dataIndex: normalGiftIds,
			fieldName: packageFields,
			status: true
		});

		//设置周期性赠品，除开始时间、结算频率、结算周期、结算期数、每期标准结算金额以外的预制字段不可编辑
		if (periodGiftIds.length > 0) {
			const disableFields = packageFields.filter(
				f => ![service_start_time,settlement_rate,settlement_cycle,settlement_period,settlement_amount_per_period].includes(f)
			);
			param.dataUpdater.setReadOnly({
				dataIndex: periodGiftIds,
				fieldName: disableFields,
				status: true,
			});
		}

		gifts.forEach(g => {
			//设置赠品属性字段不可编辑
			if (g.giftAttrFields?.length > 0) {
				param.dataUpdater.setReadOnly({
					dataIndex: [g.rowId],
					fieldName: g.giftAttrFields,
					status: true
				});
			}
		})

		//设置主对象修改量字段不可编辑
		if (masterData[price_policy_id]) {
			const policy = policies.find(
				(p) => p.id == masterData[price_policy_id]
			),
				executions_m =
					(policy &&
						policy.executionFieldMap &&
						policy.executionFieldMap[me.masterApi]) ||
					null;
			executions_m &&
				param.dataUpdater.setReadOnly({
					fieldName: executions_m,
					status: true,
					operateId: me.pluginUniqId
				});
		}
	}

	createGiftIdentifier(d) {
		if (!d) {
			return '';
		}

		// 提取所需的属性
		const pricePolicyId = d.price_policy_id || '';
		const pricePolicyRuleId = d.price_policy_rule_ids?.[0] || '';;
		const parentGiftKey = d.parent_gift_key || '';
		const productId = d.product_id || '';
		const actualUnit = d.actual_unit || d.unit__v || '';

		// 构建 key
		const policyKey = d.group_key || `${pricePolicyId}_${pricePolicyRuleId}_${parentGiftKey}`;
		const key = `${policyKey}_${productId}_${actualUnit}`;

		return key;
	}

	// 收集赠品id
	collectGiftIds(detailsArr, mdDel) {
		const giftIdMap = new Map();
		detailsArr.forEach((d) => {
			if (
				d.is_giveaway == '1' &&
				d.parent_gift_key &&
				d._id &&
				mdDel.includes(d.prod_pkg_key)
			) {
				const key = this.createGiftIdentifier(d);
				// 检查Map中是否存在key对应的Set，不存在则初始化，并在存在时直接添加_id
				if (!giftIdMap.has(key)) {
					giftIdMap.set(key, new Set());
				}
				giftIdMap.get(key).add(d._id);
			}
		});
		return giftIdMap;
	}

	//初始化政策日志
	createLog(serviceName) {
		const processId = PPM.uniqueCode(),
			startTime = new Date().toISOString();
		return {
			requestId: this.requestId,
			processId: `${processId}_end`,
			startTime: new Date().toISOString(),
			endTime: '',
			durationMs: 0,
			serviceName: serviceName,
			details: []
		};
	}

	//价格政策的额外参数
	createExtraParam(param) {
		return {
			allDetailsInfo: param.dataGetter.getDetails(),
			triggerCal:param.triggerCal,
			triggerCalAndUIEvent:param.triggerCalAndUIEvent
		}
	}
	//
	checkAndLogPolicyResult(result) {
		//数据校验不通过时实时上传日志，否则走手动上报
		if (!this.checkPolicyResult(result)) {
			this.sendLog({
				eventId: 'fs-crm-sfa-pricePolicyPlugin-log',
				eventType: 'PROD',
				eventName: 'pv',
				apiName: 'price_policy',
				data: result?.promoteProcessLog
			})
		} else {
			return true;
		}
	}

	checkPolicyResult() {
		return true;
	}


}
