
import PolicyUtil from '../src/package/policy_util';
import { newDataIsEmpty, newDataNoPolicyId, newDataOnlyPolicyId, newDataHoldChose, newDataNormal, mockGiftRule, mockGiftRuleScene } from '../helpers/testdata';
import { fieldMapData } from '../helpers/testhelper';
import { DetailDataFace, GiftMap, GiftMapItemFace, PolicyDataFace } from '../src/package/new_data_interface';
import { FieldMapFace } from '../src/package/data_interface';

jest.mock('plugin_public_methods', () => ({
  ...jest.requireActual('plugin_public_methods').default,
  multiplicational: (num1, num2) => num1 * num2,
  accSub: (minuend, subtrahend) => minuend - subtrahend,
}))

describe('overlapArr', () => {
  test('测试两个数组是否有重合', () => {
    const arr1 = [1, 2, 3, 4, 5];
    const arr2 = [3, 4, 5, 6, 7];
    const result = PolicyUtil.overlapArr(arr1, arr2);
    expect(result).toBe(true);
  });
});

describe('collectChangeFields', () => {

  it('收集合并修改字段有去重', () => {
    const inputParam = {
      masterApi: 'masterApi',
      detailApi: 'detailApi',
      masterUpdate: { field1: 'value1', field2: 'value2', field3: 'value3' },
      mdUpdate: {
        row1: { field3: 'value3', field4: 'value4' },
        row2: { field5: 'value5', field6: 'value6', field4: 'value4' },
      },
      modifyFields: {
        masterApi: ['field1', 'field8'],
        detailApi: ['field4', 'field10'],
      },
    };

    const expectedOutput = {
      masterApi: ['field1', 'field2', 'field3', 'field8'],
      detailApi: ['field3', 'field4', 'field5', 'field6', 'field10'],
    };

    expect(PolicyUtil.collectChangeFields(inputParam)).toEqual(expectedOutput);
  });
  it('处理空值', () => {
    const inputParam = {
      masterApi: 'masterApi',
      detailApi: 'detailApi',
      masterUpdate: {},
      mdUpdate: {},
      modifyFields: {
        masterApi: [],
        detailApi: [],
      },
    };

    const expectedOutput = {
      masterApi: [],
      detailApi: [],
    };
    expect(PolicyUtil.collectChangeFields(inputParam)).toEqual(expectedOutput);
  });
  it('处理异常值', () => {
    const inputParam = {
      masterApi: 'masterApi',
      detailApi: 'detailApi',
      masterUpdate: null,
      mdUpdate: {
        row1: { field3: 'value3', field4: 'value4' },
      },
      modifyFields: {
        masterApi: null,
        detailApi: null,
      },
    };

    // 你需要根据你的方法的行为来设定这个期望的输出
    const expectedOutput = {
      masterApi: [],
      detailApi: ["field3", "field4"],
    };

    expect(PolicyUtil.collectChangeFields(inputParam)).toEqual(expectedOutput);
  });
});

describe('fieldsInCondition', () => {
  it('如果changeFields中的任何字段出现在conditionFields或specialCondition中，则应返回true', () => {
    const changeFields = {
      api1: ['field1', 'field2'],
      api2: ['field3', 'field4'],
    };
    const conditionFields = {
      api1: ['field1', 'field5'],
      api2: ['field6', 'field7'],
    };
    const specialCondition = ['field8', 'field9'];

    expect(PolicyUtil.fieldsInCondition(changeFields, conditionFields, specialCondition)).toBe(true);

    changeFields.api1[0] = "fields10";
    specialCondition.push('field3');
    expect(PolicyUtil.fieldsInCondition(changeFields, conditionFields, specialCondition)).toBe(true);
  });

  it('如果changeFields中的任何字段都没出现在conditionFields或specialCondition中，则应返回false', () => {
    const changeFields = {
      api1: ['field1', 'field2'],
      api2: ['field3', 'field4'],
    };
    const conditionFields = {
      api1: ['field5', 'field6'],
      api2: ['field7', 'field8'],
    };
    const specialCondition = ['field9', 'field10'];

    expect(PolicyUtil.fieldsInCondition(changeFields, conditionFields, specialCondition)).toBe(false);
  });

  it('应正确处理null和undefined输入', () => {
    const changeFields = null;
    const conditionFields = undefined;
    const specialCondition = null;

    expect(PolicyUtil.fieldsInCondition(changeFields, conditionFields, specialCondition)).toBe(false);
  });
});

describe("校验处理接口返回的数据：verifyPolicyData", () => {

  it("fullyUpdate & 返回结果为空对象时：", () => {
    const result = PolicyUtil.verifyPolicyData(newDataIsEmpty);
    expect(result).toEqual({
      price_policy_id: null,
      price_policy_rule_ids: null,
      policy_dynamic_amount: 0,
      gift_map: null,
    });
  })

  it("fullyUpdate & 返回结果里不包括price_policy_id：", () => {
    const result = PolicyUtil.verifyPolicyData(newDataNoPolicyId);
    expect(result).toEqual({
      price_policy_id: null,
      price_policy_rule_ids: null,
      policy_dynamic_amount: 0,
      gift_map: null,
    });
  })

  it("fullyUpdate & 返回结果里只清空了price_policy_id：", () => {
    const result = PolicyUtil.verifyPolicyData(newDataOnlyPolicyId);

    expect(result).toEqual(Object.assign(newDataOnlyPolicyId.nData, {
      gift_map: null,
    }));
  })

  it("fullyUpdate & 返回结果里只清空了price_policy_id：", () => {
    const result = PolicyUtil.verifyPolicyData(newDataHoldChose);
    expect(result).toEqual({
      price_policy_id: "aGiftPolicy",
      gift_map: {
        "ruleId_1": {
          "hold_chose": "1",
          productList: [{
            name: "产品A",
            quantity: 2
          }]
        }
      }
    });
  })
  it("fullyUpdate & 返回结果是正常数据：", () => {
    const result = PolicyUtil.verifyPolicyData(newDataNormal);
    expect(result).toEqual({
      price_policy_id: 'policy1',
      price_policy_rule_ids: ['rule1'],
      group_key: null,
      policy_dynamic_amount: -50,
      gift_map: null,
    });
  })
})

describe('校验判断赠品政策方法:hasGiftPolicy', () => {

  it(' gift_map 为null时应该返回false', () => {
    const data = {
      gift_map: null,
    };
    expect(PolicyUtil.hasGiftPolicy(data as PolicyDataFace)).toBe(false);
  });

  it('gift_map 为空对象时应该返回false', () => {
    const data = {
      gift_map: {},
    };
    expect(PolicyUtil.hasGiftPolicy(data as PolicyDataFace)).toBe(false);
  });

  it('gift_map有数据时应该返回true', () => {
    const data = <PolicyDataFace>{
      gift_map: <GiftMap>{
        'rule1': <GiftMapItemFace>{ /*...GiftMapItemFace 属性*/ }
      },
    };
    expect(PolicyUtil.hasGiftPolicy(data)).toBe(true);
  });
  it('data为null时应该返回false', () => {
    const data = null;
    expect(PolicyUtil.hasGiftPolicy(data)).toBe(false);
  });
  it('data为空对象时应该返回false', () => {
    const data = {};
    expect(PolicyUtil.hasGiftPolicy(data as PolicyDataFace)).toBe(false);
  });
});

describe('verifyGiftMap 方法', () => {
  it('当 giftMap 为 null 时，应返回 null', () => {
    expect(PolicyUtil.verifyGiftMap('noUpdate', null, null)).toBeNull();
  });

  it('当 matchFrom 为 "noUpdate" 且 gift_kind_upper_limit 和 gift_total_num 为 null 时，应将这两个值设置为 Infinity', () => {
    const input = {
      '1': <GiftMapItemFace>{
        gift_condition: '',
        gift_condition_unit__s: '',
        gift_condition_unit__id: '',
        gift_kind_upper_limit: null,
        gift_list: [],
        gift_total_num: null,
        object_api_name: '',
        rule_type: 'gift',
        type: 'OPTIONAL',
        cycle_count: 0
      }
    },
      oData = <DetailDataFace>{
        rowId: "rowId"
      };

    const output = PolicyUtil.verifyGiftMap('noUpdate', input, oData);
    expect(output['1'].gift_kind_upper_limit).toBe(Infinity);
    expect(output['1'].gift_total_num).toBe(Infinity);
  });

  it('当 hold_chose 为 "1" 时，应将 cacheInit 设置为 true', () => {
    const input = {
      '1': <GiftMapItemFace>{
        gift_condition: '',
        gift_condition_unit__s: '',
        gift_condition_unit__id: '',
        gift_kind_upper_limit: 10,
        gift_list: [],
        gift_total_num: 10,
        object_api_name: '',
        rule_type: 'gift',
        type: 'OPTIONAL',
        cycle_count: 0,
        hold_chose: '1'
      }
    },
      oData = <DetailDataFace>{
        rowId: "rowId"
      };

    const output = PolicyUtil.verifyGiftMap('noUpdate', input, oData);
    expect(output['1'].cacheInit).toBe(true);
  });

  it('当 老数据的规则里有新匹配的政策规则，应将 cacheInit 设置为 true', () => {
    const input = {
      'rule1': <GiftMapItemFace>{
        gift_condition: '',
        gift_condition_unit__s: '',
        gift_condition_unit__id: '',
        gift_kind_upper_limit: 10,
        gift_list: [],
        gift_total_num: 10,
        object_api_name: '',
        rule_type: 'gift',
        type: 'OPTIONAL',
        cycle_count: 0,

      }
    },
      oData = <DetailDataFace>{
        rowId: "rowId",
        price_policy_rule_ids: ["rule1"]
      };

    const output = PolicyUtil.verifyGiftMap('noUpdate', input, oData);
    expect(output["rule1"].cacheInit).toBe(true);
  });
});

describe('completeHoldChoose 方法', () => {
  it('当没有赠品政策时，应返回 null', () => {
    jest.spyOn(PolicyUtil, 'hasGiftPolicy').mockReturnValue(false);

    const data: DetailDataFace = {
      price_policy_id: null,
      price_policy_rule_ids: [],
      master_policy_id: null,
      group_key: '',
      policy_dynamic_amount: 0,
      gift_map: null,
      rowId: 'row1',
      dataIndex: 'index1',
      prod_pkg_key: 'key1',
    };

    expect(PolicyUtil.completeHoldChoose(data)).toBeNull();
  });

  it('当有赠品政策时，应为 gift_map 中的每个项目添加 hold_chose 属性', () => {
    jest.spyOn(PolicyUtil, 'hasGiftPolicy').mockReturnValue(true);

    const data: DetailDataFace = {
      price_policy_id: 'policy1',
      price_policy_rule_ids: ['rule1', 'rule2'],
      master_policy_id: 'master1',
      group_key: 'group1',
      policy_dynamic_amount: 100,
      gift_map: {
        'rule1': <GiftMapItemFace>{
          gift_condition: '',
          gift_condition_unit__s: '',
          gift_condition_unit__id: '',
          gift_kind_upper_limit: 10,
          gift_list: [],
          gift_total_num: 10,
          object_api_name: '',
          rule_type: 'gift',
          type: 'OPTIONAL',
          cycle_count: 0,
        }
      },
      rowId: 'row1',
      dataIndex: 'index1',
      prod_pkg_key: 'key1',
    };

    const output = PolicyUtil.completeHoldChoose(data);
    for (let rule in output) {
      expect(output[rule].hold_chose).toBe('1');
    }
  });
});

describe('recombineGiftMap 方法', () => {
  it('当 oriGiftMap 中存在对应的 ruleId 时，且新数据有hold_choose,应返回一个新的对象，其 hold_chose 属性已被设置为 "1"', () => {
    const nData = {
      price_policy_id: 'policy1',
      price_policy_rule_ids: ['rule1', 'rule2'],
      master_policy_id: 'master1',
      group_key: 'group1',
      policy_dynamic_amount: 100,
      gift_map: {
        'rule1': <GiftMapItemFace>{
          gift_condition: '',
          gift_condition_unit__s: '',
          gift_condition_unit__id: '',
          gift_kind_upper_limit: 10,
          gift_list: [],
          gift_total_num: 10,
          object_api_name: '',
          rule_type: 'gift',
          type: 'OPTIONAL',
          cycle_count: 0,
          hold_chose: "1"
        }
      },
    };

    const oData = {
      price_policy_id: 'policy2',
      price_policy_rule_ids: ['rule1'],
      master_policy_id: 'master2',
      group_key: 'group2',
      policy_dynamic_amount: 200,
      gift_map: {
        'rule1': <GiftMapItemFace>{
          gift_condition: '',
          gift_condition_unit__s: '',
          gift_condition_unit__id: '',
          gift_kind_upper_limit: 5,
          gift_list: [],
          gift_total_num: 5,
          object_api_name: '',
          rule_type: 'gift',
          type: 'OPTIONAL',
          cycle_count: 0,
        }
      },
      rowId: 'row1',
      dataIndex: 'index1',
      prod_pkg_key: 'key1',
    };

    const output = PolicyUtil.recombineGiftMap(nData, oData);
    for (let ruleId in output) {
      if (oData.gift_map && oData.gift_map.hasOwnProperty(ruleId)) {
        expect(output[ruleId].hold_chose).toBe('1');
        //赠品总数量应等于oData中的数量
        expect(output[ruleId].gift_total_num).toBe(oData.gift_map[ruleId].gift_total_num);
      }
    }
  });

  it('当 oriGiftMap 中不存在对应的 ruleId 时，应返回 nData.gift_map 中的对象', () => {
    const nData: PolicyDataFace = {
      price_policy_id: 'policy1',
      price_policy_rule_ids: ['rule1', 'rule2'],
      master_policy_id: 'master1',
      group_key: 'group1',
      policy_dynamic_amount: 100,
      gift_map: {
        'rule1': <GiftMapItemFace>{
          gift_condition: '',
          gift_condition_unit__s: '',
          gift_condition_unit__id: '',
          gift_kind_upper_limit: 10,
          gift_list: [],
          gift_total_num: 10,
          object_api_name: '',
          rule_type: 'gift',
          type: 'OPTIONAL',
          cycle_count: 0,
        },
        'rule2': <GiftMapItemFace>{
          gift_condition: '',
          gift_condition_unit__s: '',
          gift_condition_unit__id: '',
          gift_kind_upper_limit: 15,
          gift_list: [],
          gift_total_num: 15,
          object_api_name: '',
          rule_type: 'gift',
          type: 'OPTIONAL',
          cycle_count: 0,
        }
      },
    };

    const oData: DetailDataFace = {
      price_policy_id: 'policy2',
      price_policy_rule_ids: ['rule1'],
      master_policy_id: 'master2',
      group_key: 'group2',
      policy_dynamic_amount: 200,
      gift_map: {
        'rule1': <GiftMapItemFace>{
          gift_condition: '',
          gift_condition_unit__s: '',
          gift_condition_unit__id: '',
          gift_kind_upper_limit: 5,
          gift_list: [],
          gift_total_num: 5,
          object_api_name: '',
          rule_type: 'gift',
          type: 'OPTIONAL',
          cycle_count: 0,
        }
      },
      rowId: 'row1',
      dataIndex: 'index1',
      prod_pkg_key: 'key1',
    };

    const output = PolicyUtil.recombineGiftMap(nData, oData);
    for (let ruleId in output) {
      if (!oData.gift_map || !oData.gift_map.hasOwnProperty(ruleId)) {
        expect(output[ruleId]).toEqual(nData.gift_map[ruleId]);
      }
    }
  });
});

describe('parseNumByDecimal', () => {
  test('Handles regular numbers with no decimal', () => {
    expect(PolicyUtil.parseNumByDecimal(5, 2)).toBe(5);
  });

  test('Handles regular numbers with decimals', () => {
    expect(PolicyUtil.parseNumByDecimal(5.123, 2)).toBe(5.12);
  });

  test('Handles numbers with decimals and isToFix set to true', () => {
    expect(PolicyUtil.parseNumByDecimal(5.126, 2, undefined, true)).toBe(5.13);
  });

  test('Handles numbers with decimals and isToFix set to false', () => {
    expect(PolicyUtil.parseNumByDecimal(5.126, 2, undefined, false)).toBe(5.12);
  });

  test('Handles negative numbers with decimals', () => {
    expect(PolicyUtil.parseNumByDecimal(-5.123, 2)).toBe(-5.12);
  });

  test('Handles values with a decimal point at the beginning', () => {
    expect(PolicyUtil.parseNumByDecimal('.123', 2)).toBe(0.12);
  });

  test('Handles non-numeric values with defaultVal', () => {
    expect(PolicyUtil.parseNumByDecimal('non-numeric', 2, 0)).toBe(0);
  });

  test('Handles non-numeric values without defaultVal', () => {
    expect(PolicyUtil.parseNumByDecimal('non-numeric', 2)).toBe("non-numeric");
  });
});

// describe('PolicyUtil的分配可选赠品方法parseOptionalGift', () => {
//   // 使用之前创建的mock数据
//   const mockRuleId = 'rule_1';
//   const mockQDecimal = 2;
//   const mockPriceBookId = 'pricebook_test';
//   const mockCommonProps = {
//     commonKey: 'commonValue'
//   };

//   test.each(mockGiftRuleScene)('基于每一种测试场景赋值赠品限制值 %#', (scenario) => {

//     mockGiftRule.gift_total_num= scenario.rule[0];
//     mockGiftRule.gift_kind_upper_limit=scenario.rule[1];
// 	  mockGiftRule.cycle_count=scenario.rule[2];
//     mockGiftRule.gift_list.forEach((item, idx) => {
//       const giftItem = scenario.giftList[idx];
//       item.max_value = giftItem[0];
//       item.min_value = giftItem[1];
//       item.required = giftItem[2];
//     })
//     const result = PolicyUtil.parseOptionalGift(
//       mockRuleId,
//       mockGiftRule,
//       mockCommonProps,
//       mockQDecimal,
//       mockPriceBookId,
//       fieldMapData
//     );
// 	//expect(result).toBe({});
//     scenario.expect.forEach((expectedOutcome, index) => {
//       expect(result.gift_list[index].quantity).toBe(expectedOutcome[0]);
//       expect(result.gift_list[index].isActive).toBe(expectedOutcome[1]);
//     });
//   })

// });

// describe('PolicyUtil分配赠品数量方法initQuantity', () => {
//   // 使用之前创建的mock数据

//   const mockGift = {
//     ...mockGiftRule.gift_list[2],
//     quantity: 1,
//     isActive: true
//   };

//   it('测试第三轮分配', () => {
//     const result = PolicyUtil.initQuantity(
//       mockGift,   //gift数据
//       1, //activeKind
//       3, //maxKind
//       2, //activeNum
//       40, //maxNum
//       2,//qDecimal
//       2, //cycle
//       1, //stage
//       fieldMapData
//     );

//     // 确保gift_list中的赠品被正确处理
//     expect(result.gift.quantity).toEqual(22);
//   });
// });
