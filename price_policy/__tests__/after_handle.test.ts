import Handle from '../src/package/after_handle';
import {
    DecimalMapFace,
} from '../src/package/data_interface';
import { fieldMapData } from '../helpers/testhelper';
import { param } from '../helpers/testdata';

jest.mock('plugin_public_methods', () => ({
    ...jest.requireActual('plugin_public_methods').default,
    multiplicational: (num1, num2) => num1 * num2,
    accSub: (minuend, subtrahend) => minuend - subtrahend,
}));

const constructor_params = {
    masterApiName: 'SalesOrderObj',
    detailApiName: 'SalesOrderProductObj',
    fromType: 'SalesOrder',
    recordType: 'normal',
    request: {},
    triggerCal: () => {},
    triggerUIEvent: () => {},
    fieldMap: fieldMapData,
    decimalMap: <DecimalMapFace>{},
    fields: <{ [api: string]: object }>{},
    events: <{ [type: string]: any }>{
        policy_event: 'policy_event'
    },
    greyPolicyBatch: false,
};

describe('after_handle', () => {
    const handle = new Handle(
        constructor_params.masterApiName,
        constructor_params.detailApiName,
        constructor_params.fromType,
        constructor_params.recordType,
        constructor_params.request,
        constructor_params.triggerCal,
        constructor_params.triggerUIEvent,
        constructor_params.fieldMap,
        constructor_params.decimalMap,
        constructor_params.fields,
        constructor_params.events,
        constructor_params.greyPolicyBatch
    );

    test('after_handle_this', () => {
        expect(handle.masterApiName).toBe('SalesOrderObj');
        expect(handle.detailApiName).toBe('SalesOrderProductObj');
        expect(handle.fromType).toBe('SalesOrder');
        expect(handle.recordType).toBe('normal');
        //expect(handle.greyPolicyBatch).toBe(false);
    });

    test('after_handle_afterHandle', async () => {
        const res_null = await handle.afterHandle(null, { trigger: false });
        expect(res_null).toBe(null);

        const res_param = await handle.afterHandle(param, { trigger: true });
        expect(
            Object.keys(res_param.detailDataMap).includes('groupGift1')
        ).toBe(false);
        expect(
            Object.keys(res_param.detailDataMap).includes('prod4')
        ).toBe(true);
        expect(
            Object.keys(res_param.detailDataMap).includes('prod5')
        ).toBe(false);
    });

    test('after_handle_updateUiRes', () => {
        const updateUiRes_param_res = {
            Value: {
                data: {
                    SalesOrderObj: {
                        master_data_test: 'master_data_test',
                    },
                    SalesOrderProductObj: {}
                }
            },
        }
        const res = handle.updateUiRes(param, updateUiRes_param_res);
        // expect(res.masterData).toEqual({
        //     account_id: '',
        //     master_data_test: 'master_data_test',
        // });
        expect(res.oriUiValue.data).toEqual(updateUiRes_param_res.Value.data);
    })
    
});
