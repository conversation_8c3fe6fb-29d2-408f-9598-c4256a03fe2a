//LJ-01 accountId:"6205ccf446d0730001276e46"

import { createPolicyExecuteInstance } from '../helpers/testhelper';

import { masterFieldMapFace, FieldMapFace, DecimalMapFace, modifyInfoFace } from '../src/package/data_interface';

import { mockMatchParam, mockDetailPricePolicy } from "../helpers/testdata";

import PPM from 'ppm';
import PolicyUtil from '../src/package/policy_util';

jest.mock('plugin_public_methods', () => ({
    ...jest.requireActual('plugin_public_methods').default,
    multiplicational: (num1, num2) => num1 * num2,
    accSub: (minuend, subtrahend) => minuend - subtrahend,
}))

describe('PolicyExecute', () => {
    let instance;

    beforeEach(() => {
        // 清理所有模拟调用信息
        jest.clearAllMocks();
        instance = createPolicyExecuteInstance();

    });

    describe('请求客户政策配置状态：getConfigStatus', () => {
        it('should call PPM.ajax with correct URL and parameters', async () => {
            const account = 'testAccount';

            // 执行reqConfig方法
            const response = await instance.reqConfig(account);

            // 检查PPM.ajax是否被正确调用
            expect(response).toEqual('mocked request result');
        });

    })

    describe('是否触发价格政策：triggerPolicyCheck', () => {
        it('triggerPolicyCheck返回是否可以触发价格政策', () => {
            const detailsMap = {
                '1': { "name": "产品A" },
                '2': { "name": "产品B" },
            };
            const mInfo: modifyInfoFace = {
                modifyFields: {
                    "SalesOrderObj": [],
                    "SalesOrderProductObj": ['quantity'],
                },
                triggerPolicy: false,
                modifyIndex: ['1'],
            };
            const result = instance.triggerPolicyCheck(mInfo, detailsMap);
            expect(result).toBe(true);
        });
    })

    describe('match接口入参格式化：parseMatchArgs_d', () => {
        it('测试match detail的入参', () => {
            let extendArgs = {};
            const result = instance.parseMatchArgs_d(extendArgs, mockMatchParam);

            // 对返回值进行断言
            expect(result.matchArgs).toBeDefined();
            expect(result.matchArgs.batchNo).toBeDefined();
            expect(result.matchArgs.requestId).toBe(instance.requestId);
            expect(result.matchArgs.matchType).toBe('detail');
            expect(result.matchArgs.masterObjectApiName).toBe(instance.masterApiName);
            expect(result.matchArgs.accountId).toBe(mockMatchParam.masterData.account_id);
            expect(result.matchArgs.masterData).toBe(mockMatchParam.masterData);
            expect(result.matchArgs.detailDataMap).toEqual({
                "prodPkgKey4": {
                    "product_id": "normal_product",
                }
            });;
            expect(result.matchArgs.modifiedDataIndexList).toEqual(["prodPkgKey4"]);
            expect(result.matchArgs.modifiedFieldApiNames).toEqual(mockMatchParam.modifyInfo.modifyFields);
            expect(result.matchArgs.removeGroupKeySet).toEqual([]);
            expect(result.matchArgs.exactlyMatchModifiedData).toBe(false);
            expect(result.matchArgs.actionCode).toBe(instance.fromType == 'edit' ? 'Edit' : 'Add');
            expect(result.matchArgs.requestSource).toBe('web');
        })
    })

    describe('matchDetail返回结果格式化：matchDetail', () => {
        it('match Detail返回结果', async () => {
            // 监视 reqMatch 方法
            const reqMatchSpy = jest.spyOn(instance, 'reqMatch');

            // 使用 mockImplementation 或 mockResolvedValue 设置模拟返回值
            reqMatchSpy.mockImplementation(() => Promise.resolve(Object.assign(mockMatchParam, {
                matchRes: mockDetailPricePolicy
            })));

            //执行价格政策的matchDetail方法
            const result = await instance.matchDetail({}, mockMatchParam);
            expect(reqMatchSpy).toHaveBeenCalled();
            // expect(result).toEqual({a:"a"});
            expect(result.detailDataMap.prodPkgKey4).toEqual({
                "product_id": "normal_product",
                "rowId": 'prodPkgKey4',
                "dataIndex": 'prodPkgKey4',
                "prod_pkg_key": 'prodPkgKey4',
                "price_policy_id": 'policy1',
                "price_policy_rule_ids": ['rule1'],
                "group_key": null,
                "policy_dynamic_amount": -50,
                "gift_map": null,
                "master_policy_id": null,
            });
        });
    });

    describe('自动执行价格政策：executePolicy', () => {
        let mockMatchDetail, mockMatchMaster, mockCalBatch;

        beforeEach(() => {
            instance.triggerPolicyCheck = jest.fn();
            mockMatchDetail = jest.fn();
            mockMatchMaster = jest.fn();
            mockCalBatch = jest.fn();
            instance.matchDetail = mockMatchDetail;
            instance.matchMaster = mockMatchMaster;
            instance.calBatch = mockCalBatch;
        });
        afterEach(() => {
            jest.restoreAllMocks();  // 清除所有模拟
        });

        it('不触发价格政策时应该返回null', async () => {
            instance.triggerPolicyCheck.mockReturnValue(false);
            const result = await instance.executePolicy({ modifyInfo: {}, detailDataMap: {} });
            expect(result).toBeNull();
        });

        it('没有不分摊政策时（noAmortizeRules false）时，应该调用matchDetail并返回其结果', async () => {
            instance.triggerPolicyCheck.mockReturnValue(true);
            mockMatchDetail.mockResolvedValue('some data');

            const result = await instance.executePolicy({ modifyInfo: {}, detailDataMap: {} });
            expect(mockMatchDetail).toHaveBeenCalled();
            expect(result).toEqual('some data');
        });

        it('有不分摊政策时 应该正确组合方法调用并返回最终结果', async () => {
            jest.spyOn(PolicyUtil, 'getMasterRule').mockReturnValue(true);
            instance.triggerPolicyCheck.mockReturnValue(true);
            mockMatchDetail.mockResolvedValue('initial data');
            mockMatchMaster.mockResolvedValue('processed by matchMaster');
            mockCalBatch.mockResolvedValue('processed by calBatch');

            const result = await instance.executePolicy({ modifyInfo: {}, detailDataMap: {} });
            expect(mockMatchDetail).toHaveBeenCalled();
            expect(mockCalBatch).toHaveBeenCalled();
            expect(mockMatchMaster).toHaveBeenCalled();
            expect(result).toEqual('processed by matchMaster');
        });
    });

    describe('取消政策：cancelPolicy', () => {
        beforeEach(() => {
            jest.spyOn(instance, 'updateCancelRes').mockResolvedValue({
                masterData: {},
                tip: "processed by cancel"
            });
            jest.spyOn(instance, 'reqCancel').mockResolvedValue({masterData:{}});
            jest.spyOn(instance, 'parseCancelArgs').mockImplementation(args => args);
            jest.spyOn(instance, 'parseMatchArgs_d').mockImplementation((args, data) => data);
            jest.spyOn(instance, 'matchMaster').mockResolvedValue({ tip: "processed by matchMaster" });
            jest.spyOn(instance, 'calBatch').mockResolvedValue({ tip: "processed by calBatch" });
            jest.spyOn(instance, 'updateMatchRes').mockResolvedValue({masterData:{}, tip: "processed by matchDetail" });
            jest.spyOn(instance, 'reqMatch').mockResolvedValue({});
            jest.spyOn(instance, 'parseCancelArgs').mockImplementation(args => ({ ...args, cancelInfo: { isGroupData: false, restGroupKeys: [] } }));
            jest.spyOn(PolicyUtil, 'hasRuleByType').mockImplementation((rules, config, type) => type === true);
        });

        afterEach(() => {
            jest.restoreAllMocks();
        });

        it('没有分摊，没有不分摊，也没有组合政策时,只执行cancel逻辑', async () => {
            PolicyUtil.hasRuleByType.mockReturnValueOnce(false).mockReturnValueOnce(false);
            instance.parseCancelArgs.mockReturnValueOnce({ cancelInfo: { isGroupData: false, restGroupKeys: [] } });
            instance.notMatchGroupOtherRow = '0'; // Make sure it doesn't affect the test

            const result = await instance.cancelPolicy({ type: 'detail' });
            expect(result.tip).toEqual('processed by cancel');
            expect(instance.calBatch).not.toHaveBeenCalled();
        });

        it('有分摊和组合政策时，需要调用match detail接口', async () => {
            PolicyUtil.hasRuleByType.mockReturnValueOnce(false).mockReturnValueOnce(true);
            instance.parseCancelArgs.mockReturnValueOnce({ cancelInfo: { isGroupData: true, restGroupKeys: ['1'] } });
            instance.notMatchGroupOtherRow = '0';

            const result = await instance.cancelPolicy({ type: 'detail' });
            expect(result.tip).toEqual("processed by matchDetail");
            expect(instance.calBatch).toHaveBeenCalled();
            expect(instance.reqMatch).toHaveBeenCalled();
        });

        it('有不分摊政策和组合政策时', async () => {
            PolicyUtil.hasRuleByType.mockReturnValueOnce(true).mockReturnValueOnce(false);
            instance.parseCancelArgs.mockReturnValueOnce({ cancelInfo: { isGroupData: true, restGroupKeys: ['1'] } });
            instance.notMatchGroupOtherRow = '2';  // Ensure this setting is respected

            const result = await instance.cancelPolicy({ type: 'detail' });
            expect(result.tip).toEqual("processed by matchMaster");
            expect(instance.calBatch).toHaveBeenCalled();  // CalBatch may still be called
            expect(instance.reqMatch).toHaveBeenCalled();  // No match because of "1"
            expect(instance.matchMaster).toHaveBeenCalled(); 
        });
    });
    describe('selectPolicy', () => {
        beforeEach(() => {
            jest.spyOn(instance, 'parseMatchArgs_d').mockImplementation((args, data) => data);
            jest.spyOn(instance, 'matchDetail').mockResolvedValue({ tip: "processed by matchDetail" });
            jest.spyOn(instance, 'matchMaster').mockResolvedValue({ tip: "processed by matchMaster" });
            jest.spyOn(instance, 'calBatch').mockResolvedValue({ tip: "processed by calBatch" });
            jest.spyOn(PolicyUtil, 'hasRuleByType').mockImplementation((rules, config, type) => type === true);
        });
    
        afterEach(() => {
            jest.restoreAllMocks();
        });
    
        it('should handle detail type with no amortize policy', async () => {
            const param = { masterData: { price_policy_rule_ids: [], price_policy_id: '123' }};
            const result = await instance.selectPolicy(param, 'detail', 'targetPolicy', 'dataKey');
    
            PolicyUtil.hasRuleByType.mockReturnValueOnce(false);
            expect(instance.matchDetail).toHaveBeenCalled();
            expect(result.tip).toEqual('processed by matchDetail');
        });
    
        // it('should handle non-detail type with amortize and no amortize rules', async () => {
        //     const param = { masterData: { price_policy_rule_ids: [], price_policy_id: '123' }};
        //     const result = await instance.selectPolicy(param, 'master', 'targetPolicy', 'dataKey');
    
        //     expect(instance.matchDetail).toHaveBeenCalled();
        //     expect(instance.calBatch).toHaveBeenCalled();
        //     expect(instance.matchMaster).toHaveBeenCalled();
        //     expect(result).toEqual('matchMasterResult');
        // });
    
        // it('should handle non-detail type without any rules', async () => {
        //     PolicyUtil.getMasterRule.mockReturnValue(false); // No rules
        //     const param = { masterData: { price_policy_rule_ids: [], price_policy_id: '123' }};
        //     const result = await instance.selectPolicy(param, 'master', 'targetPolicy', 'dataKey');
    
        //     expect(instance.matchDetail).not.toHaveBeenCalled();
        //     expect(instance.calBatch).not.toHaveBeenCalled();
        //     expect(instance.matchMaster).not.toHaveBeenCalled();
        //     expect(result).toEqual(param); // Expecting the unchanged param if no operations are performed
        // });
    });
    describe('reqMatch', () => {  
  
        it('should call PPM.ajax with the correct URL and parameters', async () => {
            const param = {
                matchArgs: { someKey: 'someValue' } // 提供必要的测试参数
            };
            const result = await instance.reqMatch(param);
    
            // 检查返回值是否包含了ajax调用的结果
            expect(result.matchRes).toBe('mocked request result');
            expect(result).toEqual(param); // 确保返回的是修改后的param对象
        });
    });

});

