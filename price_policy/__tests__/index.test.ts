
//import Price_Policy from '../src/index';
//jest.mock('../src/index');
const Price_Policy = require('../src/index').default;

jest.mock('plugin_public_methods', () => ({
    ...jest.requireActual('plugin_public_methods').default,
    multiplicational: (num1, num2) => num1 * num2,
    accSub: (minuend, subtrahend) => minuend - subtrahend,
}))

const param = {
    dataGetter: {
        getDescribe: () => {},
        getDescribeLayout: () => ({
            layout: {
                events: []
            },
            detailObjectList: [],
        }),
        getRequestId: () => {},
        getMasterData: () => ({})
    },
    dataUpdater: {
        updateMaster: () => {}
    }
}

describe('Price_Policy', () => {
    let pricePolicy;

    beforeEach(() => {
        pricePolicy = new Price_Policy({
            api: {
                request: {}
            }
        }, {
            params:{
                fieldMapping:{}, 
            },
            bizStateConfig: {
                price_policy_support_percentile_gift: ''
            },
        });
    });



    describe('sortDetailsHandle', () => {
        it('单品赠和组合赠、主对象赠品要正确排序', () => {
            // mock details
            const details = [
                { prod_pkg_key: 'prod1', group_key: null, parent_gift_key: null, is_giveaway: '0' },
                { prod_pkg_key: 'prod2', group_key: 'group1', parent_gift_key: null, is_giveaway: '0' },
                { prod_pkg_key: 'masterGift1', group_key: null, parent_gift_key: "master", is_giveaway: '1' },
                { prod_pkg_key: 'prod3', group_key: 'group1', parent_gift_key: null, is_giveaway: '0' },
                { prod_pkg_key: 'gift1', group_key: null, parent_gift_key: 'prod1', is_giveaway: '1' },
                { prod_pkg_key: 'groupGift1', group_key: 'group1', parent_gift_key: 'prod2', is_giveaway: '1' }
            ];
            const sortedDetails = pricePolicy.mockSortHandle(details);
            // check if the returned array is sorted correctly
            expect(sortedDetails).toEqual([
                { prod_pkg_key: 'masterGift1', group_key: null, parent_gift_key: "master", is_giveaway: '1' },
                { prod_pkg_key: 'prod1', group_key: null, parent_gift_key: null, is_giveaway: '0' },
                { prod_pkg_key: 'gift1', group_key: null, parent_gift_key: 'prod1', is_giveaway: '1' },
                { prod_pkg_key: 'prod2', group_key: 'group1', parent_gift_key: null, is_giveaway: '0' },
                { prod_pkg_key: 'prod3', group_key: 'group1', parent_gift_key: null, is_giveaway: '0' },
                { prod_pkg_key: 'groupGift1', group_key: 'group1', parent_gift_key: 'prod2', is_giveaway: '1' }
            ]);
        });
        it('无赠品数据组合正确排序', () => {

            // mock details
            const details = [
                { prod_pkg_key: 'prod1', group_key: null, parent_gift_key: null, is_giveaway: '0' },
                { prod_pkg_key: 'prod2', group_key: 'group1', parent_gift_key: null, is_giveaway: '0' },
                { prod_pkg_key: 'prod4', group_key: null, parent_gift_key: null, is_giveaway: '0' },
                { prod_pkg_key: 'prod5', group_key: null, parent_gift_key: null, is_giveaway: '1' },
                { prod_pkg_key: 'prod3', group_key: 'group1', parent_gift_key: null, is_giveaway: '0' }
            ];
            const sortedDetails = pricePolicy.mockSortHandle(details);
            // check if the returned array is sorted correctly
            expect(sortedDetails).toEqual([
                { prod_pkg_key: 'prod1', group_key: null, parent_gift_key: null, is_giveaway: '0' },
                { prod_pkg_key: 'prod2', group_key: 'group1', parent_gift_key: null, is_giveaway: '0' },
                { prod_pkg_key: 'prod3', group_key: 'group1', parent_gift_key: null, is_giveaway: '0' },
                { prod_pkg_key: 'prod4', group_key: null, parent_gift_key: null, is_giveaway: '0' },
                { prod_pkg_key: 'prod5', group_key: null, parent_gift_key: null, is_giveaway: '1' },
            ]);
        });
        test('initModule', () => {
            
        })
    });

    // describe('init', () => {
    //     test('mdRenderBefore', async () => {
    //         const config = {
    //             greyPolicyBatch: true
    //         };

    //         await pricePolicy.mdRenderBefore(param, config);
    //     })
    // })

});



