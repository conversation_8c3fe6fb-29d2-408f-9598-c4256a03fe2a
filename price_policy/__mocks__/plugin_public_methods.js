// __mocks__/ppm.js
const PPM = {
    ajax: jest.fn(),
    pipeAsync: (...fns) => {
        return async (result) => {
            for (let fn of fns) {
                result = await fn(result)
            }
            return result;
        }
    },
    composeAsync: (...fns) => {
        return async (result) => {
            let list = [...fns];
            while (list.length > 0) {
                result = await list.pop()(result)
            }
            return result;
        }
    },
    curry: (arity, fn) => {
        return (function nextCurried(prevArgs) {
            return function curried(nextArg) {
                var args = [...prevArgs, nextArg];

                if (args.length >= arity) {
                    return fn(...args);
                }
                else {
                    return nextCurried(args);
                }
            };
        })([]);
    },
    collectCalFields: () => {
        return {};
    },

    multiplicational: (value, multiplier) => {
        return value * multiplier
    },

    division: (amount, price) => {
        return (amount / price)
    },
    accSub: (a, b) =>{
        return (a - b)
    } ,
    uniqueCode: () => {
        return 'unique_code'
    },

    parsePriceBookDataRangeDetails: (oDetails, priceBookIdField) => {
        return { parsedDetails: 'mockedDetails' };
    },
    collectCalFields: (arg1, arg2, arg3) => {
        return {
            detailApiName: ['field1', 'field2', 'field3']
        }
    },
    getCalFields: (arg1, arg2) => {
        return {
            detailApiName: ['field1', 'field2', 'field3']
        }
    }
}
// 确保所有 customerPPM 方法也被 jest.fn() 包裹
Object.keys(PPM).forEach(key => {
    if (typeof PPM[key] === 'function') {
      PPM[key] = jest.fn(PPM[key]);
    }
  });
export default PPM;

