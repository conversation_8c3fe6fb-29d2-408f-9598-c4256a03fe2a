{"name": "rebate", "version": "960.0.1", "description": "返利", "main": "dist/index.js", "scripts": {"release_old": "babel src --out-dir dist && npm publish", "build": "webpack --mode production --config webpack.product.config.js", "release": "webpack --mode production --config webpack.product.config.js && npm publish --registry https://registry-npm.firstshare.cn", "test": "jest"}, "author": "", "license": "ISC", "devDependencies": {"@babel/core": "^7.21.3", "@babel/plugin-transform-runtime": "^7.21.4", "@babel/preset-env": "^7.20.2", "@babel/preset-typescript": "^7.21.0", "babel-loader": "^9.1.2", "babel-cli": "^6.26.0", "babel-preset-es2015": "^6.24.1", "babel-preset-stage-3": "^6.24.1", "ts-loader": "^9.4.2", "tslib": "^2.4.0", "webpack": "^5.70.0", "webpack-cli": "^4.9.2", "jest": "^29.5.0", "ts-jest": "^29.1.0", "@types/jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "typescript": "^4.6.4", "plugin_public_methods": "^1.8.12"}, "dependencies": {"plugin_base": "^1.1.0", "@babel/runtime": "^7.21.0"}}