import PPM from "plugin_public_methods";

import { FieldMapFace, ExecuteFace } from "./data_interface";

export default class PolicyAfterHandle {
    constructor(
        public masterApiName: string,
        public detailApiName: string,
        public request: any,
        public triggerUIEvent: any,
        public fieldMap: FieldMapFace,
        public events: {
            [type: string]: any
        }
    ) { }

    public updateTriggerUIEvent(newTriggerUIEvent:any) {
        this.triggerUIEvent = newTriggerUIEvent;
    }
    //调用UI事件
    public async calUiEvent(param: ExecuteFace) {

        const eventName = this.getUiEvent(param.masterData);

        if (!this.events[eventName]) {
            return param;
        }

        const dataFromPolicy = await PPM.composeAsync(
           PPM.curry(2, this.updateUiRes.bind(this))(param),
            this.triggerUIEvent.bind(this),
            PPM.curry(2, this.parseUiArgs.bind(this))(eventName),
        )(param);
        return dataFromPolicy;
    }

    private getUiEvent(masterData: any): string {

        const { rebate = [], rangeRebate = [], product_rebate = [] } = masterData.misc_content || {};
        const matchEvent = "rebate_match",
            cancelEvent = "rebate_cancel";

        if (rebate && rebate.length > 0) {
            return matchEvent;
        }
        if (rangeRebate && rangeRebate.some((item:any) => item.rangeRebates && item.rangeRebates.length > 0)) {
            return matchEvent;
        }
        if (product_rebate && product_rebate.length > 0) {
            return matchEvent;
        }
        return cancelEvent;

    }

    private parseUiArgs(eventName: string, param: ExecuteFace) {
        const args = {
            noLoading: true,
            noRetry: true,
            event: this.events[eventName],
            masterData: param.masterData,
            details: {
                [this.detailApiName]: Object.keys(param.detailDataMap).map((key: string) => param.detailDataMap[key])
            },
            objApiName: this.masterApiName
        }
        return args;
    }

    public updateUiRes(param: ExecuteFace, result: any) {
        const uiRes = result?.Value?.data;
        const oriUiValue = { ...result?.Value };

        if (!uiRes) {
            return { ...param, oriUiValue };
        }

        param.changeInfo.mdAdd = param.changeInfo.mdAdd || [];
        param.changeInfo.mdDel = param.changeInfo.mdDel || [];

        const resMaster = uiRes[this.masterApiName] || {},
            resDetail = uiRes[this.detailApiName] || {},

            oriDetailKeys = Object.keys(param.detailDataMap),

            curResIds: Array<string> = Object.keys(resDetail.u || {});

        //删除数据
        oriDetailKeys.forEach((key: string) => {
            if (!curResIds.includes(key)) {
                param.changeInfo.mdDel.push(key)
            }
        });


        //更新UI事件新增数据
        if (resDetail.a.length) {
            resDetail.a.forEach((a: any) => {
                const uniqueCode = PPM.uniqueCode();
                Object.assign(a,{
                    prod_pkg_key: uniqueCode,
                    data_index: uniqueCode,
                    rowId: uniqueCode
                })
                param.changeInfo.mdAdd.push(a);
            })
        }
        //更新数据
        const iDetailMap: any = {};
        curResIds.forEach((rowId: string) => {
            iDetailMap[rowId] = resDetail.u[rowId];
            param.detailDataMap[rowId] = {
                ...param.detailDataMap[rowId],
                ...resDetail.u[rowId]
            };
        });


        return {
            masterData: { ...param.masterData, ...resMaster },
            detailDataMap: param.detailDataMap,
            changeInfo: PPM.updateChangeInfo(param.changeInfo, resMaster, iDetailMap),
            modifyInfo: PPM.generateModifyInfo(resMaster, iDetailMap, this.masterApiName, this.detailApiName),
            policyInfo: param.policyInfo, 
            oriUiValue: oriUiValue
        };

    }

}
