export interface FieldMapFace {
    product_price: string;
    price_book_discount: string;
    price_book_price: string;
    price_book_id: string;
    price_book_id__r: string;
    price_book_product_id: string;
    price_book_product_id__r: string;
    dataId: string;
    account_id: string;
    misc_content: string;
    rebate_amortize_amount: string;
    rebate_dynamic_amount: string;
    rebate_amount: string;
    rebate_coupon_id: string;
    coupon_amortize_amount: string;
    coupon_dynamic_amount: string;
    coupon_amount: string;
    rebate_rule_id: string;
    product_rebate_rule_id: string;
    quantity: string;
    actual_unit: string;
    product_id: string;
    product_id__r: string;
    subtotal: string;
    range_rebate_rule_ids: string;
    is_multiple_unit:string;
    base_unit_count:string;
    conversion_ratio:string;
    stat_unit_count:string;

    pricing_period:string;
	pricing_mode:string;
	pricing_cycle:string;
	pricing_rate:string;
	whole_period_sale:string;
	settlement_mode:string;
	settlement_cycle:string;
	settlement_rate:string;
	service_start_time:string;
	service_end_time:string;

}

export interface DecimalMapFace {
    [props: string]: number;
}
export interface ExecuteFace {
    masterData: any;
    detailDataMap: {          //不包含赠品，用于请求接口的明细数据
        [key: string]: any;
    };
    detailsArr: Array<any>;   //包含赠品的全部明细
    rebateType: string;
    policyInfo: any;
    changeInfo: changeInfoFace;
    modifyInfo: modifyInfoFace;
    fundAccountId?:string;
    extraArgs?:any;
}

export interface changeInfoFace {
    masterUpdate: any;
    mdUpdate: any;
    mdAdd: Array<any>;
    mdDel: Array<any>;
    [props: string]: any;
}
export interface modifyInfoFace {
    modifyFields: {
        [objApi: string]: Array<string>;
    };
    modifyIndex: Array<string>;
}
//优惠券数据
export interface CouponFace {
    id: string;
    name: string;
    amount: string;
    lower_limit: string;
    desc: string;
    start_date: string;
    end_date: string;
    can_use:string;
}



export interface UseRebateFace {
    ruleId: string;
    rebateData: Array<any>;
    productRuleId: string;
    productRebateData: Array<any>;
    rangeRebateData: Array<any>;
}


export interface ConditionFieldsResFace {
    productRebateChange: boolean;
    rebateChange: boolean;
    rebateConditionField: {
        [apiname: string]: Array<string>
    };
    couponChange: boolean;
    couponConditionField: {
        [api: string]: Array<string>;
    };
}
export interface GetUnitGiftFace {
    gifts: Array<any>;
    unitType: string;
    info: null | string;
    detailsArr: Array<any>;
}
export interface UnitResFace {
    [product_id: string]: {
        [unitType: string]: {
            unit_id: string;
            unit__s: string;
        }
    }
}
/****************************** 返利适配客户账户 ******************************/

//客户账户ID
interface FundAccount {
    fundAccountId: string;
}
//当前使用的返利规则ID
interface Rule {
    ruleId: string;
}
export interface MasterData {
    misc_content: MiscContent | null;
    rebate_rule_id: string;
    product_rebate_rule_id: string;
    range_rebate_rule_ids: string;
    [props: string]: any;
}
export interface DetailData {
    rowId: string;
    misc_content: any | null;
    [props: string]: any;
}
//接口请求基本数据
interface RequestData {
    requestId: string;
    edit: boolean;  //是否是编辑界面的请求
    masterObjectApiName: string;
    detailObjectApiName: string;
    masterData: MasterData;
    detailDataMap: {
        [rowId: string]: DetailData;
    };
}

//返利查询接口入参（用户操作数据）
export interface RebateInfo extends FundAccount, Rule {
    needRule: boolean;//是否需要返回使用规则
    changeRule: boolean; //是否切换规则
    rebateType: string;  //"Money"，"Product"
    rangeRuleIds: Array<string>; //金额返利范围规则ids
}

//返利查询接口入参
export interface RebateQueryRequest extends RebateInfo, RequestData {
    accountId: string;
}

//查询返利单接口返回数据格式
export interface RebateQueryResponse {
    ruleId: string;
    rules: Array<RebateRule>;
    datas: Array<Rebate | ProductRebate>; //返利单或产品返利单
    limitMoney: number;
    rangeRuleIds: Array<string>;
    rangeRules: Array<RangeRebateRule>;
    rangeData: Array<Rebate>;  //|RangeRebateFace
    rebateType: string;  //前端补全参数
}
//返利单
export interface Rebate {
    can_use: string;
    start_date: number;
    end_date: number;
    last_modified_time: string;
    id: string;
    topic: string;
    name: string;
    rebate_type: string;
    sum_amount: string;
    unused_amount: string;
    use_type: string;    //使用类型，("Amount", "按金额"),("Quantity", "按数量")
}

//产品返利单
export interface ProductRebate extends Rebate {

    product_condition_type?: string;   //"FIXED","CONDITION","ALL"
    product_range?: {
        data: string | Array<RebateGoods>;
        object_api_name: string;
        gift_condition_unit_id: string;
    }
}
//query接口返回的返利品
export interface RebateGoods {
    pricebook_id: string;// 价目表id
    pricebook_id__r: string;// 价目表
    _id: string;      // 价目表产品id
    name: string;     // 价目表明细
    product_id:string;
    product_id__r:string;
    unit_id: string;
    unit__s: string;
    is_multiple_unit: boolean;  
    pricebook_price: string;  //价目表价格
    discount: string;        //价目表折扣
    selling_price: string;   //价格
    periodic_map?:object;
}

//返利使用规则
export interface RebateRule {
    id: string;
    last_modified_time: number;
    name: string;
    priority: number;
    product_range_type: string;
    rule_type: string;// "Money"
}

//有范围的返利使用规则
export interface RangeRebateRule extends RebateRule {
    limitMoney: number;
    data_index: Array<string>;//当前规则适用的产品行data_index数组
    rangeRebateIds: Array<string>;//当前规则适用的返利单id数组  
}


//格式化后的返利详情页返利单数据
export interface RebateDetails extends ProductRebate {
    using: boolean;
    use_amount: number;
    disabled: boolean;
    productList?: Array<any>;
    productCondition?:Array<any>;
}

//查询返利格式化后的返回数据

export interface UsedInfo{
    total:number;
    details:Array<{
        fundTotal:number;
        fundId:string;
        fundName?:string;
    }>
}
export interface RebateInfoDetails extends Rule {
    ruleName: string;
    limitMoney: number;
    rebates: Array<RebateDetails>;
    usedInfo:UsedInfo
}
export interface RebateQueryData {
    ruleId: string;
    rules: Array<RebateRule>;
    rangeRuleIds: Array<string>;
    rangeRules: Array<RangeRebateRule>;
    rebateData: Array<RebateInfoDetails>;
}

//分摊接口入参
export interface CouponUsed{
    id: string;
    amount: number;
}
export interface RebateUsed extends CouponUsed  {
    fund_account_id: string;
}
export interface ProductUsed{
    id: string;
    product_id: string;
    amount: number;
    price: string;
    quantity: string;
    priceBookPriceFlag?:string;
}
export interface ProductDetail{
    rebate_coupon_id:string;
    rebate_dynamic_amount:number;
}
export interface Product extends ProductUsed{
    unit_id: string;
    unit__s: string;
    rebate_coupon_id:string;
    rebate_coupon_object_api_name:string;
    [props:string]:any;
};


//返利详情页提交数据
export interface RebateSubmitData {
    //无范围金额返利
    ruleId: string;
    rebateData: Array<RebateUsed>;
    //产品返利
    productRuleId: string;
    productRebateData: Array<Product>
    //有范围金额返利
    rangeRebateData: {
        [rangeRuleId: string]: Array<RebateUsed>;
    };
}
export interface RebateRequest extends Rule {
    rebateDatas: Array<RebateUsed>;
    //产品返利
    productRuleId: string;
    productRebateDatas: Array<ProductUsed>
    //有范围金额返利
    rangeRebateDatas: Array<{
        rangeRuleId: string;
        rangeRebates: Array<RebateUsed>;
    }>;
}
//分摊接口入参
export interface RebateMatchRequest extends RebateRequest, RequestData {
    change: boolean;   //是否修改字段重新分摊
}

//分摊接口返回数据
export interface RebateMatchResponse {
    masterData: MasterData,
    detailDataMap: {
        [prod_pkg_key: string]: DetailData
    };
    misc_content: MiscContent;
    couponChange: boolean;
    rebateChange: boolean;
    productRebateChange: boolean;
    needUpdateProduct: boolean;
    //前端补充数据：
    ruleId:string;
    productRuleId:string;
    productRebateData?:Array<Product|ProductUsed>;
}
export interface ResponseProductRebate {
    id: string;
    amount: string;
    quantity: string;
    fund_account_id: string;
    product: Array<ProductUsed>;
}
export interface MiscContent {
    rebate: Array<RebateUsed>;
    coupon: Array<CouponUsed>;
    rebate_amount: string;
    coupon_amount: string;
    rangeRebate: Array<{
        rangeRuleId: string;
        rangeRebates: Array<RebateUsed>;
    }>;
    product_rebate: Array<ResponseProductRebate>
}


// 自动使用返利入参
export interface AutoUseRebateRequest extends FundAccount, Rule, RequestData {
    amount: number;
}

/****************************** 优惠券 ******************************/